/**
 * yk-common 业务编号生成模块 (ES Module版本)
 * 
 * 此模块包含完整的业务编号生成函数实现
 * 与原项目中的 src/utils/yk-common.ts 保持一致
 * 用于浏览器环境的ES模块导入
 */

/**
 * 业务类型枚举
 */
export const BusinessType = {
    APPLYMENT: 'APPLYMENT',     // 商户进件申请
    PAYMENT: 'PAYMENT',         // 支付订单
    REFUND: 'REFUND',           // 退款申请
    WITHDRAW: 'WITHDRAW',       // 提现申请
    TRANSFER: 'TRANSFER',       // 转账申请
    SETTLEMENT: 'SETTLEMENT',   // 结算申请
};

/**
 * 商户类型枚举
 */
export const MerchantType = {
    ENTERPRISE: 0,    // 企业
    INDIVIDUAL: 1,    // 个体工商户
    PERSONAL: 2       // 个人
};

/**
 * 生成业务请求唯一标识
 * 格式：{业务类型}_{时间戳}_{商户类型}_{用户ID}_{自定义后缀}_{6位随机数}
 * 示例：APPLYMENT_20250630173311_ENT_12345_123456
 *
 * @param {string} businessType 业务类型
 * @param {number} userId 用户ID
 * @param {number} merchantType 商户类型（可选，某些业务可能不需要）
 * @param {string} customSuffix 自定义后缀（可选，用于特殊业务场景）
 * @returns {string} 生成的唯一业务编号
 */
export function generateBusinessRequestNo(businessType, userId, merchantType, customSuffix) {
    // 获取当前时间戳 (YYYYMMDDHHMMSS)
    const now = new Date();
    const timestamp = now.getFullYear().toString() +
        (now.getMonth() + 1).toString().padStart(2, '0') +
        now.getDate().toString().padStart(2, '0') +
        now.getHours().toString().padStart(2, '0') +
        now.getMinutes().toString().padStart(2, '0') +
        now.getSeconds().toString().padStart(2, '0');

    // 商户类型简码（如果提供）
    const typeCode = merchantType !== undefined ? {
        [MerchantType.ENTERPRISE]: 'ENT',    // 企业
        [MerchantType.INDIVIDUAL]: 'IND',    // 个体工商户
        [MerchantType.PERSONAL]: 'PER'       // 个人
    }[merchantType] || 'UNK' : '';

    // 生成6位随机数
    const randomNum = Math.floor(Math.random() * 900000) + 100000;

    // 组装完整的业务编号
    const parts = [businessType, timestamp];

    if (typeCode) {
        parts.push(typeCode);
    }

    parts.push(userId.toString());

    if (customSuffix) {
        parts.push(customSuffix);
    }

    parts.push(randomNum.toString());

    return parts.join('_');
}

/**
 * 生成商户进件申请唯一标识（向后兼容的便捷函数）
 * @param {number} merchantType 商户类型
 * @param {number} userId 用户ID
 * @returns {string} 生成的商户进件申请编号
 */
export function generateOutRequestNo(merchantType, userId) {
    return generateBusinessRequestNo(BusinessType.APPLYMENT, userId, merchantType);
}

/**
 * 获取商户类型简码
 * @param {number} merchantType 商户类型
 * @returns {string} 商户类型简码
 */
export function getMerchantTypeCode(merchantType) {
    const codes = {
        [MerchantType.ENTERPRISE]: 'ENT',
        [MerchantType.INDIVIDUAL]: 'IND',
        [MerchantType.PERSONAL]: 'PER'
    };
    return codes[merchantType] || 'UNK';
}

/**
 * 验证业务编号格式
 * @param {string} requestNo 业务编号
 * @returns {object} 验证结果
 */
export function validateBusinessRequestNo(requestNo) {
    if (!requestNo || typeof requestNo !== 'string') {
        return { valid: false, error: '编号不能为空且必须是字符串' };
    }

    const parts = requestNo.split('_');
    
    if (parts.length < 4) {
        return { valid: false, error: '编号格式不正确，部分数量不足' };
    }

    // 验证业务类型
    const businessType = parts[0];
    if (!Object.values(BusinessType).includes(businessType)) {
        return { valid: false, error: `无效的业务类型: ${businessType}` };
    }

    // 验证时间戳格式
    const timestamp = parts[1];
    if (!/^\d{14}$/.test(timestamp)) {
        return { valid: false, error: '时间戳格式不正确' };
    }

    // 验证随机数格式
    const randomNum = parts[parts.length - 1];
    if (!/^\d{6}$/.test(randomNum)) {
        return { valid: false, error: '随机数格式不正确' };
    }

    return { 
        valid: true, 
        parts: {
            businessType: parts[0],
            timestamp: parts[1],
            merchantType: parts.length > 4 ? parts[2] : null,
            userId: parts.length > 4 ? parts[3] : parts[2],
            customSuffix: parts.length > 5 ? parts[4] : null,
            randomNum: parts[parts.length - 1]
        }
    };
}
