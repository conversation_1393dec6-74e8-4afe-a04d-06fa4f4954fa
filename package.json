{"name": "taroApp", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "default", "typescript": true, "css": "Less", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch --env development", "dev:h5poll": "taro build --type h5 --watch --env development --mode development --force-polling --port 8822", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch"}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"], "author": "", "dependencies": {"@arco-design/mobile-react": "^2.34.0", "@arco-iconbox/react-yk-arco": "^0.0.3", "@babel/runtime": "^7.24.4", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@tarojs/components": "4.0.8", "@tarojs/helper": "4.0.8", "@tarojs/plugin-framework-react": "4.0.8", "@tarojs/plugin-platform-alipay": "4.0.8", "@tarojs/plugin-platform-h5": "4.0.8", "@tarojs/plugin-platform-harmony-hybrid": "4.0.8", "@tarojs/plugin-platform-jd": "4.0.8", "@tarojs/plugin-platform-qq": "4.0.8", "@tarojs/plugin-platform-swan": "4.0.8", "@tarojs/plugin-platform-tt": "4.0.8", "@tarojs/plugin-platform-weapp": "4.0.8", "@tarojs/react": "4.0.8", "@tarojs/runtime": "4.0.8", "@tarojs/shared": "4.0.8", "@tarojs/taro": "4.0.8", "emoji-mart": "^5.6.0", "html2canvas": "^1.4.1", "js-md5": "^0.8.3", "moment": "^2.30.1", "qrcode.react": "^4.2.0", "react": "^18.0.0", "react-dom": "^18.0.0", "recoil": "^0.7.7", "taroApp": "file:", "vconsole": "^3.15.1"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/preset-react": "^7.24.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "4.0.8", "@tarojs/taro-loader": "4.0.8", "@tarojs/webpack5-runner": "4.0.8", "@types/node": "^18", "@types/qrcode.react": "^1.0.5", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "babel-preset-taro": "4.0.8", "eslint": "^8.57.0", "eslint-config-taro": "4.0.8", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.4.0", "less": "^4.2.0", "postcss": "^8.4.38", "react-refresh": "^0.14.0", "stylelint": "^16.4.0", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.4.5", "webpack": "5.91.0"}}