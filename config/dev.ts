import type { UserConfigExport } from "@tarojs/cli";
import ReactRefreshWebpackPlugin from '@pmmmwh/react-refresh-webpack-plugin';

export default {
  logger: {
    quiet: false,
    stats: true,
  },
  mini: {
    webpackChain(chain) {
      // 开启热更新
      chain.plugin('ReactRefreshWebpackPlugin')
        .use(ReactRefreshWebpackPlugin);
      // 配置文件监听
      chain.watchOptions({
        ignored: /node_modules/,
        poll: 1000, // 每秒检查一次变动
        aggregateTimeout: 300, // 防抖 300ms
      });
    }
  },
  h5: {
    webpackChain(chain) {
      // 开启热更新
      chain.plugin('ReactRefreshWebpackPlugin')
        .use(ReactRefreshWebpackPlugin);
      // 配置文件监听
      chain.watchOptions({
        ignored: /node_modules/,
        poll: 1000, // 每秒检查一次变动
        aggregateTimeout: 300, // 防抖 300ms
      });
    },
    publicPath: "/",
    devServer: {
      port: 8822,
      host: "0.0.0.0",
      hot: true,
      liveReload: true,
      watchFiles: ['src/**/*'],
      proxy: {
        "/devApi": {
          // target: "http://***********:38080",
          target: "http://ppxc.fjpipixia.com",
          secure: false,
          changeOrigin: true,
          pathRewrite: {
            "^/devApi": "",
          },
        },
        "/ppxcdevApi": {
          target: "http://ppxc.fjpipixia.com",
          secure: false,
          changeOrigin: true,
          pathRewrite: {
            "^/ppxcdevApi": "",
          },
        },
        "/baiduApi": {
          target: "https://aip.baidubce.com",
          secure: false,
          changeOrigin: true,
          pathRewrite: {
            "^/baiduApi": ""
          },
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET,PUT,POST,DELETE,OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type'
          }
        }
      },
    },
  },
} satisfies UserConfigExport<"webpack5">;
