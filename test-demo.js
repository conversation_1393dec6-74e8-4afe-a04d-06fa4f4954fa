// yk-common 测试脚本 - 使用模块导入方式
const {
    BusinessType,
    MerchantType,
    generateBusinessRequestNo,
    generateOutRequestNo,
    getMerchantTypeCode,
    validateBusinessRequestNo
} = require('./yk-common');

console.log('🧪 yk-common 独立测试环境 (模块化版本)');
console.log('📍 位置: /mnt/d/source/gitlab/yk-common-standalone-test/');
console.log('🔒 完全独立于arcoxc项目');
console.log('📦 使用模块导入方式，代码结构更清晰\n');

console.log('1. 基础用法:');
const result1 = generateBusinessRequestNo(BusinessType.PAYMENT, 12345);
console.log('generateBusinessRequestNo(BusinessType.PAYMENT, 12345)');
console.log('结果:', result1);

console.log('\n2. 完整参数:');
const result2 = generateBusinessRequestNo(BusinessType.APPLYMENT, 67890, MerchantType.ENTERPRISE, 'BATCH01');
console.log('generateBusinessRequestNo(BusinessType.APPLYMENT, 67890, MerchantType.ENTERPRISE, "BATCH01")');
console.log('结果:', result2);

console.log('\n3. 商户进件申请:');
const result3 = generateOutRequestNo(MerchantType.INDIVIDUAL, 11111);
console.log('generateOutRequestNo(MerchantType.INDIVIDUAL, 11111)');
console.log('结果:', result3);

console.log('\n4. 商户类型简码获取:');
Object.entries(MerchantType).forEach(([name, value]) => {
    const code = getMerchantTypeCode(value);
    console.log(`${name} (${value}): ${code}`);
});

console.log('\n5. 编号格式验证:');
const validationTests = [result1, result2, result3, 'INVALID_FORMAT'];
validationTests.forEach((testNo, index) => {
    const validation = validateBusinessRequestNo(testNo);
    console.log(`测试${index + 1}: ${validation.valid ? '✅' : '❌'} ${testNo}`);
    if (!validation.valid) {
        console.log(`  错误: ${validation.error}`);
    } else {
        console.log(`  解析: 业务类型=${validation.parts.businessType}, 用户ID=${validation.parts.userId}`);
    }
});

console.log('\n6. 不同业务类型示例:');
Object.values(BusinessType).forEach(type => {
    const result = generateBusinessRequestNo(type, 99999);
    console.log(`${type}: ${result}`);
});

console.log('\n✨ 演示完成！');
console.log('\n💡 优势:');
console.log('  - 使用模块导入，代码结构更清晰');
console.log('  - 函数复用性更好，易于维护');
console.log('  - 新增了工具函数：getMerchantTypeCode, validateBusinessRequestNo');
console.log('  - 完整版本请查看 index.html');
