name: test
on: [push, pull_request]

jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: setup node
        uses: actions/setup-node@v2
        with:
          node-version: 16

      - name: install
        run: npm install

      - name: test and collect coverage
        run: npm run test
      
      - name: upload coverage to Codecov
        uses: codecov/codecov-action@v3
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
