@import "../../../style/mixin.less";

@size-map: @avatar-size-map;

.@{prefix}-avatar {
    position: relative;
    font-size: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .use-var(color, avatar-text-font-color);
    .use-var(background-color, avatar-background);

    &-mode-image {
        background-color: transparent !important;
    }

    &-default-overlap {
        .use-var(background-color, avatar-default-overlap-background);
    }

    &-shape-circle {
        .set-avatar-radius(50%);
    }

    &-shape-square {
        .set-avatar-radius(.08rem);
    }

    .set-avatar-size(length(@size-map));

    &-img {
        width: 100%;
        height: 100%;
    }

    &-decoration {
        position: absolute;
    }

    &-text {
        flex: 0 0 auto;
        font-weight: bold;
    }

    &-wrapper {
        display: inline-block;

        &-with-info {
            display: flex;
            align-items: center;
        }
    }

    &-info {
        display: flex;
        justify-content: center;
        flex-direction: column;
        .rem-with-rtl(margin-left, 16);
    }

    &-name {
        .use-var(color, avatar-name-color);
    }

    &-desc {
        .use-var(color, avatar-desc-color);
    }
}

.@{prefix}-avatar-group {
    .@{prefix}-avatar-wrapper-shape-circle {
        position: relative; // 为了让z-index属性生效

        &:first-child {
            margin-left: 0;
        }
    }

    .@{prefix}-avatar {
        border-style: solid;
        .use-var(border-color, avatar-group-border-color);
    }

    .set-avatar-group-size(length(@size-map));
}

.set-avatar-radius(@radius) {
    border-radius: @radius;
    .@{prefix}-avatar-img .image-content {
        border-radius: @radius;
    }
}

// 循环设置头像叠层的样式
.set-avatar-group-size(@index) when (@index > 0) {

    @size: extract(@size-map, @index);

    &-size-@{size} {
        .@{prefix}-avatar-wrapper {
            .use-var-with-rtl(margin-left, "avatar-group-@{size}-size-offset");
        }
        .@{prefix}-avatar {
            .use-var(border-width, "avatar-group-@{size}-size-border");
        }
    }

    .set-avatar-group-size(@index - 1);
}

// 循环设置头像的样式
.set-avatar-size(@index) when (@index > 0) {

    @size: extract(@size-map, @index);

    &-size-@{size} {
        .use-var(width, "avatar-@{size}-size");
        .use-var(height, "avatar-@{size}-size");
    }
    &-default-icon-size-@{size} {
        .use-var(font-size, "avatar-default-overlap-@{size}-size");
    }
    &-text-size-@{size} {
        .use-var(font-size, "avatar-@{size}-text-font-size");
    }

    // 有辅助信息的样式
    &-wrapper-with-info-size-@{size} {
        .use-var(height, "avatar-info-box-@{size}-size");
    }

    // 用户名样式
    &-name-size-@{size} {
        .use-var(font-size, "avatar-name-@{size}-font-size");
        .use-var(line-height, "avatar-name-@{size}-line-height");
    }

    // 用户描述样式
    &-desc-size-@{size} {
        .use-var(margin-top, "avatar-desc-@{size}-margin-top");
        .use-var(font-size, "avatar-desc-@{size}-font-size");
        .use-var(line-height, "avatar-desc-@{size}-line-height");
    }

    .set-avatar-size(@index - 1);
}

