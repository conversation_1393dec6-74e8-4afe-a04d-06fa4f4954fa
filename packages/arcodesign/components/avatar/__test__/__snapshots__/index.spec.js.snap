// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`avatar demo test avatar demo: auto-font-size.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="avatar-demo-box"
  >
    <div
      class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
    >
      <div
        class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
      >
        <span
          class="arco-avatar-text arco-avatar-text-size-small"
          style="transform: scale(1);"
        >
          Arco
        </span>
      </div>
    </div>
    <div
      class="arco-avatar-wrapper avatar-left-margin small circle arco-avatar-wrapper-shape-circle"
    >
      <div
        class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
        style="background-color: rgb(255, 125, 0);"
      >
        <span
          class="arco-avatar-text arco-avatar-text-size-small"
          style="transform: scale(1);"
        >
          ByteDancer
        </span>
      </div>
    </div>
    <div
      class="arco-avatar-wrapper avatar-left-margin small square arco-avatar-wrapper-shape-square"
    >
      <div
        class="arco-avatar arco-avatar-size-small small arco-avatar-shape-square square arco-text-avatar arco-avatar-mode-text"
        style="background-color: rgb(201, 205, 212);"
      >
        <span
          class="arco-avatar-text arco-avatar-text-size-small"
          style="transform: scale(1);"
        >
          Garth
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`avatar demo test avatar demo: avatar-with-info.md renders correctly 1`] = `
<DocumentFragment>
  <div
    style="margin: -16px 0px;"
  >
    <div
      class="arco-avatar-wrapper large circle arco-avatar-wrapper-shape-circle arco-avatar-wrapper-with-info arco-avatar-wrapper-with-info-size-large with-info"
    >
      <div
        class="arco-avatar arco-avatar-size-large large arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
      >
        <div
          class="arco-image all-border-box pc arco-avatar-img"
        >
          <div
            class="image-content image-bottom-overlap"
          >
            <div
              class="image-placeholder"
            />
          </div>
          <div
            class="image-container loading animate"
            style="transition-duration: 200ms;"
          />
        </div>
      </div>
      <div
        class="arco-avatar-info"
      >
        <div
          class="arco-avatar-name arco-avatar-name-size-large"
        >
          Username
        </div>
        <div
          class="arco-avatar-desc arco-avatar-desc-size-large"
        >
          Auxiliary information
        </div>
      </div>
    </div>
    <div
      class="avatar-divider"
    />
    <div
      class="arco-avatar-wrapper medium circle arco-avatar-wrapper-shape-circle arco-avatar-wrapper-with-info arco-avatar-wrapper-with-info-size-medium with-info"
    >
      <div
        class="arco-avatar arco-avatar-size-medium medium arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
      >
        <div
          class="arco-image all-border-box pc arco-avatar-img"
        >
          <div
            class="image-content image-bottom-overlap"
          >
            <div
              class="image-placeholder"
            />
          </div>
          <div
            class="image-container loading animate"
            style="transition-duration: 200ms;"
          />
        </div>
      </div>
      <div
        class="arco-avatar-info"
      >
        <div
          class="arco-avatar-name arco-avatar-name-size-medium"
        >
          Username
        </div>
        <div
          class="arco-avatar-desc arco-avatar-desc-size-medium"
        >
          Auxiliary information
        </div>
      </div>
    </div>
    <div
      class="avatar-divider"
    />
    <div
      class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle arco-avatar-wrapper-with-info arco-avatar-wrapper-with-info-size-small with-info"
    >
      <div
        class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
      >
        <div
          class="arco-image all-border-box pc arco-avatar-img"
        >
          <div
            class="image-content image-bottom-overlap"
          >
            <div
              class="image-placeholder"
            />
          </div>
          <div
            class="image-container loading animate"
            style="transition-duration: 200ms;"
          />
        </div>
      </div>
      <div
        class="arco-avatar-info"
      >
        <div
          class="arco-avatar-name arco-avatar-name-size-small"
        >
          Username
        </div>
        <div
          class="arco-avatar-desc arco-avatar-desc-size-small"
        >
          Auxiliary information
        </div>
      </div>
    </div>
    <div
      class="avatar-divider"
    />
    <div
      class="arco-avatar-wrapper smaller circle arco-avatar-wrapper-shape-circle arco-avatar-wrapper-with-info arco-avatar-wrapper-with-info-size-smaller with-info"
    >
      <div
        class="arco-avatar arco-avatar-size-smaller smaller arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
      >
        <div
          class="arco-image all-border-box pc arco-avatar-img"
        >
          <div
            class="image-content image-bottom-overlap"
          >
            <div
              class="image-placeholder"
            />
          </div>
          <div
            class="image-container loading animate"
            style="transition-duration: 200ms;"
          />
        </div>
      </div>
      <div
        class="arco-avatar-info"
      >
        <div
          class="arco-avatar-name arco-avatar-name-size-smaller"
        >
          Username
        </div>
        <div
          class="arco-avatar-desc arco-avatar-desc-size-smaller"
        >
          Auxiliary information
        </div>
      </div>
    </div>
    <div
      class="avatar-divider"
    />
    <div
      class="arco-avatar-wrapper ultra-small circle arco-avatar-wrapper-shape-circle arco-avatar-wrapper-with-info arco-avatar-wrapper-with-info-size-ultra-small with-info"
    >
      <div
        class="arco-avatar arco-avatar-size-ultra-small ultra-small arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
      >
        <div
          class="arco-image all-border-box pc arco-avatar-img"
        >
          <div
            class="image-content image-bottom-overlap"
          >
            <div
              class="image-placeholder"
            />
          </div>
          <div
            class="image-container loading animate"
            style="transition-duration: 200ms;"
          />
        </div>
      </div>
      <div
        class="arco-avatar-info"
      >
        <div
          class="arco-avatar-name arco-avatar-name-size-ultra-small"
        >
          Username
        </div>
        <div
          class="arco-avatar-desc arco-avatar-desc-size-ultra-small"
        >
          Auxiliary information
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`avatar demo test avatar demo: custom.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="avatar-demo-box"
  >
    <div
      class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
    >
      <div
        class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
      >
        <div
          class="arco-image all-border-box pc arco-avatar-img"
        >
          <div
            class="image-content image-bottom-overlap"
          >
            <div
              class="image-placeholder"
            />
          </div>
          <div
            class="image-container loading animate"
            style="transition-duration: 200ms;"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`avatar demo test avatar demo: custom-class.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="avatar-demo-box"
  >
    <div
      class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
    >
      <div
        class="arco-avatar avatar-1 arco-avatar-size-small small arco-avatar-shape-circle circle arco-avatar-default-overlap default-overlap"
      >
        <svg
          class="arco-icon arco-icon-user-fill arco-avatar-default arco-avatar-default-icon-size-small"
          fill="currentColor"
          height="1em"
          viewBox="0 0 20 20"
          width="1em"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.5 10.833c2.301 0 5 1.786 5 5v2.5c0 .46-.373.834-.833.834H3.333a.833.833 0 01-.833-.834v-2.5c0-3.211 2.699-5 5-5h5zM10 1.25a4.167 4.167 0 110 8.333 4.167 4.167 0 010-8.333z"
          />
        </svg>
      </div>
    </div>
    <div
      class="arco-avatar-wrapper avatar-left-margin small square arco-avatar-wrapper-shape-square"
    >
      <div
        class="arco-avatar avatar-2 arco-avatar-size-small small arco-avatar-shape-square square arco-avatar-default-overlap default-overlap"
      >
        <svg
          class="arco-icon arco-icon-user-fill arco-avatar-default arco-avatar-default-icon-size-small"
          fill="currentColor"
          height="1em"
          viewBox="0 0 20 20"
          width="1em"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.5 10.833c2.301 0 5 1.786 5 5v2.5c0 .46-.373.834-.833.834H3.333a.833.833 0 01-.833-.834v-2.5c0-3.211 2.699-5 5-5h5zM10 1.25a4.167 4.167 0 110 8.333 4.167 4.167 0 010-8.333z"
          />
        </svg>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`avatar demo test avatar demo: default.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="avatar-demo-box"
  >
    <div
      class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
    >
      <div
        class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-avatar-default-overlap default-overlap"
      >
        <svg
          class="arco-icon arco-icon-user-fill arco-avatar-default arco-avatar-default-icon-size-small"
          fill="currentColor"
          height="1em"
          viewBox="0 0 20 20"
          width="1em"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.5 10.833c2.301 0 5 1.786 5 5v2.5c0 .46-.373.834-.833.834H3.333a.833.833 0 01-.833-.834v-2.5c0-3.211 2.699-5 5-5h5zM10 1.25a4.167 4.167 0 110 8.333 4.167 4.167 0 010-8.333z"
          />
        </svg>
      </div>
    </div>
    <div
      class="arco-avatar-wrapper avatar-left-margin small square arco-avatar-wrapper-shape-square"
    >
      <div
        class="arco-avatar arco-avatar-size-small small arco-avatar-shape-square square arco-avatar-default-overlap default-overlap"
      >
        <svg
          class="arco-icon arco-icon-user-fill arco-avatar-default arco-avatar-default-icon-size-small"
          fill="currentColor"
          height="1em"
          viewBox="0 0 20 20"
          width="1em"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.5 10.833c2.301 0 5 1.786 5 5v2.5c0 .46-.373.834-.833.834H3.333a.833.833 0 01-.833-.834v-2.5c0-3.211 2.699-5 5-5h5zM10 1.25a4.167 4.167 0 110 8.333 4.167 4.167 0 010-8.333z"
          />
        </svg>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`avatar demo test avatar demo: icon-avatar.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="avatar-demo-box custom-icon"
  >
    <div
      class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
    >
      <div
        class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle"
      >
        <svg
          class="arco-icon arco-icon-user-fill custom-avatar-icon"
          fill="currentColor"
          height="1em"
          viewBox="0 0 20 20"
          width="1em"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.5 10.833c2.301 0 5 1.786 5 5v2.5c0 .46-.373.834-.833.834H3.333a.833.833 0 01-.833-.834v-2.5c0-3.211 2.699-5 5-5h5zM10 1.25a4.167 4.167 0 110 8.333 4.167 4.167 0 010-8.333z"
          />
        </svg>
        <div
          class="arco-avatar-decoration"
        >
          <img
            class="bottom-edit-icon"
            src="https://sf1-cdn-tos.toutiaostatic.com/obj/arco-mobile/_static_/avatar-edit.png"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`avatar demo test avatar demo: size.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="avatar-demo-box"
  >
    <div
      class="avatar-size-demo-box"
    >
      <div
        class="arco-avatar-wrapper large circle arco-avatar-wrapper-shape-circle"
      >
        <div
          class="arco-avatar arco-avatar-size-large large arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
        >
          <div
            class="arco-image all-border-box pc arco-avatar-img"
          >
            <div
              class="image-content image-bottom-overlap"
            >
              <div
                class="image-placeholder"
              />
            </div>
            <div
              class="image-container loading animate"
              style="transition-duration: 200ms;"
            />
          </div>
        </div>
      </div>
      <span
        class="size-text"
        style="margin-top: 4px;"
      >
        large
      </span>
    </div>
    <div
      class="avatar-size-demo-box avatar-left-margin"
    >
      <div
        class="arco-avatar-wrapper medium circle arco-avatar-wrapper-shape-circle"
      >
        <div
          class="arco-avatar arco-avatar-size-medium medium arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
        >
          <div
            class="arco-image all-border-box pc arco-avatar-img"
          >
            <div
              class="image-content image-bottom-overlap"
            >
              <div
                class="image-placeholder"
              />
            </div>
            <div
              class="image-container loading animate"
              style="transition-duration: 200ms;"
            />
          </div>
        </div>
      </div>
      <span
        class="size-text"
        style="margin-top: 8px;"
      >
        medium
      </span>
    </div>
    <div
      class="avatar-size-demo-box avatar-left-margin"
    >
      <div
        class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
      >
        <div
          class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
        >
          <div
            class="arco-image all-border-box pc arco-avatar-img"
          >
            <div
              class="image-content image-bottom-overlap"
            >
              <div
                class="image-placeholder"
              />
            </div>
            <div
              class="image-container loading animate"
              style="transition-duration: 200ms;"
            />
          </div>
        </div>
      </div>
      <span
        class="size-text"
        style="margin-top: 12px;"
      >
        small
      </span>
    </div>
    <div
      class="avatar-size-demo-box avatar-left-margin"
    >
      <div
        class="arco-avatar-wrapper smaller circle arco-avatar-wrapper-shape-circle"
      >
        <div
          class="arco-avatar arco-avatar-size-smaller smaller arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
        >
          <div
            class="arco-image all-border-box pc arco-avatar-img"
          >
            <div
              class="image-content image-bottom-overlap"
            >
              <div
                class="image-placeholder"
              />
            </div>
            <div
              class="image-container loading animate"
              style="transition-duration: 200ms;"
            />
          </div>
        </div>
      </div>
      <span
        class="size-text"
        style="margin-top: 16px;"
      >
        smaller
      </span>
    </div>
    <div
      class="avatar-size-demo-box avatar-left-margin"
    >
      <div
        class="arco-avatar-wrapper ultra-small circle arco-avatar-wrapper-shape-circle"
      >
        <div
          class="arco-avatar arco-avatar-size-ultra-small ultra-small arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
        >
          <div
            class="arco-image all-border-box pc arco-avatar-img"
          >
            <div
              class="image-content image-bottom-overlap"
            >
              <div
                class="image-placeholder"
              />
            </div>
            <div
              class="image-container loading animate"
              style="transition-duration: 200ms;"
            />
          </div>
        </div>
      </div>
      <span
        class="size-text"
        style="margin-top: 20px;"
      >
        ultra-small
      </span>
    </div>
  </div>
</DocumentFragment>
`;

exports[`avatar demo test avatar demo: text-avatar.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="avatar-demo-box"
  >
    <div
      class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
    >
      <div
        class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
      >
        <span
          class="arco-avatar-text arco-avatar-text-size-small"
          style="transform: scale(1);"
        >
          Nietzsche
        </span>
      </div>
    </div>
    <div
      class="arco-avatar-wrapper avatar-left-margin small circle arco-avatar-wrapper-shape-circle"
    >
      <div
        class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
        style="background-color: rgb(255, 125, 0);"
      >
        <span
          class="arco-avatar-text arco-avatar-text-size-small"
          style="transform: scale(1);"
        >
          Z
        </span>
      </div>
    </div>
    <div
      class="arco-avatar-wrapper avatar-left-margin small square arco-avatar-wrapper-shape-square"
    >
      <div
        class="arco-avatar arco-avatar-size-small small arco-avatar-shape-square square arco-text-avatar arco-avatar-mode-text"
        style="background-color: rgb(201, 205, 212);"
      >
        <span
          class="arco-avatar-text arco-avatar-text-size-small"
          style="transform: scale(1);"
        >
          T
        </span>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`avatar demo test avatar demo: text-avatar-size.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="avatar-demo-box"
  >
    <div
      class="arco-avatar-wrapper large circle arco-avatar-wrapper-shape-circle"
    >
      <div
        class="arco-avatar arco-avatar-size-large large arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
      >
        <span
          class="arco-avatar-text arco-avatar-text-size-large"
          style="transform: scale(1);"
        >
          Nietzsche
        </span>
      </div>
    </div>
    <div
      class="arco-avatar-wrapper avatar-left-margin medium circle arco-avatar-wrapper-shape-circle"
    >
      <div
        class="arco-avatar arco-avatar-size-medium medium arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
      >
        <span
          class="arco-avatar-text arco-avatar-text-size-medium"
          style="transform: scale(1);"
        >
          Nietzsche
        </span>
      </div>
    </div>
    <div
      class="arco-avatar-wrapper avatar-left-margin small circle arco-avatar-wrapper-shape-circle"
    >
      <div
        class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
      >
        <span
          class="arco-avatar-text arco-avatar-text-size-small"
          style="transform: scale(1);"
        >
          Nietzsche
        </span>
      </div>
    </div>
    <div
      class="arco-avatar-wrapper avatar-left-margin smaller circle arco-avatar-wrapper-shape-circle"
    >
      <div
        class="arco-avatar arco-avatar-size-smaller smaller arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
      >
        <span
          class="arco-avatar-text arco-avatar-text-size-smaller"
          style="transform: scale(1);"
        >
          Nietzsche
        </span>
      </div>
    </div>
    <div
      class="arco-avatar-wrapper avatar-left-margin ultra-small circle arco-avatar-wrapper-shape-circle"
    >
      <div
        class="arco-avatar arco-avatar-size-ultra-small ultra-small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
      >
        <span
          class="arco-avatar-text arco-avatar-text-size-ultra-small"
          style="transform: scale(1);"
        >
          Nietzsche
        </span>
      </div>
    </div>
  </div>
  <div
    class="avatar-group-demo"
  >
    <div
      class="demo-sub-title"
    >
      Stacking in middle size
    </div>
    <div
      class="arco-avatar-group arco-avatar-group-size-small group-small"
    >
      <div
        class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
        style="z-index: 5;"
      >
        <div
          class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
          style="background-color: rgb(123, 198, 22);"
        >
          <span
            class="arco-avatar-text arco-avatar-text-size-small"
            style="transform: scale(1);"
          >
            Nietzsche
          </span>
        </div>
      </div>
      <div
        class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
        style="z-index: 4;"
      >
        <div
          class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
          style="background-color: rgb(20, 201, 201);"
        >
          <span
            class="arco-avatar-text arco-avatar-text-size-small"
            style="transform: scale(1);"
          >
            M
          </span>
        </div>
      </div>
      <div
        class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
        style="z-index: 3;"
      >
        <div
          class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
          style="background-color: rgb(22, 140, 255);"
        >
          <span
            class="arco-avatar-text arco-avatar-text-size-small"
            style="transform: scale(1);"
          >
            X
          </span>
        </div>
      </div>
      <div
        class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
        style="z-index: 2;"
      >
        <div
          class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
          style="background-color: rgb(255, 125, 0);"
        >
          <span
            class="arco-avatar-text arco-avatar-text-size-small"
            style="transform: scale(1);"
          >
            Z
          </span>
        </div>
      </div>
      <div
        class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
        style="z-index: 1;"
      >
        <div
          class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
          style="background-color: rgb(255, 199, 46);"
        >
          <span
            class="arco-avatar-text arco-avatar-text-size-small"
            style="transform: scale(1);"
          >
            JD
          </span>
        </div>
      </div>
    </div>
  </div>
  <div
    class="avatar-group-demo"
  >
    <div
      class="demo-sub-title"
    >
      Stacking in small size
    </div>
    <div
      class="arco-avatar-group arco-avatar-group-size-ultra-small group-ultra-small"
    >
      <div
        class="arco-avatar-wrapper ultra-small circle arco-avatar-wrapper-shape-circle"
        style="z-index: 1;"
      >
        <div
          class="arco-avatar arco-avatar-size-ultra-small ultra-small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
          style="background-color: rgb(123, 198, 22);"
        >
          <span
            class="arco-avatar-text arco-avatar-text-size-ultra-small"
            style="transform: scale(1);"
          >
            T
          </span>
        </div>
      </div>
      <div
        class="arco-avatar-wrapper ultra-small circle arco-avatar-wrapper-shape-circle"
        style="z-index: 2;"
      >
        <div
          class="arco-avatar arco-avatar-size-ultra-small ultra-small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
          style="background-color: rgb(20, 201, 201);"
        >
          <span
            class="arco-avatar-text arco-avatar-text-size-ultra-small"
            style="transform: scale(1);"
          >
            M
          </span>
        </div>
      </div>
      <div
        class="arco-avatar-wrapper ultra-small circle arco-avatar-wrapper-shape-circle"
        style="z-index: 3;"
      >
        <div
          class="arco-avatar arco-avatar-size-ultra-small ultra-small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
          style="background-color: rgb(22, 140, 255);"
        >
          <span
            class="arco-avatar-text arco-avatar-text-size-ultra-small"
            style="transform: scale(1);"
          >
            X
          </span>
        </div>
      </div>
      <div
        class="arco-avatar-wrapper ultra-small circle arco-avatar-wrapper-shape-circle"
        style="z-index: 4;"
      >
        <div
          class="arco-avatar arco-avatar-size-ultra-small ultra-small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
          style="background-color: rgb(255, 125, 0);"
        >
          <span
            class="arco-avatar-text arco-avatar-text-size-ultra-small"
            style="transform: scale(1);"
          >
            Z
          </span>
        </div>
      </div>
      <div
        class="arco-avatar-wrapper ultra-small circle arco-avatar-wrapper-shape-circle"
        style="z-index: 5;"
      >
        <div
          class="arco-avatar arco-avatar-size-ultra-small ultra-small arco-avatar-shape-circle circle arco-text-avatar arco-avatar-mode-text"
          style="background-color: rgb(255, 199, 46);"
        >
          <span
            class="arco-avatar-text arco-avatar-text-size-ultra-small"
            style="transform: scale(1);"
          >
            JD
          </span>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
