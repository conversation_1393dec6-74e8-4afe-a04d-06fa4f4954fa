@import "../../../style/mixin.less";

@keyframes scale-on {

    0% {
        transform: scale(0);opacity: 0; }

    100% {
        transform: scale(1); opacity: 1; }
}

@keyframes scale-off {

    0% {
        transform: scale(1); opacity: 1; }

    100% {
        transform: scale(0); opacity: 0; }
}

.@{prefix}-scale-enter-active {
    animation: scale-on .4s cubic-bezier(.3, 1.3, .3, 1) forwards;
}

.@{prefix}-scale-exit-active {
    animation: scale-off .4s cubic-bezier(.3, 1.3, .3, 1) forwards;
}

.@{prefix}-badge {
    cursor: pointer;
    box-sizing: content-box;
    display: inline-block;
    z-index: 2;
    .use-var(color, badge-text-color);
    .use-var(background-color, badge-background-color);
    white-space: nowrap;
    .use-var(border-radius, badge-border-radius);
    .use-var(height, badge-text-width);
    .use-var(line-height, badge-text-width);
    .set-content-box-width-var(min-width, badge-text-width, badge-text-padding, badge-text-padding);
    .use-var(padding, badge-text-padding, 0);

    &-text {
        display: block;
        font-weight: 500;
        .set-font-size-var(badge-font-size);
    }

    &-dot {
        .use-var(width, badge-dot-width);
        .use-var(height, badge-dot-width);
        padding: 0;
        min-width: auto;
    }

    &-bordered {
        .use-var(border, badge-border-color, 1PX solid);
    }

    &-absolute {
        position: absolute;
        top: 0;
        .set-prop-with-rtl(left, 100%);
        .use-var-with-rtl(margin-left, badge-text-deviation);
        .use-var(margin-top, badge-text-deviation);
    }

    &-absolute&-dot {
        .use-var-with-rtl(margin-left, badge-dot-deviation);
        .use-var(margin-top, badge-dot-deviation);
    }
}

