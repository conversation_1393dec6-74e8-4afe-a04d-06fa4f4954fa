### 布局

# 分割线 Divider

划分内容的装饰线

======

> 属性/Props

|参数|描述|类型|默认值|
|----------|-------------|------|------|
|hairline|是否使用 0\.5px 线|boolean|true|
|content|文字|ReactNode|-|
|align|文字位置|"left" \| "center" \| "right"|'center'|
|width|分割线宽度（单边），仅在\`align\`为\`center\`时生效|ReactText|-|
|children|子元素，同 content，优先级低于 content|ReactNode|null|
|className|自定义类名|string|""|
|style|自定义样式|CSSProperties|{}|

> 引用/Refs

|参数|描述|类型|
|----------|-------------|------|
|dom|最外层 DOM 元素|HTMLDivElement|
