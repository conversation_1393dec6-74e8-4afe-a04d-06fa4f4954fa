### Layout

# Divider 

A partitioning line element

======

> Props

|Property|Description|Type|DefaultValue|
|----------|-------------|------|------|
|hairline|Whether to use hairline|boolean|true|
|content|Divider text|ReactNode|-|
|align|The position of text inside divider|"left" \| "center" \| "right"|'center'|
|width|The width of divider, only take effect when \`align\` is \`center\`\.|ReactText|-|
|children|Children element, same as \`content\`, with the priority lower than \`content\`|ReactNode|null|
|className|Custom classname|string|""|
|style|Custom stylesheet|CSSProperties|{}|

> Refs

|Property|Description|Type|
|----------|-------------|------|
|dom|The outer DOM element of the component|HTMLDivElement|
