{"description": "模态对话框，在浮层中显示，引导用户进行相关操作。默认做了防滚动穿透处理，如果弹层内容中需要滚动，则需将滚动容器传入`getScrollContainer`属性以在未滚动到顶部或底部时释放滚动。", "descriptionTags": {"en": "A modal dialog, displayed in a floating layer, guides the user to perform related operations. By default, anti-scroll penetration processing is performed. If scrolling is required in the content of the bullet layer, you need to pass the scroll container to `getScrollContainer` to release scrolling when it is not scrolled to the top or bottom.", "type": "反馈", "type_en": "FeedBack", "name": "对话框", "name_en": "Dialog"}, "displayName": "Dialog", "methods": [{"description": "打开确认框(含一个确认按钮)", "docblock": "打开确认框(含一个确认按钮)\n@desc {en} Open a confirmation dialog (with a confirmation button)\n@param config Configuration\n@returns", "descriptionTags": {"params": [{"name": "config", "description": "Configuration", "type": "AlertOptions & DialogProps", "optional": false}], "return": {"name": "", "description": "", "type": "{ close: () => void; update: (newConfig: AlertOptions & DialogProps) => void; }", "optional": false}, "desc": {"en": "Open a confirmation dialog (with a confirmation button)"}}, "modifiers": [], "name": "alert", "params": [{"description": null, "name": "config", "type": {"name": "AlertOptions"}}, {"description": null, "name": "context?", "type": {"name": "GlobalContextParams"}}], "returns": null}, {"description": "打开提示框(含一个确认按钮和一个取消按钮)", "docblock": "打开提示框(含一个确认按钮和一个取消按钮)\n@desc {en} Open a prompt dialog (with a confirm button and a cancel button)\n@param config Configuration\n@returns", "descriptionTags": {"params": [{"name": "config", "description": "Configuration", "type": "ConfirmOptions & AlertOptions", "optional": false}], "return": {"name": "", "description": "", "type": "{ close: () => void; update: (newConfig: ConfirmOptions & AlertOptions) => void; }", "optional": false}, "desc": {"en": "Open a prompt dialog (with a confirm button and a cancel button)"}}, "modifiers": [], "name": "confirm", "params": [{"description": null, "name": "config", "type": {"name": "ConfirmOptions"}}, {"description": null, "name": "context?", "type": {"name": "GlobalContextParams"}}], "returns": null}, {"description": "打开常规对话框", "docblock": "打开常规对话框\n@desc {en} Open the general dialog\n@param config Configuration\n@returns", "descriptionTags": {"params": [{"name": "config", "description": "Configuration", "type": "DialogProps", "optional": false}], "return": {"name": "", "description": "", "type": "{ close: () => void; update: (newConfig: DialogProps) => void; }", "optional": false}, "desc": {"en": "Open the general dialog"}}, "modifiers": [], "name": "open", "params": [{"description": null, "name": "config", "type": {"name": "Config"}}, {"description": null, "name": "context?", "type": {"name": "GlobalContextParams"}}], "returns": null}], "props": {"title": {"defaultValue": null, "description": "弹窗标题\n@en Dialog title", "name": "title", "tags": {"en": "Dialog title"}, "descWithTags": "弹窗标题", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dialog/index.tsx", "name": "DialogProps"}, "required": false, "type": {"name": "ReactNode"}}, "footer": {"defaultValue": {"value": "[]"}, "description": "底部按钮配置\n@en Bottom button configuration", "name": "footer", "tags": {"en": "Bottom button configuration", "default": "[]"}, "descWithTags": "底部按钮配置", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dialog/index.tsx", "name": "DialogProps"}, "required": false, "type": {"name": "FooterButtonOptions[]"}}, "footerType": {"defaultValue": {"value": "\"grid\""}, "description": "底部按钮样式，grid表示通栏样式，button表示按钮样式，platform=ios时有效\n@en Bottom button style, grid indicates the banner style, button indicates the button style, valid when platform = ios", "name": "footerType", "tags": {"en": "Bottom button style, grid indicates the banner style, button indicates the button style, valid when platform = ios", "default": "\"grid\""}, "descWithTags": "底部按钮样式，grid表示通栏样式，button表示按钮样式，platform=ios时有效", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dialog/index.tsx", "name": "DialogProps"}, "required": false, "type": {"name": "enum", "raw": "\"grid\" | \"button\"", "value": [{"value": "\"grid\""}, {"value": "\"button\""}]}}, "renderFooter": {"defaultValue": null, "description": "自定义渲染footer\n@en Custom rendering footer", "name": "renderFooter", "tags": {"en": "Custom rendering footer"}, "descWithTags": "自定义渲染footer", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dialog/index.tsx", "name": "DialogProps"}, "required": false, "type": {"name": "() => ReactNode"}}, "footerCollapseCount": {"defaultValue": {"value": "3"}, "description": "底部按钮大于等于多少个时竖向排布\n@en Vertical arrangement when the number of buttons at the bottom is greater than or equal to the set value", "name": "footerCollapseCount", "tags": {"en": "Vertical arrangement when the number of buttons at the bottom is greater than or equal to the set value", "default": "3"}, "descWithTags": "底部按钮大于等于多少个时竖向排布", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dialog/index.tsx", "name": "DialogProps"}, "required": false, "type": {"name": "number"}}, "titleAlign": {"defaultValue": {"value": "platform=android 时为\"left\"，否则为\"center\""}, "description": "标题对齐方向\n@en Title alignment\n@default_en \"left\" when platform=android, \"center\" otherwise", "name": "titleAlign", "tags": {"en": "Title alignment", "default": "platform=android 时为\"left\"，否则为\"center\"", "default_en": "\"left\" when platform=android, \"center\" otherwise"}, "descWithTags": "标题对齐方向", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dialog/index.tsx", "name": "DialogProps"}, "required": false, "type": {"name": "enum", "raw": "\"left\" | \"center\" | \"right\"", "value": [{"value": "\"left\""}, {"value": "\"center\""}, {"value": "\"right\""}]}}, "contentAlign": {"defaultValue": {"value": "platform=android 时为\"left\"，否则为\"center\""}, "description": "内容对齐方向\n@en Content alignment\n@default_en \"left\" when platform=android, \"center\" otherwise", "name": "contentAlign", "tags": {"en": "Content alignment", "default": "platform=android 时为\"left\"，否则为\"center\"", "default_en": "\"left\" when platform=android, \"center\" otherwise"}, "descWithTags": "内容对齐方向", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dialog/index.tsx", "name": "DialogProps"}, "required": false, "type": {"name": "enum", "raw": "\"left\" | \"center\" | \"right\"", "value": [{"value": "\"left\""}, {"value": "\"center\""}, {"value": "\"right\""}]}}, "platform": {"defaultValue": {"value": "跟随当前所在系统"}, "description": "当前所在操作系统，对应不同样式\n@en The current operating system, corresponding to different styles\n@default_en Follow the system", "name": "platform", "tags": {"en": "The current operating system, corresponding to different styles", "default": "跟随当前所在系统", "default_en": "Follow the system"}, "descWithTags": "当前所在操作系统，对应不同样式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dialog/index.tsx", "name": "DialogProps"}, "required": false, "type": {"name": "enum", "raw": "\"ios\" | \"android\"", "value": [{"value": "\"ios\""}, {"value": "\"android\""}]}}, "extra": {"defaultValue": null, "description": "弹窗中的其他元素，比如关闭按钮等\n@en Other elements in the dialog, such as the close button, etc.", "name": "extra", "tags": {"en": "Other elements in the dialog, such as the close button, etc."}, "descWithTags": "弹窗中的其他元素，比如关闭按钮等", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dialog/index.tsx", "name": "DialogProps"}, "required": false, "type": {"name": "ReactNode"}}, "contentTransitionType": {"defaultValue": {"value": "platform=android 时为\"fade\"，否则为\"fade-scale\""}, "description": "内容过渡动画类名\n@en Content transition animation classname\n@default_en \"fade\" when platform=android, \"fade-scale\" otherwise", "name": "contentTransitionType", "tags": {"en": "Content transition animation classname", "default": "platform=android 时为\"fade\"，否则为\"fade-scale\"", "default_en": "\"fade\" when platform=android, \"fade-scale\" otherwise"}, "descWithTags": "内容过渡动画类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dialog/index.tsx", "name": "DialogProps"}, "required": false, "type": {"name": "string"}}, "maskTransitionTimeout": {"defaultValue": {"value": "300"}, "description": "蒙层动画时长\n@en Mask animation duration", "name": "maskTransitionTimeout", "tags": {"en": "Mask animation duration", "default": "300"}, "descWithTags": "蒙层动画时长", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dialog/index.tsx", "name": "DialogProps"}, "required": false, "type": {"name": "number | { appear?: number; enter?: number; exit?: number; }"}}, "contentTransitionTimeout": {"defaultValue": {"value": "450"}, "description": "弹窗内容动画时长\n@en Dialog content animation duration", "name": "contentTransitionTimeout", "tags": {"en": "Dialog content animation duration", "default": "450"}, "descWithTags": "弹窗内容动画时长", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dialog/index.tsx", "name": "DialogProps"}, "required": false, "type": {"name": "number | { appear?: number; enter?: number; exit?: number; }"}}, "className": {"defaultValue": null, "description": "自定义类名\n@en Custom classname", "name": "className", "tags": {"en": "Custom classname"}, "descWithTags": "自定义类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "string"}}, "maskClass": {"defaultValue": null, "description": "自定义蒙层类名\n@en Custom mask classname", "name": "maskClass", "tags": {"en": "Custom mask classname"}, "descWithTags": "自定义蒙层类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "string"}}, "maskStyle": {"defaultValue": null, "description": "自定义蒙层样式\n@en Custom mask stylesheet", "name": "maskStyle", "tags": {"en": "Custom mask stylesheet"}, "descWithTags": "自定义蒙层样式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "CSSProperties"}}, "contentClass": {"defaultValue": null, "description": "自定义内容类名\n@en Custom content classname", "name": "contentClass", "tags": {"en": "Custom content classname"}, "descWithTags": "自定义内容类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "string"}}, "contentStyle": {"defaultValue": null, "description": "自定义内容样式\n@en Custom content stylesheet", "name": "contentStyle", "tags": {"en": "Custom content stylesheet"}, "descWithTags": "自定义内容样式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "CSSProperties"}}, "visible": {"defaultValue": null, "description": "是否展示菜单（受控）\n@en Whether to display the menu (controlled)", "name": "visible", "tags": {"en": "Whether to display the menu (controlled)"}, "descWithTags": "是否展示菜单（受控）", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": true, "type": {"name": "boolean"}}, "close": {"defaultValue": null, "description": "关闭菜单方法\n@en Close menu method", "name": "close", "tags": {"en": "Close menu method"}, "descWithTags": "关闭菜单方法", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": true, "type": {"name": "(e: MouseEvent<HTMLElement, MouseEvent>) => void"}}, "maskTransitionType": {"defaultValue": {"value": "\"fade\""}, "description": "蒙层过渡动画类名\n@en Mask transition animation classname", "name": "maskTransitionType", "tags": {"en": "Mask transition animation classname", "default": "\"fade\""}, "descWithTags": "蒙层过渡动画类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "string"}}, "children": {"defaultValue": null, "description": "菜单内部内容\n@en Contents of menu", "name": "children", "tags": {"en": "Contents of menu"}, "descWithTags": "菜单内部内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "ReactNode"}}, "maskClosable": {"defaultValue": {"value": "true"}, "description": "点击蒙层是否关闭菜单\n@en Whether to click the mask to close the menu", "name": "maskClosable", "tags": {"en": "Whether to click the mask to close the menu", "default": "true"}, "descWithTags": "点击蒙层是否关闭菜单", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "boolean"}}, "animatingClosable": {"defaultValue": {"value": "false"}, "description": "执行进场动画时点击蒙层是否可关闭菜单\n@en Whether the menu can be closed by clicking on the mask when performing the entry animation", "name": "animatingClosable", "tags": {"en": "Whether the menu can be closed by clicking on the mask when performing the entry animation", "default": "false"}, "descWithTags": "执行进场动画时点击蒙层是否可关闭菜单", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "boolean"}}, "mountOnEnter": {"defaultValue": {"value": "true"}, "description": "是否在打开菜单时再加载内容\n@en Whether to reload content when the menu is opened", "name": "mountOnEnter", "tags": {"en": "Whether to reload content when the menu is opened", "default": "true"}, "descWithTags": "是否在打开菜单时再加载内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "boolean"}}, "unmountOnExit": {"defaultValue": {"value": "true"}, "description": "是否在退出时卸载内容\n@en Whether to unmount content on exit", "name": "unmountOnExit", "tags": {"en": "Whether to unmount content on exit", "default": "true"}, "descWithTags": "是否在退出时卸载内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "boolean"}}, "orientationDirection": {"defaultValue": {"value": "\"top\""}, "description": "transform方向，用于通过transform模拟横屏的情况\n@en The transform direction, used to simulate the situation of horizontal screen through transform", "name": "orientationDirection", "tags": {"en": "The transform direction, used to simulate the situation of horizontal screen through transform", "default": "\"top\""}, "descWithTags": "transform方向，用于通过transform模拟横屏的情况", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "enum", "raw": "\"left\" | \"right\" | \"top\" | \"bottom\"", "value": [{"value": "\"left\""}, {"value": "\"right\""}, {"value": "\"top\""}, {"value": "\"bottom\""}]}}, "preventBodyScroll": {"defaultValue": {"value": "true"}, "description": "弹窗打开时是否禁止body的滚动\n@en Whether to prohibit the scrolling of the body when the popup is opened", "name": "preventBodyScroll", "tags": {"en": "Whether to prohibit the scrolling of the body when the popup is opened", "default": "true"}, "descWithTags": "弹窗打开时是否禁止body的滚动", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "boolean"}}, "initialBodyOverflow": {"defaultValue": {"value": "第一个全屏组件（弹窗、toast等）打开时页面overflow值"}, "description": "页面初始 overflow 状态，即关闭弹窗时 overflow 应该还原的状态\n@en The initial overflow state of the page, that is, the state in which overflow should be restored when the popup is closed\n@default_en The page overflow value when the first fullscreen component (popup, toast, etc.) is opened", "name": "initialBodyOverflow", "tags": {"en": "The initial overflow state of the page, that is, the state in which overflow should be restored when the popup is closed", "default": "第一个全屏组件（弹窗、toast等）打开时页面overflow值", "default_en": "The page overflow value when the first fullscreen component (popup, toast, etc.) is opened"}, "descWithTags": "页面初始 overflow 状态，即关闭弹窗时 overflow 应该还原的状态", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "string"}}, "gestureOutOfControl": {"defaultValue": {"value": "false"}, "description": "是否禁用滚动容器手势判断，禁用后交给业务方自己判断\n@en Whether to disable the scrolling container gesture judgment, leave it to users to judge", "name": "gestureOutOfControl", "tags": {"en": "Whether to disable the scrolling container gesture judgment, leave it to users to judge", "default": "false"}, "descWithTags": "是否禁用滚动容器手势判断，禁用后交给业务方自己判断", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "boolean"}}, "onClose": {"defaultValue": null, "description": "关闭后回调（动画执行完毕）\n@en Callback after closing (animation is completed)", "name": "onClose", "tags": {"en": "Callback after closing (animation is completed)"}, "descWithTags": "关闭后回调（动画执行完毕）", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "(scene?: string) => void"}}, "onOpen": {"defaultValue": null, "description": "打开后回调（动画执行完毕）\n@en Callback after opening (animation is completed)", "name": "onOpen", "tags": {"en": "Callback after opening (animation is completed)"}, "descWithTags": "打开后回调（动画执行完毕）", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "() => void"}}, "onMaskClick": {"defaultValue": null, "description": "点击蒙层回调，maskClosable=false时也会触发\n@en Callback when clicking the mask , also triggered when maskClosable=false", "name": "onMaskClick", "tags": {"en": "Callback when clicking the mask , also triggered when maskClosable=false"}, "descWithTags": "点击蒙层回调，maskClosable=false时也会触发", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "() => void"}}, "onTouchMove": {"defaultValue": null, "description": "弹窗的touchmove回调\n@en Touch event callbacks for masking", "name": "onTouchMove", "tags": {"en": "Touch event callbacks for masking"}, "descWithTags": "弹窗的touchmove回调", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "(e: <PERSON><PERSON><PERSON>, prevented: boolean, direction: \"x\" | \"y\") => void"}}, "onPreventTouchMove": {"defaultValue": null, "description": "非滚动区域或滚动到顶部及底部时的触摸事件回调\n@en Touch event callbacks for non-scrolling areas or when scrolling to the top and bottom", "name": "onPreventTouchMove", "tags": {"en": "Touch event callbacks for non-scrolling areas or when scrolling to the top and bottom"}, "descWithTags": "非滚动区域或滚动到顶部及底部时的触摸事件回调", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "(e: <PERSON><PERSON><PERSON>, direction: \"x\" | \"y\") => void"}}, "getContainer": {"defaultValue": null, "description": "获取挂载容器\n@en Get mounted container", "name": "getContainer", "tags": {"en": "Get mounted container"}, "descWithTags": "获取挂载容器", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "() => HTMLElement"}}, "getScrollContainer": {"defaultValue": null, "description": "内容内部滚动区域容器，在该容器中未滚动到顶部或底部时会释放滚动\n@en Container of inner scroll area in content, scroll is released when not scrolled to the top or bottom in this container", "name": "getScrollContainer", "tags": {"en": "Container of inner scroll area in content, scroll is released when not scrolled to the top or bottom in this container"}, "descWithTags": "内容内部滚动区域容器，在该容器中未滚动到顶部或底部时会释放滚动", "parent": {"fileName": "arcom-github/packages/arcodesign/components/masking/index.tsx", "name": "MaskingCommonProps"}, "required": false, "type": {"name": "() => HTMLElement | HTMLElement[]"}}, "ref": {"defaultValue": null, "description": "", "name": "ref", "tags": {}, "descWithTags": "", "parent": {"fileName": "arcom-github/node_modules/@types/react/index.d.ts", "name": "RefAttributes"}, "required": false, "type": {"name": "Ref<DialogRef>"}}}, "deps": {"FooterButtonOptions": {"content": {"name": "content", "required": true, "description": "按钮内容\n@en Button content", "defaultValue": null, "type": {"name": "ReactNode | ((locale: ILocale) => ReactNode)"}, "tags": {"en": "Button content"}, "descWithTags": "按钮内容"}, "className": {"name": "className", "required": false, "description": "按钮类名\n@en Button classname", "defaultValue": null, "type": {"name": "string"}, "tags": {"en": "Button classname"}, "descWithTags": "按钮类名"}, "disabled": {"name": "disabled", "required": false, "description": "是否禁用按钮\n@en Whether to disable button", "defaultValue": null, "type": {"name": "boolean"}, "tags": {"en": "Whether to disable button"}, "descWithTags": "是否禁用按钮"}, "onClick": {"name": "onClick", "required": false, "description": "点击按钮事件，返回值为true时可以阻止dialog关闭\n@en Button clicking event, when the return value is true, the dialog can be prevented from closing", "defaultValue": null, "type": {"name": "(e: MouseEvent<HTMLElement, MouseEvent>) => boolean | void | Promise<boolean | void>"}, "tags": {"en": "Button clicking event, when the return value is true, the dialog can be prevented from closing"}, "descWithTags": "点击按钮事件，返回值为true时可以阻止dialog关闭"}}, "ILocale": {"locale": {"name": "locale", "required": true, "description": "语言类型\n@en Language Type", "defaultValue": null, "type": {"name": "string"}, "tags": {"en": "Language Type"}, "descWithTags": "语言类型"}, "LoadMore": {"name": "LoadMore", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadMoreText: string; loadingText: string; prepareText: string; noDataText: string; failLoadText: string; prepareScrollText: string; prepareClickText: string; }"}, "tags": {}, "descWithTags": ""}, "Picker": {"name": "Picker", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ okText: string; cancelText: string; }"}, "tags": {}, "descWithTags": ""}, "Tag": {"name": "Tag", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ addTag: string; }"}, "tags": {}, "descWithTags": ""}, "Dialog": {"name": "Dialog", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ okText: string; cancelText: string; }"}, "tags": {}, "descWithTags": ""}, "SwipeLoad": {"name": "SwipeLoad", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ normalText: string; activeText: string; }"}, "tags": {}, "descWithTags": ""}, "PullRefresh": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadingText: string; pullingText: string; finishText: string; loosingText: string; }"}, "tags": {}, "descWithTags": ""}, "DropdownMenu": {"name": "DropdownMenu", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ select: string; }"}, "tags": {}, "descWithTags": ""}, "Pagination": {"name": "Pagination", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ previousPage: string; nextPage: string; }"}, "tags": {}, "descWithTags": ""}, "Image": {"name": "Image", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadError: string; }"}, "tags": {}, "descWithTags": ""}, "ImagePicker": {"name": "ImagePicker", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadError: string; }"}, "tags": {}, "descWithTags": ""}, "SearchBar": {"name": "SearchBar", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ placeholder: string; cancelBtn: string; }"}, "tags": {}, "descWithTags": ""}, "Stepper": {"name": "Stepper", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ minusButtonName: string; addButtonName: string; }"}, "tags": {}, "descWithTags": ""}, "Keyboard": {"name": "Keyboard", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ confirm: string; }"}, "tags": {}, "descWithTags": ""}, "Form": {"name": "Form", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ required: string; type: { email: string; url: string; string: string; number: string; array: string; object: string; boolean: string; }; number: { min: string; max: string; equal: string; range: string; positive: string; negative: string; }; ... 4 more ...; pickerDefaultHint: string; }"}, "tags": {}, "descWithTags": ""}, "NavBar": {"name": "NavBar", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ backBtnAriaLabel: string; }"}, "tags": {}, "descWithTags": ""}}, "DialogRef": {"dom": {"name": "dom", "required": true, "description": "最外层元素 DOM\n@en The outermost element DOM", "defaultValue": null, "type": {"name": "HTMLDivElement"}, "tags": {"en": "The outermost element DOM"}, "descWithTags": "最外层元素 DOM"}, "mask": {"name": "mask", "required": true, "description": "蒙层 DOM\n@en Mask DOM", "defaultValue": null, "type": {"name": "HTMLDivElement"}, "tags": {"en": "Mask DOM"}, "descWithTags": "蒙层 DOM"}, "content": {"name": "content", "required": true, "description": "内容 DOM\n@en Content DOM", "defaultValue": null, "type": {"name": "HTMLDivElement"}, "tags": {"en": "Content DOM"}, "descWithTags": "内容 DOM"}, "setCloseScene": {"name": "setCloseScene", "required": true, "description": "在关闭弹窗前修改 onClose 的 scene 参数值\n@en Modify the scene of onClose before closing the popup", "defaultValue": null, "type": {"name": "(scene: string) => void"}, "tags": {"en": "Modify the scene of onClose before closing the popup"}, "descWithTags": "在关闭弹窗前修改 onClose 的 scene 参数值"}}, "AlertOptions": {"key": {"name": "key", "required": false, "description": "组件挂载容器id区分\n@en Component mount container id distinction", "defaultValue": null, "type": {"name": "string"}, "tags": {"en": "Component mount container id distinction"}, "descWithTags": "组件挂载容器id区分"}, "onOk": {"name": "onOk", "required": false, "description": "点击确认按钮回调\n@en Callback when clicking OK button", "defaultValue": null, "type": {"name": "(e: MouseEvent<HTMLElement, MouseEvent>) => boolean | void | Promise<boolean | void>"}, "tags": {"en": "Callback when clicking OK button"}, "descWithTags": "点击确认按钮回调"}, "okText": {"name": "okText", "required": false, "description": "确认按钮文字\n@en Ok button text", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "Ok button text"}, "descWithTags": "确认按钮文字"}}, "GlobalContextParams": {"prefixCls": {"name": "prefixCls", "required": false, "description": "组件类名前缀\n@en Component classname prefix", "defaultValue": {"value": "\"arco\""}, "type": {"name": "string"}, "tags": {"en": "Component classname prefix", "default": "\"arco\""}, "descWithTags": "组件类名前缀"}, "system": {"name": "system", "required": false, "description": "手动控制当前所在系统，传入后将直接使用传入的值，ssr场景需指定系统初始值时适用\n@en Manually control the current system, and the incoming value will be used directly after being passed in. It is applicable when the initial value of the system needs to be specified in the ssr scenario.", "defaultValue": {"value": "\"\""}, "type": {"name": "enum", "raw": "\"\" | \"ios\" | \"android\" | \"pc\"", "value": [{"value": "\"\""}, {"value": "\"ios\""}, {"value": "\"android\""}, {"value": "\"pc\""}]}, "tags": {"en": "Manually control the current system, and the incoming value will be used directly after being passed in. It is applicable when the initial value of the system needs to be specified in the ssr scenario.", "default": "\"\""}, "descWithTags": "手动控制当前所在系统，传入后将直接使用传入的值，ssr场景需指定系统初始值时适用"}, "useDarkMode": {"name": "useDarkMode", "required": false, "description": "是否监听系统原生的暗黑模式变化(prefers-color-scheme: dark)以判断是否切为暗黑模式\n@en Whether to monitor the system's native dark mode changes (prefers-color-scheme: dark) to determine whether to switch to dark mode", "defaultValue": {"value": "false"}, "type": {"name": "boolean"}, "tags": {"en": "Whether to monitor the system's native dark mode changes (prefers-color-scheme: dark) to determine whether to switch to dark mode", "default": "false"}, "descWithTags": "是否监听系统原生的暗黑模式变化(prefers-color-scheme: dark)以判断是否切为暗黑模式"}, "isDarkMode": {"name": "isDarkMode", "required": false, "description": "是否处于暗黑模式，指定后以指定的值为准\n@en Whether it is in dark mode, the value shall prevail after being specified", "defaultValue": {"value": "false"}, "type": {"name": "boolean"}, "tags": {"en": "Whether it is in dark mode, the value shall prevail after being specified", "default": "false"}, "descWithTags": "是否处于暗黑模式，指定后以指定的值为准"}, "darkModeSelector": {"name": "darkModeSelector", "required": false, "description": "当处于暗黑模式时，body上挂载的类名，为空值时不挂载类名\n@en When in dark mode, the class name mounted on the body, if it is empty, the class name will not be mounted", "defaultValue": {"value": "\"arco-theme-dark\""}, "type": {"name": "string"}, "tags": {"en": "When in dark mode, the class name mounted on the body, if it is empty, the class name will not be mounted", "default": "\"arco-theme-dark\""}, "descWithTags": "当处于暗黑模式时，body上挂载的类名，为空值时不挂载类名"}, "theme": {"name": "theme", "required": false, "description": "主题变量，传入后将在线替换css变量，需设置less变量 @use-css-vars: 1\n@en Theme variable. The css variable will be replaced online after input. The less variable needs to be set\n@use-css-vars : 1", "defaultValue": null, "type": {"name": "Record<string, string>"}, "tags": {"en": "Theme variable. The css variable will be replaced online after input. The less variable needs to be set", "use-css-vars": ": 1"}, "descWithTags": "主题变量，传入后将在线替换css变量，需设置less变量 @use-css-vars: 1"}, "locale": {"name": "locale", "required": false, "description": "国际化语言包配置\n@en Internationalized language configuration", "defaultValue": null, "type": {"name": "ILocale"}, "tags": {"en": "Internationalized language configuration"}, "descWithTags": "国际化语言包配置"}, "useRtl": {"name": "useRtl", "required": false, "description": "是否使用Rtl模式\n@en Whether to use rtl", "defaultValue": {"value": "false"}, "type": {"name": "boolean"}, "tags": {"en": "Whether to use rtl", "default": "false"}, "descWithTags": "是否使用Rtl模式"}, "onDarkModeChange": {"name": "onDarkModeChange", "required": false, "description": "当系统原生暗黑模式发生变化时触发，useDarkMode=true 时有效\n@en Triggered when the system's native dark mode changes, valid when useDarkMode=true", "defaultValue": null, "type": {"name": "(isDark: boolean) => void"}, "tags": {"en": "Triggered when the system's native dark mode changes, valid when useDarkMode=true"}, "descWithTags": "当系统原生暗黑模式发生变化时触发，useDarkMode=true 时有效"}}, "ConfirmOptions": {"onCancel": {"name": "onCancel", "required": false, "description": "点击取消按钮回调\n@en Callback when clicking cancel button", "defaultValue": null, "type": {"name": "(e: MouseEvent<HTMLElement, MouseEvent>) => boolean | void | Promise<boolean | void>"}, "tags": {"en": "Callback when clicking cancel button"}, "descWithTags": "点击取消按钮回调"}, "cancelText": {"name": "cancelText", "required": false, "description": "取消按钮文字\n@en Cancel button text", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "Cancel button text"}, "descWithTags": "取消按钮文字"}}}, "depComps": {}, "typeNameInfo": {"props": "DialogProps", "ref": "DialogRef"}, "isDefaultExport": true}