{"description": "全局数据控制组件，用于替换全局数据。", "descriptionTags": {"en": "Global data control component, used to replace global data.", "type": "其他", "type_en": "Others", "name": "全局配置", "name_en": "ContextProvider"}, "displayName": "ContextProvider", "methods": [], "props": {"prefixCls": {"defaultValue": {"value": "\"arco\""}, "description": "组件类名前缀\n@en Component classname prefix", "name": "prefixCls", "tags": {"en": "Component classname prefix", "default": "\"arco\""}, "descWithTags": "组件类名前缀", "parent": {"fileName": "arcom-github/packages/arcodesign/components/context-provider/index.tsx", "name": "GlobalContextParams"}, "required": false, "type": {"name": "string"}}, "system": {"defaultValue": {"value": "\"\""}, "description": "手动控制当前所在系统，传入后将直接使用传入的值，ssr场景需指定系统初始值时适用\n@en Manually control the current system, and the incoming value will be used directly after being passed in. It is applicable when the initial value of the system needs to be specified in the ssr scenario.", "name": "system", "tags": {"en": "Manually control the current system, and the incoming value will be used directly after being passed in. It is applicable when the initial value of the system needs to be specified in the ssr scenario.", "default": "\"\""}, "descWithTags": "手动控制当前所在系统，传入后将直接使用传入的值，ssr场景需指定系统初始值时适用", "parent": {"fileName": "arcom-github/packages/arcodesign/components/context-provider/index.tsx", "name": "GlobalContextParams"}, "required": false, "type": {"name": "enum", "raw": "\"\" | \"pc\" | \"android\" | \"ios\"", "value": [{"value": "\"\""}, {"value": "\"pc\""}, {"value": "\"android\""}, {"value": "\"ios\""}]}}, "useDarkMode": {"defaultValue": {"value": "false"}, "description": "是否监听系统原生的暗黑模式变化(prefers-color-scheme: dark)以判断是否切为暗黑模式\n@en Whether to monitor the system's native dark mode changes (prefers-color-scheme: dark) to determine whether to switch to dark mode", "name": "useDarkMode", "tags": {"en": "Whether to monitor the system's native dark mode changes (prefers-color-scheme: dark) to determine whether to switch to dark mode", "default": "false"}, "descWithTags": "是否监听系统原生的暗黑模式变化(prefers-color-scheme: dark)以判断是否切为暗黑模式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/context-provider/index.tsx", "name": "GlobalContextParams"}, "required": false, "type": {"name": "boolean"}}, "isDarkMode": {"defaultValue": {"value": "false"}, "description": "是否处于暗黑模式，指定后以指定的值为准\n@en Whether it is in dark mode, the value shall prevail after being specified", "name": "isDarkMode", "tags": {"en": "Whether it is in dark mode, the value shall prevail after being specified", "default": "false"}, "descWithTags": "是否处于暗黑模式，指定后以指定的值为准", "parent": {"fileName": "arcom-github/packages/arcodesign/components/context-provider/index.tsx", "name": "GlobalContextParams"}, "required": false, "type": {"name": "boolean"}}, "darkModeSelector": {"defaultValue": {"value": "\"arco-theme-dark\""}, "description": "当处于暗黑模式时，body上挂载的类名，为空值时不挂载类名\n@en When in dark mode, the class name mounted on the body, if it is empty, the class name will not be mounted", "name": "darkModeSelector", "tags": {"en": "When in dark mode, the class name mounted on the body, if it is empty, the class name will not be mounted", "default": "\"arco-theme-dark\""}, "descWithTags": "当处于暗黑模式时，body上挂载的类名，为空值时不挂载类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/context-provider/index.tsx", "name": "GlobalContextParams"}, "required": false, "type": {"name": "string"}}, "theme": {"defaultValue": null, "description": "主题变量，传入后将在线替换css变量，需设置less变量 @use-css-vars: 1\n@en Theme variable. The css variable will be replaced online after input. The less variable needs to be set\n@use-css-vars : 1", "name": "theme", "tags": {"en": "Theme variable. The css variable will be replaced online after input. The less variable needs to be set", "use-css-vars": ": 1"}, "descWithTags": "主题变量，传入后将在线替换css变量，需设置less变量 @use-css-vars: 1", "parent": {"fileName": "arcom-github/packages/arcodesign/components/context-provider/index.tsx", "name": "GlobalContextParams"}, "required": false, "type": {"name": "Record<string, string>"}}, "locale": {"defaultValue": null, "description": "国际化语言包配置\n@en Internationalized language configuration", "name": "locale", "tags": {"en": "Internationalized language configuration"}, "descWithTags": "国际化语言包配置", "parent": {"fileName": "arcom-github/packages/arcodesign/components/context-provider/index.tsx", "name": "GlobalContextParams"}, "required": false, "type": {"name": "ILocale"}}, "useRtl": {"defaultValue": {"value": "false"}, "description": "是否使用Rtl模式\n@en Whether to use rtl", "name": "useRtl", "tags": {"en": "Whether to use rtl", "default": "false"}, "descWithTags": "是否使用Rtl模式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/context-provider/index.tsx", "name": "GlobalContextParams"}, "required": false, "type": {"name": "boolean"}}, "onDarkModeChange": {"defaultValue": null, "description": "当系统原生暗黑模式发生变化时触发，useDarkMode=true 时有效\n@en Triggered when the system's native dark mode changes, valid when useDarkMode=true", "name": "onDarkModeChange", "tags": {"en": "Triggered when the system's native dark mode changes, valid when useDarkMode=true"}, "descWithTags": "当系统原生暗黑模式发生变化时触发，useDarkMode=true 时有效", "parent": {"fileName": "arcom-github/packages/arcodesign/components/context-provider/index.tsx", "name": "GlobalContextParams"}, "required": false, "type": {"name": "(isDark: boolean) => void"}}}, "deps": {"ILocale": {"locale": {"name": "locale", "required": true, "description": "语言类型\n@en Language Type", "defaultValue": null, "type": {"name": "string"}, "tags": {"en": "Language Type"}, "descWithTags": "语言类型"}, "LoadMore": {"name": "LoadMore", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadMoreText: string; loadingText: string; prepareText: string; noDataText: string; failLoadText: string; prepareScrollText: string; prepareClickText: string; }"}, "tags": {}, "descWithTags": ""}, "Picker": {"name": "Picker", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ okText: string; cancelText: string; }"}, "tags": {}, "descWithTags": ""}, "Tag": {"name": "Tag", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ addTag: string; }"}, "tags": {}, "descWithTags": ""}, "Dialog": {"name": "Dialog", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ okText: string; cancelText: string; }"}, "tags": {}, "descWithTags": ""}, "SwipeLoad": {"name": "SwipeLoad", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ normalText: string; activeText: string; }"}, "tags": {}, "descWithTags": ""}, "PullRefresh": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadingText: string; pullingText: string; finishText: string; loosingText: string; }"}, "tags": {}, "descWithTags": ""}, "DropdownMenu": {"name": "DropdownMenu", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ select: string; }"}, "tags": {}, "descWithTags": ""}, "Pagination": {"name": "Pagination", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ previousPage: string; nextPage: string; }"}, "tags": {}, "descWithTags": ""}, "Image": {"name": "Image", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadError: string; }"}, "tags": {}, "descWithTags": ""}, "ImagePicker": {"name": "ImagePicker", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ loadError: string; }"}, "tags": {}, "descWithTags": ""}, "SearchBar": {"name": "SearchBar", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ placeholder: string; cancelBtn: string; }"}, "tags": {}, "descWithTags": ""}, "Stepper": {"name": "Stepper", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ minusButtonName: string; addButtonName: string; }"}, "tags": {}, "descWithTags": ""}, "Keyboard": {"name": "Keyboard", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ confirm: string; }"}, "tags": {}, "descWithTags": ""}, "Form": {"name": "Form", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ required: string; type: { email: string; url: string; string: string; number: string; array: string; object: string; boolean: string; }; number: { min: string; max: string; equal: string; range: string; positive: string; negative: string; }; ... 4 more ...; pickerDefaultHint: string; }"}, "tags": {}, "descWithTags": ""}, "NavBar": {"name": "NavBar", "required": true, "description": "", "defaultValue": null, "type": {"name": "{ backBtnAriaLabel: string; }"}, "tags": {}, "descWithTags": ""}}}, "depComps": {}, "typeNameInfo": {"props": "ContextProviderProps", "ref": ""}, "isDefaultExport": true}