// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`form demo test form demo: index.md renders correctly 1`] = `
<DocumentFragment>
  <div>
    <div
      class="arco-radio-group"
    >
      <div
        aria-checked="true"
        aria-disabled="false"
        class="arco-radio shape-circle inline"
        role="checkbox"
        tabindex="0"
      >
        <div
          class="radio-icon checked"
        >
          <svg
            class="arco-icon arco-icon-circle-checked "
            height="1em"
            viewBox="0 0 20 20"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M10 1c-5 0-9 4-9 9s4 9 9 9 9-4 9-9-4-9-9-9zm4.9 6.3L10 13.2s0 .1-.1.1l-.6.7c-.1.1-.2.1-.3.2-.1 0-.3 0-.4-.1l-.6-.7-.1-.1-2.8-2.4c-.2-.2-.2-.5-.1-.7l.6-.7c.2-.2.5-.2.7-.1l2.5 2.1L13.5 6c.2-.2.5-.2.7-.1l.7.6c.******* 0 .8z"
              fill="currentColor"
              fill-rule="evenodd"
            />
          </svg>
        </div>
        <div
          class="radio-text"
        >
          horizontal
        </div>
      </div>
      <div
        aria-checked="false"
        aria-disabled="false"
        class="arco-radio shape-circle inline"
        role="checkbox"
        tabindex="0"
      >
        <div
          class="radio-icon"
        >
          <svg
            class="arco-icon arco-icon-circle-unchecked "
            height="1em"
            viewBox="0 0 20 20"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10 19c-5 0-9-4-9-9s4-9 9-9 9 4 9 9-4 9-9 9zm0-17c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z"
              fill="currentColor"
            />
          </svg>
        </div>
        <div
          class="radio-text"
        >
          vertical
        </div>
      </div>
    </div>
    <form
      class="arco-form "
    >
      <div
        class="arco-form-item arco-form-item-horizontal arco-form-item-warning"
      >
        <div
          class="arco-form-label-item"
        >
          <span
            class="arco-form-label-item-required-asterisk"
          >
            *
          </span>
          UserName
        </div>
        <div
          class="arco-form-item-control-wrapper"
        >
          <div
            class="arco-form-item-control"
          >
            <div
              class="arco-input-container all-border-box "
              role="search"
            >
              <div
                class="arco-input-wrap text border-none pc"
              >
                <input
                  aria-label=""
                  class="arco-input"
                  placeholder="Please input username"
                  type="text"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="arco-form-item arco-form-item-horizontal arco-form-item-warning"
      >
        <div
          class="arco-form-label-item"
        >
          Age
        </div>
        <div
          class="arco-form-item-control-wrapper"
        >
          <div
            class="arco-form-item-control"
          >
            <div
              class="arco-input-container all-border-box "
              role="search"
            >
              <div
                class="arco-input-wrap text border-none pc"
              >
                <input
                  aria-label=""
                  class="arco-input"
                  placeholder="Please input age"
                  type="text"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="arco-form-item arco-form-item-horizontal arco-form-item-warning"
      >
        <div
          class="arco-form-label-item"
        >
          Gender
        </div>
        <div
          class="arco-form-item-control-wrapper"
        >
          <div
            class="arco-form-item-control"
          >
            <div
              class="arco-radio-group"
            >
              <div
                aria-checked="false"
                aria-disabled="false"
                class="arco-radio shape-circle inline"
                role="checkbox"
                tabindex="0"
              >
                <div
                  class="radio-icon"
                >
                  <svg
                    class="arco-icon arco-icon-circle-unchecked "
                    height="1em"
                    viewBox="0 0 20 20"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10 19c-5 0-9-4-9-9s4-9 9-9 9 4 9 9-4 9-9 9zm0-17c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div
                  class="radio-text"
                >
                  male
                </div>
              </div>
              <div
                aria-checked="false"
                aria-disabled="false"
                class="arco-radio shape-circle inline"
                role="checkbox"
                tabindex="0"
              >
                <div
                  class="radio-icon"
                >
                  <svg
                    class="arco-icon arco-icon-circle-unchecked "
                    height="1em"
                    viewBox="0 0 20 20"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10 19c-5 0-9-4-9-9s4-9 9-9 9 4 9 9-4 9-9 9zm0-17c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div
                  class="radio-text"
                >
                  female
                </div>
              </div>
              <div
                aria-checked="false"
                aria-disabled="false"
                class="arco-radio shape-circle inline"
                role="checkbox"
                tabindex="0"
              >
                <div
                  class="radio-icon"
                >
                  <svg
                    class="arco-icon arco-icon-circle-unchecked "
                    height="1em"
                    viewBox="0 0 20 20"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10 19c-5 0-9-4-9-9s4-9 9-9 9 4 9 9-4 9-9 9zm0-17c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div
                  class="radio-text"
                >
                  others
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="arco-form-item arco-form-item-horizontal arco-form-item-warning"
      >
        <div
          class="arco-form-label-item"
        >
          <span
            class="arco-form-label-item-required-asterisk"
          >
            *
          </span>
          Checkbox
        </div>
        <div
          class="arco-form-item-control-wrapper"
        >
          <div
            class="arco-form-item-control"
          >
            <div
              class="arco-checkbox-group"
            >
              <div
                aria-checked="false"
                aria-disabled="false"
                class="arco-checkbox shape-circle block"
                role="checkbox"
                style="height: 42px;"
                tabindex="0"
              >
                <div
                  class="checkbox-icon"
                >
                  <svg
                    class="arco-icon arco-icon-circle-unchecked "
                    height="1em"
                    viewBox="0 0 20 20"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10 19c-5 0-9-4-9-9s4-9 9-9 9 4 9 9-4 9-9 9zm0-17c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div
                  class="checkbox-text"
                >
                  Option content 1
                </div>
              </div>
              <div
                aria-checked="false"
                aria-disabled="false"
                class="arco-checkbox shape-circle block"
                role="checkbox"
                style="height: 42px;"
                tabindex="0"
              >
                <div
                  class="checkbox-icon"
                >
                  <svg
                    class="arco-icon arco-icon-circle-unchecked "
                    height="1em"
                    viewBox="0 0 20 20"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10 19c-5 0-9-4-9-9s4-9 9-9 9 4 9 9-4 9-9 9zm0-17c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div
                  class="checkbox-text"
                >
                  Option content 2
                </div>
              </div>
              <div
                aria-checked="false"
                aria-disabled="false"
                class="arco-checkbox shape-circle block"
                role="checkbox"
                style="height: 42px;"
                tabindex="0"
              >
                <div
                  class="checkbox-icon"
                >
                  <svg
                    class="arco-icon arco-icon-circle-unchecked "
                    height="1em"
                    viewBox="0 0 20 20"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10 19c-5 0-9-4-9-9s4-9 9-9 9 4 9 9-4 9-9 9zm0-17c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div
                  class="checkbox-text"
                >
                  Option content 3
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="arco-form-item arco-form-item-horizontal arco-form-item-warning"
      >
        <div
          class="arco-form-label-item"
        >
          Birthday
        </div>
        <div
          class="arco-form-item-control-wrapper"
        >
          <div
            class="arco-form-item-control"
          >
            <div
              class="arco-picker-linked-container"
            >
              <div
                class="arco-form-picker-link-container"
              >
                2015-12-10 
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="arco-form-item arco-form-item-horizontal arco-form-item-warning"
      >
        <div
          class="arco-form-label-item"
        >
          Location
        </div>
        <div
          class="arco-form-item-control-wrapper"
        >
          <div
            class="arco-form-item-control"
          >
            <div
              class="arco-picker-linked-container"
            >
              <div
                class="arco-form-picker-link-container"
              >
                <span
                  class="arco-form-picker-link-container-placeholder"
                >
                  请选择
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="arco-form-item arco-form-item-horizontal arco-form-item-warning"
      >
        <div
          class="arco-form-label-item"
        >
          Score
        </div>
        <div
          class="arco-form-item-control-wrapper"
        >
          <div
            class="arco-form-item-control"
          >
            <div
              aria-disabled="false"
              aria-valuemax="5"
              aria-valuemin="0"
              aria-valuenow="0"
              class="arco-rate"
              role="slider"
            >
              <div
                class="arco-rate-icon-wrap normal"
              >
                <svg
                  class="arco-icon arco-icon-star-fill arco-rate-icon normal"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 1024 1024"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs />
                  <path
                    d="M497.371 826.514L270.63 936.23c-18.286 7.314-40.229 0-47.543-18.286-3.657-3.657-7.315-10.972-3.657-18.286l25.6-248.686c0-10.971-3.658-18.285-7.315-29.257L69.486 435.2c-14.629-14.629-10.972-36.571 3.657-51.2 3.657-3.657 10.971-7.314 14.628-7.314l245.029-51.2c10.971-3.657 18.286-7.315 21.943-18.286l128-219.429C490.057 73.143 512 65.83 530.286 76.8c3.657 3.657 10.971 7.314 14.628 14.629l128 219.428c3.657 7.314 14.629 14.629 21.943 18.286l245.029 51.2c18.285 3.657 32.914 21.943 25.6 43.886 0 7.314-3.657 10.971-7.315 14.628L789.943 621.714c-7.314 7.315-10.972 18.286-7.314 29.257l25.6 248.686c3.657 18.286-10.972 36.572-32.915 40.229-7.314 0-14.628 0-18.285-3.657L530.286 826.514c-10.972-7.314-25.6-7.314-32.915 0z"
                  />
                </svg>
                <div
                  class="arco-rate-icon-click-half left"
                />
                <div
                  class="arco-rate-icon-click-half right"
                />
              </div>
              <div
                class="arco-rate-icon-wrap normal"
              >
                <svg
                  class="arco-icon arco-icon-star-fill arco-rate-icon normal"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 1024 1024"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs />
                  <path
                    d="M497.371 826.514L270.63 936.23c-18.286 7.314-40.229 0-47.543-18.286-3.657-3.657-7.315-10.972-3.657-18.286l25.6-248.686c0-10.971-3.658-18.285-7.315-29.257L69.486 435.2c-14.629-14.629-10.972-36.571 3.657-51.2 3.657-3.657 10.971-7.314 14.628-7.314l245.029-51.2c10.971-3.657 18.286-7.315 21.943-18.286l128-219.429C490.057 73.143 512 65.83 530.286 76.8c3.657 3.657 10.971 7.314 14.628 14.629l128 219.428c3.657 7.314 14.629 14.629 21.943 18.286l245.029 51.2c18.285 3.657 32.914 21.943 25.6 43.886 0 7.314-3.657 10.971-7.315 14.628L789.943 621.714c-7.314 7.315-10.972 18.286-7.314 29.257l25.6 248.686c3.657 18.286-10.972 36.572-32.915 40.229-7.314 0-14.628 0-18.285-3.657L530.286 826.514c-10.972-7.314-25.6-7.314-32.915 0z"
                  />
                </svg>
                <div
                  class="arco-rate-icon-click-half left"
                />
                <div
                  class="arco-rate-icon-click-half right"
                />
              </div>
              <div
                class="arco-rate-icon-wrap normal"
              >
                <svg
                  class="arco-icon arco-icon-star-fill arco-rate-icon normal"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 1024 1024"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs />
                  <path
                    d="M497.371 826.514L270.63 936.23c-18.286 7.314-40.229 0-47.543-18.286-3.657-3.657-7.315-10.972-3.657-18.286l25.6-248.686c0-10.971-3.658-18.285-7.315-29.257L69.486 435.2c-14.629-14.629-10.972-36.571 3.657-51.2 3.657-3.657 10.971-7.314 14.628-7.314l245.029-51.2c10.971-3.657 18.286-7.315 21.943-18.286l128-219.429C490.057 73.143 512 65.83 530.286 76.8c3.657 3.657 10.971 7.314 14.628 14.629l128 219.428c3.657 7.314 14.629 14.629 21.943 18.286l245.029 51.2c18.285 3.657 32.914 21.943 25.6 43.886 0 7.314-3.657 10.971-7.315 14.628L789.943 621.714c-7.314 7.315-10.972 18.286-7.314 29.257l25.6 248.686c3.657 18.286-10.972 36.572-32.915 40.229-7.314 0-14.628 0-18.285-3.657L530.286 826.514c-10.972-7.314-25.6-7.314-32.915 0z"
                  />
                </svg>
                <div
                  class="arco-rate-icon-click-half left"
                />
                <div
                  class="arco-rate-icon-click-half right"
                />
              </div>
              <div
                class="arco-rate-icon-wrap normal"
              >
                <svg
                  class="arco-icon arco-icon-star-fill arco-rate-icon normal"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 1024 1024"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs />
                  <path
                    d="M497.371 826.514L270.63 936.23c-18.286 7.314-40.229 0-47.543-18.286-3.657-3.657-7.315-10.972-3.657-18.286l25.6-248.686c0-10.971-3.658-18.285-7.315-29.257L69.486 435.2c-14.629-14.629-10.972-36.571 3.657-51.2 3.657-3.657 10.971-7.314 14.628-7.314l245.029-51.2c10.971-3.657 18.286-7.315 21.943-18.286l128-219.429C490.057 73.143 512 65.83 530.286 76.8c3.657 3.657 10.971 7.314 14.628 14.629l128 219.428c3.657 7.314 14.629 14.629 21.943 18.286l245.029 51.2c18.285 3.657 32.914 21.943 25.6 43.886 0 7.314-3.657 10.971-7.315 14.628L789.943 621.714c-7.314 7.315-10.972 18.286-7.314 29.257l25.6 248.686c3.657 18.286-10.972 36.572-32.915 40.229-7.314 0-14.628 0-18.285-3.657L530.286 826.514c-10.972-7.314-25.6-7.314-32.915 0z"
                  />
                </svg>
                <div
                  class="arco-rate-icon-click-half left"
                />
                <div
                  class="arco-rate-icon-click-half right"
                />
              </div>
              <div
                class="arco-rate-icon-wrap normal"
              >
                <svg
                  class="arco-icon arco-icon-star-fill arco-rate-icon normal"
                  fill="currentColor"
                  height="1em"
                  viewBox="0 0 1024 1024"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs />
                  <path
                    d="M497.371 826.514L270.63 936.23c-18.286 7.314-40.229 0-47.543-18.286-3.657-3.657-7.315-10.972-3.657-18.286l25.6-248.686c0-10.971-3.658-18.285-7.315-29.257L69.486 435.2c-14.629-14.629-10.972-36.571 3.657-51.2 3.657-3.657 10.971-7.314 14.628-7.314l245.029-51.2c10.971-3.657 18.286-7.315 21.943-18.286l128-219.429C490.057 73.143 512 65.83 530.286 76.8c3.657 3.657 10.971 7.314 14.628 14.629l128 219.428c3.657 7.314 14.629 14.629 21.943 18.286l245.029 51.2c18.285 3.657 32.914 21.943 25.6 43.886 0 7.314-3.657 10.971-7.315 14.628L789.943 621.714c-7.314 7.315-10.972 18.286-7.314 29.257l25.6 248.686c3.657 18.286-10.972 36.572-32.915 40.229-7.314 0-14.628 0-18.285-3.657L530.286 826.514c-10.972-7.314-25.6-7.314-32.915 0z"
                  />
                </svg>
                <div
                  class="arco-rate-icon-click-half left"
                />
                <div
                  class="arco-rate-icon-click-half right"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="arco-form-item arco-form-item-horizontal arco-form-item-warning"
      >
        <div
          class="arco-form-label-item"
        >
          Pictures
        </div>
        <div
          class="arco-form-item-control-wrapper"
        >
          <div
            class="arco-form-item-control"
          >
            <div
              class="arco-image-picker"
            >
              <div
                class="arco-image-picker-container"
              >
                <div
                  class="arco-grid"
                >
                  <div
                    class="arco-grid-rows"
                  >
                    <div
                      class="arco-grid-rows-item"
                      style="margin-bottom: 0px; margin-right: 8px;"
                    >
                      <div
                        class="arco-image-picker-image"
                      >
                        <div
                          class="arco-image-picker-image-container"
                        >
                          <div
                            class="arco-image all-border-box pc"
                          >
                            <div
                              class="image-content image-bottom-overlap"
                            >
                              <div
                                class="image-placeholder"
                              />
                            </div>
                            <div
                              class="image-container loading animate"
                              style="transition-duration: 200ms;"
                            />
                            <div
                              class="image-content image-loading-container"
                            >
                              <div
                                class="image-loading"
                              >
                                <div
                                  class="arco-loading all-border-box circle loading-icon"
                                  style="animation-duration: 1000ms; width: 20px; height: 20px;"
                                >
                                  <svg
                                    viewBox="0 0 20 20"
                                  >
                                    <defs>
                                      <lineargradient
                                        id="grad1-inner-0"
                                        x1="0%"
                                        x2="100%"
                                        y1="0%"
                                        y2="0%"
                                      >
                                        <stop
                                          class="loading-circle-middle stop-color-with-config"
                                          offset="0%"
                                        />
                                        <stop
                                          class="loading-circle-start stop-color-with-config"
                                          offset="100%"
                                        />
                                      </lineargradient>
                                      <lineargradient
                                        id="grad2-inner-0"
                                        x1="0%"
                                        x2="100%"
                                        y1="0%"
                                        y2="0%"
                                      >
                                        <stop
                                          class="loading-circle-middle stop-color-with-config"
                                          offset="0%"
                                        />
                                        <stop
                                          class="loading-circle-end stop-color-with-config"
                                          offset="100%"
                                        />
                                      </lineargradient>
                                    </defs>
                                    <circle
                                      cx="10"
                                      cy="10"
                                      fill="none"
                                      r="8.5"
                                      stroke="url(#grad1-inner-0)"
                                      stroke-dasharray="26.703537555513243"
                                      stroke-dashoffset="26.703537555513243"
                                      stroke-width="3"
                                    />
                                    <circle
                                      cx="10"
                                      cy="10"
                                      fill="none"
                                      r="8.5"
                                      stroke="url(#grad2-inner-0)"
                                      stroke-dasharray="26.703537555513243"
                                      stroke-width="3"
                                    />
                                    <circle
                                      class="loading-circle-filleted fill-color-with-config"
                                      cx="18.5"
                                      cy="10"
                                      r="1.5"
                                    />
                                  </svg>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            class="arco-image-picker-image-mask"
                          />
                        </div>
                        <div
                          class="arco-image-picker-close"
                        >
                          <div
                            class="arco-image-picker-close-icon"
                          >
                            <svg
                              class="arco-icon arco-icon-close "
                              height="1em"
                              viewBox="0 0 1024 1024"
                              width="1em"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M509.568 447.083L796.16 160.469a21.333 21.333 0 0130.165 0l30.166 30.166a21.333 21.333 0 010 30.186L569.899 507.435l286.613 286.592a21.333 21.333 0 010 30.165l-30.165 30.165a21.333 21.333 0 01-30.166 0L509.568 567.765 222.933 854.38a21.333 21.333 0 01-30.186 0l-30.166-30.166a21.333 21.333 0 010-30.165l286.614-286.613L162.603 220.8a21.333 21.333 0 010-30.187l30.165-30.165a21.333 21.333 0 0130.187 0l286.613 286.613z"
                                fill="currentColor"
                              />
                            </svg>
                          </div>
                        </div>
                      </div>
                      <i
                        class="vertical-border"
                      />
                    </div>
                    <div
                      class="arco-grid-rows-item"
                      style="margin-bottom: 0px; margin-right: 8px;"
                    >
                      <div
                        class="arco-image-picker-add"
                      >
                        <div
                          class="arco-image-picker-add-container"
                        >
                          <div
                            class="arco-image-picker-add-icon"
                          >
                            <svg
                              fill="none"
                              height="30"
                              viewBox="0 0 30 30"
                              width="30"
                            >
                              <path
                                clip-rule="evenodd"
                                d="M14.5 0C14.2239 0 14 0.223857 14 0.5V14H0.5C0.223858 14 0 14.2239 0 14.5V15.5C0 15.7761 0.223857 16 0.5 16H14V29.5C14 29.7761 14.2239 30 14.5 30H15.5C15.7761 30 16 29.7761 16 29.5V16H29.5C29.7761 16 30 15.7761 30 15.5V14.5C30 14.2239 29.7761 14 29.5 14H16V0.5C16 0.223858 15.7761 0 15.5 0H14.5Z"
                                fill="#C9CDD4"
                                fill-rule="evenodd"
                              />
                            </svg>
                          </div>
                          <input
                            accept="image/*"
                            type="file"
                          />
                        </div>
                      </div>
                      <i
                        class="vertical-border"
                      />
                    </div>
                    <div
                      class="arco-grid-rows-item"
                      style="margin-bottom: 0px; margin-right: 0px;"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="arco-form-item arco-form-item-horizontal arco-form-item-warning"
      >
        <div
          class="arco-form-label-item"
        >
          Progress
        </div>
        <div
          class="arco-form-item-control-wrapper"
        >
          <div
            class="arco-form-item-control"
          >
            <div
              aria-valuemax="100"
              aria-valuemin="0"
              aria-valuenow="0"
              class="arco-slider  is-horizontal show-tooltip"
              role="slider"
            >
              <div
                class="arco-slider-wrapper"
              >
                <div
                  class="arco-slider-marks"
                />
                <div
                  class="arco-slider-line"
                  style="height: 2px;"
                >
                  <div
                    class="arco-slider-line is-activated is-transferable"
                    style="height: 2px; left: 0px; width: 0px;"
                  >
                    <div
                      class="arco-slider-thumb-wrapper is-end"
                    >
                      <div
                        class="arco-slider-popover-wrapper"
                      >
                        <div
                          class="arco-slider-thumb"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        class="arco-button arco-button-type-primary type-primary arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc"
        type="button"
      >
        <div
          class="arco-button-text arco-button-text-pc btn-text"
        >
          Submit
        </div>
      </button>
    </form>
  </div>
</DocumentFragment>
`;

exports[`form demo test form demo: use-form.md renders correctly 1`] = `
<DocumentFragment>
  <div>
    <div
      class="arco-radio-group"
    >
      <div
        aria-checked="true"
        aria-disabled="false"
        class="arco-radio shape-circle inline"
        role="checkbox"
        tabindex="0"
      >
        <div
          class="radio-icon checked"
        >
          <svg
            class="arco-icon arco-icon-circle-checked "
            height="1em"
            viewBox="0 0 20 20"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clip-rule="evenodd"
              d="M10 1c-5 0-9 4-9 9s4 9 9 9 9-4 9-9-4-9-9-9zm4.9 6.3L10 13.2s0 .1-.1.1l-.6.7c-.1.1-.2.1-.3.2-.1 0-.3 0-.4-.1l-.6-.7-.1-.1-2.8-2.4c-.2-.2-.2-.5-.1-.7l.6-.7c.2-.2.5-.2.7-.1l2.5 2.1L13.5 6c.2-.2.5-.2.7-.1l.7.6c.******* 0 .8z"
              fill="currentColor"
              fill-rule="evenodd"
            />
          </svg>
        </div>
        <div
          class="radio-text"
        >
          horizontal
        </div>
      </div>
      <div
        aria-checked="false"
        aria-disabled="false"
        class="arco-radio shape-circle inline"
        role="checkbox"
        tabindex="0"
      >
        <div
          class="radio-icon"
        >
          <svg
            class="arco-icon arco-icon-circle-unchecked "
            height="1em"
            viewBox="0 0 20 20"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10 19c-5 0-9-4-9-9s4-9 9-9 9 4 9 9-4 9-9 9zm0-17c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z"
              fill="currentColor"
            />
          </svg>
        </div>
        <div
          class="radio-text"
        >
          vertical
        </div>
      </div>
    </div>
    <form
      class="arco-form "
    >
      <div
        class="arco-form-item arco-form-item-horizontal arco-form-item-warning"
      >
        <div
          class="arco-form-label-item"
        >
          <span
            class="arco-form-label-item-required-asterisk"
          >
            *
          </span>
          UserName
        </div>
        <div
          class="arco-form-item-control-wrapper"
        >
          <div
            class="arco-form-item-control"
          >
            <div
              class="arco-input-container all-border-box "
              role="search"
            >
              <div
                class="arco-input-wrap text border-none pc"
              >
                <input
                  aria-label=""
                  class="arco-input"
                  placeholder="Please input username"
                  type="text"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="arco-form-item arco-form-item-horizontal arco-form-item-warning"
      >
        <div
          class="arco-form-label-item"
        >
          Age
        </div>
        <div
          class="arco-form-item-control-wrapper"
        >
          <div
            class="arco-form-item-control"
          >
            <div
              class="arco-input-container all-border-box "
              role="search"
            >
              <div
                class="arco-input-wrap text border-none pc"
              >
                <input
                  aria-label=""
                  class="arco-input"
                  placeholder="Please input age"
                  type="text"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="arco-form-item arco-form-item-horizontal arco-form-item-warning"
      >
        <div
          class="arco-form-label-item"
        >
          Gender
        </div>
        <div
          class="arco-form-item-control-wrapper"
        >
          <div
            class="arco-form-item-control"
          >
            <div
              class="arco-radio-group"
            >
              <div
                aria-checked="false"
                aria-disabled="false"
                class="arco-radio shape-circle inline"
                role="checkbox"
                tabindex="0"
              >
                <div
                  class="radio-icon"
                >
                  <svg
                    class="arco-icon arco-icon-circle-unchecked "
                    height="1em"
                    viewBox="0 0 20 20"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10 19c-5 0-9-4-9-9s4-9 9-9 9 4 9 9-4 9-9 9zm0-17c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div
                  class="radio-text"
                >
                  male
                </div>
              </div>
              <div
                aria-checked="false"
                aria-disabled="false"
                class="arco-radio shape-circle inline"
                role="checkbox"
                tabindex="0"
              >
                <div
                  class="radio-icon"
                >
                  <svg
                    class="arco-icon arco-icon-circle-unchecked "
                    height="1em"
                    viewBox="0 0 20 20"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10 19c-5 0-9-4-9-9s4-9 9-9 9 4 9 9-4 9-9 9zm0-17c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div
                  class="radio-text"
                >
                  female
                </div>
              </div>
              <div
                aria-checked="false"
                aria-disabled="false"
                class="arco-radio shape-circle inline"
                role="checkbox"
                tabindex="0"
              >
                <div
                  class="radio-icon"
                >
                  <svg
                    class="arco-icon arco-icon-circle-unchecked "
                    height="1em"
                    viewBox="0 0 20 20"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10 19c-5 0-9-4-9-9s4-9 9-9 9 4 9 9-4 9-9 9zm0-17c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div
                  class="radio-text"
                >
                  others
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        class="arco-button arco-button-type-primary type-primary arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc"
        type="button"
      >
        <div
          class="arco-button-text arco-button-text-pc btn-text"
        >
          Submit
        </div>
      </button>
    </form>
  </div>
</DocumentFragment>
`;
