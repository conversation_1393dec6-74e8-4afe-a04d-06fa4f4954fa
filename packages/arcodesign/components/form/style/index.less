@import '../../../style/mixin.less';

.@{prefix}-form {
    &-item {
        display: flex;
        align-items: flex-start;
        border-top-width: 1px;
        border-top-style: solid;
        .use-var(border-top-color, form-item-border-divider-color);

        &:first-child {
            border: none;
        }
    }

    &-label-item {
        display: flex;
        position: relative;
        align-items: center;
        justify-content: flex-start;
        .use-var(font-size, form-item-label-item-font-size);
        .use-var(line-height, form-item-label-item-line-height);
        .use-var(color, form-item-label-item-color);
        .use-var-with-rtl(padding-right, form-item-label-item-gutter);
        .use-var(width, form-item-label-item-width);

        &-required-asterisk {
            position: absolute;
            .set-prop-with-rtl(left, -0.6em);
            top: 0;

            .use-var(font-size, form-item-label-item-font-size);
            .use-var(line-height, form-item-label-item-line-height);
            .use-var(color, form-item-label-item-required-asterisk-color);
        }
        .disabled & {
            .use-var(color, input-disabled-color);
        }
    }
    &-item-control {
        padding: 16px 0;
        .@{prefix}-input-wrap, .@{prefix}-input {
            padding: 0;
        }
        &-wrapper {
            width: 100%;
            flex: 1;
            position: relative;
            .@{prefix}-input-wrap {
                .use-var(height, input-text-line-height);
            }
        }
    }
    &-item-message {
        .rem(font-size, 12);
        .use-var(color, form-item-label-item-required-asterisk-color);
        .rem(margin-top, -5);
        svg,
        span {
            vertical-align: middle;
        }

        svg {
            .rem-with-rtl(margin-right, 4);
        }
        margin-bottom: 16px;
    }
    &-item-error-message {
        .use-var(color, form-item-error-message-color);
    }

    &-item-warning-message {
        .use-var(color, form-item-warning-message-color);
    }

    &-item.@{prefix}-form-item-vertical {
        display: block;
        .@{prefix}-input-wrap {
            .set-prop-with-rtl(padding-left, 0);
        }
        .@{prefix}-form-label-item {
            width: auto;
            margin-top: 16px;
            .use-var(height, form-item-label-item-font-size);
            .use-var( line-height, form-item-label-item-font-size);
            &-required-asterisk {
                .use-var(height, form-item-label-item-font-size);
                .use-var( line-height, form-item-label-item-font-size);
            }
        }
    }

    &-picker-link-container {
        .rem(font-size, 16);
        .use-var(color, font-color);
        &-placeholder {
            .use-var(color, input-placeholder-color);
        }
    }
}
