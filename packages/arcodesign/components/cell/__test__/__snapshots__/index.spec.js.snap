// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`cell demo test cell demo: avatar.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-cell-group all-border-box"
  >
    <div
      class="cell-group-body bordered"
    >
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              <div
                class="demo-cell-avatar-label"
              >
                <div
                  class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
                >
                  <div
                    class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
                  >
                    <div
                      class="arco-image all-border-box pc arco-avatar-img"
                    >
                      <div
                        class="image-content image-bottom-overlap"
                      >
                        <div
                          class="image-placeholder"
                        />
                      </div>
                      <div
                        class="image-container loading animate"
                        style="transition-duration: 200ms;"
                      />
                    </div>
                  </div>
                </div>
                <span>
                  List Content
                </span>
              </div>
            </div>
          </div>
          <div
            class="cell-content has-label"
          />
        </div>
      </div>
      <div
        class="arco-cell all-border-box bordered demo-cell-avatar"
      >
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
          </div>
          <div
            class="cell-content has-label"
          >
            <div
              class="cell-text"
            >
              <div
                class="arco-avatar-wrapper small circle arco-avatar-wrapper-shape-circle"
              >
                <div
                  class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
                >
                  <div
                    class="arco-image all-border-box pc arco-avatar-img"
                  >
                    <div
                      class="image-content image-bottom-overlap"
                    >
                      <div
                        class="image-placeholder"
                      />
                    </div>
                    <div
                      class="image-container loading animate"
                      style="transition-duration: 200ms;"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="cell-arrow-icon"
          >
            <svg
              class="arrow-icon-svg"
              fill="none"
              viewBox="0 0 8 14"
            >
              <path
                clip-rule="evenodd"
                d="M0.594858 1.24219C0.399596 1.43745 0.399596 1.75403 0.594859 1.94929L5.59905 6.95348L0.636039 11.9165C0.440776 12.1118 0.440776 12.4283 0.636038 12.6236L0.989592 12.9771C1.18485 13.1724 1.50144 13.1724 1.6967 12.9771L7.35355 7.3203C7.5296 7.14425 7.54692 6.86959 7.40553 6.67413C7.38216 6.62774 7.35111 6.58423 7.31237 6.54549L1.65552 0.888634C1.46026 0.693372 1.14367 0.693372 0.948411 0.888634L0.594858 1.24219Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`cell demo test cell demo: children.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-cell-group all-border-box"
  >
    <div
      class="cell-group-body bordered"
    >
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
          </div>
          <div
            class="cell-content has-label"
          >
            <div
              aria-checked="true"
              aria-disabled="false"
              class="arco-switch all-border-box type-ios system-pc fully checked"
              role="switch"
              tabindex="0"
            >
              <div
                class="arco-switch-inner"
              />
            </div>
          </div>
          <div
            class="cell-arrow-icon"
          >
            <svg
              class="arrow-icon-svg"
              fill="none"
              viewBox="0 0 8 14"
            >
              <path
                clip-rule="evenodd"
                d="M0.594858 1.24219C0.399596 1.43745 0.399596 1.75403 0.594859 1.94929L5.59905 6.95348L0.636039 11.9165C0.440776 12.1118 0.440776 12.4283 0.636038 12.6236L0.989592 12.9771C1.18485 13.1724 1.50144 13.1724 1.6967 12.9771L7.35355 7.3203C7.5296 7.14425 7.54692 6.86959 7.40553 6.67413C7.38216 6.62774 7.35111 6.58423 7.31237 6.54549L1.65552 0.888634C1.46026 0.693372 1.14367 0.693372 0.948411 0.888634L0.594858 1.24219Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`cell demo test cell demo: desc.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-cell-group all-border-box"
  >
    <div
      class="cell-group-body bordered"
    >
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner has-desc"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
            <div
              class="cell-desc"
            >
              Description
            </div>
          </div>
          <div
            class="cell-content has-label"
          />
          <div
            class="cell-arrow-icon"
          >
            <svg
              class="arrow-icon-svg"
              fill="none"
              viewBox="0 0 8 14"
            >
              <path
                clip-rule="evenodd"
                d="M0.594858 1.24219C0.399596 1.43745 0.399596 1.75403 0.594859 1.94929L5.59905 6.95348L0.636039 11.9165C0.440776 12.1118 0.440776 12.4283 0.636038 12.6236L0.989592 12.9771C1.18485 13.1724 1.50144 13.1724 1.6967 12.9771L7.35355 7.3203C7.5296 7.14425 7.54692 6.86959 7.40553 6.67413C7.38216 6.62774 7.35111 6.58423 7.31237 6.54549L1.65552 0.888634C1.46026 0.693372 1.14367 0.693372 0.948411 0.888634L0.594858 1.24219Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner has-desc"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
            <div
              class="cell-desc"
            >
              Description
            </div>
          </div>
          <div
            class="cell-content has-label"
          />
        </div>
      </div>
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner has-desc"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
            <div
              class="cell-desc"
            >
              Description
            </div>
          </div>
          <div
            class="cell-content has-label"
          >
            <div
              class="cell-text"
            >
              Description
            </div>
          </div>
        </div>
      </div>
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner has-desc"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
            <div
              class="cell-desc"
            >
              Description
            </div>
          </div>
          <div
            class="cell-content has-label"
          >
            <span
              class="demo-cell-span"
            >
              +52.8
            </span>
          </div>
        </div>
      </div>
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner has-desc"
        >
          <div
            class="cell-label-icon"
          >
            <div
              class="arco-avatar-wrapper demo-cell-avatar-medium small circle arco-avatar-wrapper-shape-circle"
            >
              <div
                class="arco-avatar arco-avatar-size-small small arco-avatar-shape-circle circle arco-image-avatar arco-avatar-mode-image"
              >
                <div
                  class="arco-image all-border-box pc arco-avatar-img"
                >
                  <div
                    class="image-content image-bottom-overlap"
                  >
                    <div
                      class="image-placeholder"
                    />
                  </div>
                  <div
                    class="image-container loading animate"
                    style="transition-duration: 200ms;"
                  />
                </div>
              </div>
            </div>
          </div>
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
            <div
              class="cell-desc"
            >
              Description
            </div>
          </div>
          <div
            class="cell-content has-label"
          >
            <div
              class="cell-text"
            >
              Description
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`cell demo test cell demo: group.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-cell-group all-border-box"
  >
    <div
      class="cell-group-body bordered"
    >
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
          </div>
          <div
            class="cell-content has-label"
          />
          <div
            class="cell-arrow-icon"
          >
            <svg
              class="arrow-icon-svg"
              fill="none"
              viewBox="0 0 8 14"
            >
              <path
                clip-rule="evenodd"
                d="M0.594858 1.24219C0.399596 1.43745 0.399596 1.75403 0.594859 1.94929L5.59905 6.95348L0.636039 11.9165C0.440776 12.1118 0.440776 12.4283 0.636038 12.6236L0.989592 12.9771C1.18485 13.1724 1.50144 13.1724 1.6967 12.9771L7.35355 7.3203C7.5296 7.14425 7.54692 6.86959 7.40553 6.67413C7.38216 6.62774 7.35111 6.58423 7.31237 6.54549L1.65552 0.888634C1.46026 0.693372 1.14367 0.693372 0.948411 0.888634L0.594858 1.24219Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
          </div>
          <div
            class="cell-content has-label"
          />
          <div
            class="cell-arrow-icon"
          >
            <svg
              class="arrow-icon-svg"
              fill="none"
              viewBox="0 0 8 14"
            >
              <path
                clip-rule="evenodd"
                d="M0.594858 1.24219C0.399596 1.43745 0.399596 1.75403 0.594859 1.94929L5.59905 6.95348L0.636039 11.9165C0.440776 12.1118 0.440776 12.4283 0.636038 12.6236L0.989592 12.9771C1.18485 13.1724 1.50144 13.1724 1.6967 12.9771L7.35355 7.3203C7.5296 7.14425 7.54692 6.86959 7.40553 6.67413C7.38216 6.62774 7.35111 6.58423 7.31237 6.54549L1.65552 0.888634C1.46026 0.693372 1.14367 0.693372 0.948411 0.888634L0.594858 1.24219Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
          </div>
          <div
            class="cell-content has-label"
          />
          <div
            class="cell-arrow-icon"
          >
            <svg
              class="arrow-icon-svg"
              fill="none"
              viewBox="0 0 8 14"
            >
              <path
                clip-rule="evenodd"
                d="M0.594858 1.24219C0.399596 1.43745 0.399596 1.75403 0.594859 1.94929L5.59905 6.95348L0.636039 11.9165C0.440776 12.1118 0.440776 12.4283 0.636038 12.6236L0.989592 12.9771C1.18485 13.1724 1.50144 13.1724 1.6967 12.9771L7.35355 7.3203C7.5296 7.14425 7.54692 6.86959 7.40553 6.67413C7.38216 6.62774 7.35111 6.58423 7.31237 6.54549L1.65552 0.888634C1.46026 0.693372 1.14367 0.693372 0.948411 0.888634L0.594858 1.24219Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`cell demo test cell demo: header.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-cell-group all-border-box"
    style="margin-top: 16px;"
  >
    <div
      class="cell-group-header"
    >
      Load Status
    </div>
    <div
      class="cell-group-body bordered"
    >
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
          </div>
          <div
            class="cell-content has-label"
          />
          <div
            class="cell-arrow-icon"
          >
            <svg
              class="arrow-icon-svg"
              fill="none"
              viewBox="0 0 8 14"
            >
              <path
                clip-rule="evenodd"
                d="M0.594858 1.24219C0.399596 1.43745 0.399596 1.75403 0.594859 1.94929L5.59905 6.95348L0.636039 11.9165C0.440776 12.1118 0.440776 12.4283 0.636038 12.6236L0.989592 12.9771C1.18485 13.1724 1.50144 13.1724 1.6967 12.9771L7.35355 7.3203C7.5296 7.14425 7.54692 6.86959 7.40553 6.67413C7.38216 6.62774 7.35111 6.58423 7.31237 6.54549L1.65552 0.888634C1.46026 0.693372 1.14367 0.693372 0.948411 0.888634L0.594858 1.24219Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
          </div>
          <div
            class="cell-content has-label"
          />
          <div
            class="cell-arrow-icon"
          >
            <svg
              class="arrow-icon-svg"
              fill="none"
              viewBox="0 0 8 14"
            >
              <path
                clip-rule="evenodd"
                d="M0.594858 1.24219C0.399596 1.43745 0.399596 1.75403 0.594859 1.94929L5.59905 6.95348L0.636039 11.9165C0.440776 12.1118 0.440776 12.4283 0.636038 12.6236L0.989592 12.9771C1.18485 13.1724 1.50144 13.1724 1.6967 12.9771L7.35355 7.3203C7.5296 7.14425 7.54692 6.86959 7.40553 6.67413C7.38216 6.62774 7.35111 6.58423 7.31237 6.54549L1.65552 0.888634C1.46026 0.693372 1.14367 0.693372 0.948411 0.888634L0.594858 1.24219Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
          </div>
          <div
            class="cell-content has-label"
          />
          <div
            class="cell-arrow-icon"
          >
            <svg
              class="arrow-icon-svg"
              fill="none"
              viewBox="0 0 8 14"
            >
              <path
                clip-rule="evenodd"
                d="M0.594858 1.24219C0.399596 1.43745 0.399596 1.75403 0.594859 1.94929L5.59905 6.95348L0.636039 11.9165C0.440776 12.1118 0.440776 12.4283 0.636038 12.6236L0.989592 12.9771C1.18485 13.1724 1.50144 13.1724 1.6967 12.9771L7.35355 7.3203C7.5296 7.14425 7.54692 6.86959 7.40553 6.67413C7.38216 6.62774 7.35111 6.58423 7.31237 6.54549L1.65552 0.888634C1.46026 0.693372 1.14367 0.693372 0.948411 0.888634L0.594858 1.24219Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
    <div
      class="cell-group-footer"
    >
      <div
        class="demo-cell-loading-group"
      >
        <div
          class="arco-loading all-border-box circle demo-cell-loading"
          style="animation-duration: 1000ms; width: 14px; height: 14px;"
        >
          <svg
            viewBox="0 0 14 14"
          >
            <defs>
              <lineargradient
                id="grad1-inner-0"
                x1="0%"
                x2="100%"
                y1="0%"
                y2="0%"
              >
                <stop
                  class="loading-circle-middle stop-color-with-config"
                  offset="0%"
                  style="stop-color: currentColor;"
                />
                <stop
                  class="loading-circle-start stop-color-with-config"
                  offset="100%"
                  style="stop-color: currentColor;"
                />
              </lineargradient>
              <lineargradient
                id="grad2-inner-0"
                x1="0%"
                x2="100%"
                y1="0%"
                y2="0%"
              >
                <stop
                  class="loading-circle-middle stop-color-with-config"
                  offset="0%"
                  style="stop-color: currentColor;"
                />
                <stop
                  class="loading-circle-end stop-color-with-config"
                  offset="100%"
                  style="stop-color: currentColor;"
                />
              </lineargradient>
            </defs>
            <circle
              cx="7"
              cy="7"
              fill="none"
              r="6"
              stroke="url(#grad1-inner-0)"
              stroke-dasharray="18.84955592153876"
              stroke-dashoffset="18.84955592153876"
              stroke-width="2"
            />
            <circle
              cx="7"
              cy="7"
              fill="none"
              r="6"
              stroke="url(#grad2-inner-0)"
              stroke-dasharray="18.84955592153876"
              stroke-width="2"
            />
            <circle
              class="loading-circle-filleted fill-color-with-config"
              cx="13"
              cy="7"
              r="1"
              style="fill: currentColor;"
            />
          </svg>
        </div>
        Loading
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`cell demo test cell demo: icon.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-cell-group all-border-box"
  >
    <div
      class="cell-group-body bordered"
    >
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label-icon"
          >
            <svg
              class="arco-icon arco-icon-user "
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M512 192a106.688 106.688 0 000 213.333A106.688 106.688 0 00512 192zM320 298.667a192 192 0 11384 0 192 192 0 01-384 0zM398.293 640C320.64 640 256 703.936 256 785.067v68.266h512v-68.266C768 703.936 703.36 640 625.707 640H398.293zM170.667 785.067c0-127.254 101.973-230.4 227.626-230.4h227.414c125.653 0 227.626 103.146 227.626 230.4v115.2c0 21.205-17.066 38.4-37.973 38.4H208.64c-20.907 0-37.973-17.195-37.973-38.4v-115.2z"
                fill="currentColor"
              />
            </svg>
          </div>
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
          </div>
          <div
            class="cell-content has-label"
          />
          <div
            class="cell-arrow-icon"
          >
            <svg
              class="arrow-icon-svg"
              fill="none"
              viewBox="0 0 8 14"
            >
              <path
                clip-rule="evenodd"
                d="M0.594858 1.24219C0.399596 1.43745 0.399596 1.75403 0.594859 1.94929L5.59905 6.95348L0.636039 11.9165C0.440776 12.1118 0.440776 12.4283 0.636038 12.6236L0.989592 12.9771C1.18485 13.1724 1.50144 13.1724 1.6967 12.9771L7.35355 7.3203C7.5296 7.14425 7.54692 6.86959 7.40553 6.67413C7.38216 6.62774 7.35111 6.58423 7.31237 6.54549L1.65552 0.888634C1.46026 0.693372 1.14367 0.693372 0.948411 0.888634L0.594858 1.24219Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label-icon"
          >
            <svg
              class="arco-icon arco-icon-sound "
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <defs />
              <path
                d="M512 235.67L320 383.36v257.28l192 147.69V235.67zM512 128h85.333v768H512L234.667 682.667V341.333L512 128zm280.79 674.73l-49.58-69.46c68.054-48.555 110.123-131.03 110.123-221.654 0-90.197-41.685-172.352-109.226-221.013l49.877-69.227c89.899 64.747 144.683 172.715 144.683 290.261 0 118.059-55.296 226.475-145.878 291.094zm-84.16-171.455l-51.691-67.883a64.32 64.32 0 0025.728-51.35c0-20.33-9.579-39.082-25.814-51.413l51.627-67.946A149.632 149.632 0 01768 512.043a149.632 149.632 0 01-59.37 119.253zM85.332 341.333h85.334v341.334H85.333V341.333z"
                fill="currentColor"
              />
            </svg>
          </div>
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
          </div>
          <div
            class="cell-content has-label"
          />
          <div
            class="cell-arrow-icon"
          >
            <svg
              class="arrow-icon-svg"
              fill="none"
              viewBox="0 0 8 14"
            >
              <path
                clip-rule="evenodd"
                d="M0.594858 1.24219C0.399596 1.43745 0.399596 1.75403 0.594859 1.94929L5.59905 6.95348L0.636039 11.9165C0.440776 12.1118 0.440776 12.4283 0.636038 12.6236L0.989592 12.9771C1.18485 13.1724 1.50144 13.1724 1.6967 12.9771L7.35355 7.3203C7.5296 7.14425 7.54692 6.86959 7.40553 6.67413C7.38216 6.62774 7.35111 6.58423 7.31237 6.54549L1.65552 0.888634C1.46026 0.693372 1.14367 0.693372 0.948411 0.888634L0.594858 1.24219Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`cell demo test cell demo: index.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-cell all-border-box without-group"
  >
    <div
      class="arco-cell-inner"
    >
      <div
        class="cell-label"
      >
        <div
          class="cell-title"
        >
          List Content
        </div>
      </div>
      <div
        class="cell-content has-label"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`cell demo test cell demo: multi.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-cell-group all-border-box"
  >
    <div
      class="cell-group-body bordered"
    >
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          style="font-size: 12px; line-height: 18px; margin-bottom: -17px; padding-top: 16px;"
        >
          Description
        </div>
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
          </div>
          <div
            class="cell-content has-label"
          >
            <div
              class="demo-cell-info"
            >
              <div
                class="info"
              >
                Secondary information
              </div>
              <div
                class="sub-info"
              >
                Auxiliary information
              </div>
            </div>
          </div>
          <div
            class="cell-arrow-icon"
          >
            <svg
              class="arrow-icon-svg"
              fill="none"
              viewBox="0 0 8 14"
            >
              <path
                clip-rule="evenodd"
                d="M0.594858 1.24219C0.399596 1.43745 0.399596 1.75403 0.594859 1.94929L5.59905 6.95348L0.636039 11.9165C0.440776 12.1118 0.440776 12.4283 0.636038 12.6236L0.989592 12.9771C1.18485 13.1724 1.50144 13.1724 1.6967 12.9771L7.35355 7.3203C7.5296 7.14425 7.54692 6.86959 7.40553 6.67413C7.38216 6.62774 7.35111 6.58423 7.31237 6.54549L1.65552 0.888634C1.46026 0.693372 1.14367 0.693372 0.948411 0.888634L0.594858 1.24219Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
        <div
          style="font-size: 12px; line-height: 18px; margin-top: -15px; padding-bottom: 16px;"
        >
          Description
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`cell demo test cell demo: text.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-cell-group all-border-box"
  >
    <div
      class="cell-group-body bordered"
    >
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
          </div>
          <div
            class="cell-content has-label"
          >
            <div
              class="cell-text"
            >
              Description
            </div>
          </div>
        </div>
      </div>
      <div
        class="arco-cell all-border-box bordered"
      >
        <div
          class="arco-cell-inner"
        >
          <div
            class="cell-label"
          >
            <div
              class="cell-title"
            >
              List Content
            </div>
          </div>
          <div
            class="cell-content has-label"
          >
            <div
              class="cell-text"
            >
              Description
            </div>
          </div>
          <div
            class="cell-arrow-icon"
          >
            <svg
              class="arrow-icon-svg"
              fill="none"
              viewBox="0 0 8 14"
            >
              <path
                clip-rule="evenodd"
                d="M0.594858 1.24219C0.399596 1.43745 0.399596 1.75403 0.594859 1.94929L5.59905 6.95348L0.636039 11.9165C0.440776 12.1118 0.440776 12.4283 0.636038 12.6236L0.989592 12.9771C1.18485 13.1724 1.50144 13.1724 1.6967 12.9771L7.35355 7.3203C7.5296 7.14425 7.54692 6.86959 7.40553 6.67413C7.38216 6.62774 7.35111 6.58423 7.31237 6.54549L1.65552 0.888634C1.46026 0.693372 1.14367 0.693372 0.948411 0.888634L0.594858 1.24219Z"
                fill-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
