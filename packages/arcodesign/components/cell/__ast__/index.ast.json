{"description": "单元格组件，含单元格及单元格组合，常用于设置项、表单等。", "descriptionTags": {"en": "Cell, including cells and cell group, are often used to set items, forms, etc.", "type": "信息展示", "type_en": "Data Display", "name": "单元格", "name_en": "Cell"}, "displayName": "Cell", "methods": [], "props": {"style": {"defaultValue": null, "description": "自定义样式\n@en Custom stylesheet", "name": "style", "tags": {"en": "Custom stylesheet"}, "descWithTags": "自定义样式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellProps"}, "required": false, "type": {"name": "CSSProperties"}}, "className": {"defaultValue": null, "description": "自定义类名\n@en Custom classname", "name": "className", "tags": {"en": "Custom classname"}, "descWithTags": "自定义类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellProps"}, "required": false, "type": {"name": "string"}}, "icon": {"defaultValue": null, "description": "单元格左侧图标\n@en Cell left icon", "name": "icon", "tags": {"en": "Cell left icon"}, "descWithTags": "单元格左侧图标", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellProps"}, "required": false, "type": {"name": "ReactNode"}}, "label": {"defaultValue": null, "description": "标签名\n@en Label name", "name": "label", "tags": {"en": "Label name"}, "descWithTags": "标签名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellProps"}, "required": false, "type": {"name": "ReactNode"}}, "desc": {"defaultValue": null, "description": "描述\n@en Description", "name": "desc", "tags": {"en": "Description"}, "descWithTags": "描述", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellProps"}, "required": false, "type": {"name": "ReactNode"}}, "text": {"defaultValue": null, "description": "主体文字，如果有标签名则靠右，否则靠左\n@en The primary text, if there is a label name, it is to the right, otherwise it is to the left", "name": "text", "tags": {"en": "The primary text, if there is a label name, it is to the right, otherwise it is to the left"}, "descWithTags": "主体文字，如果有标签名则靠右，否则靠左", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellProps"}, "required": false, "type": {"name": "string"}}, "children": {"defaultValue": null, "description": "主体内容，如果有标签名则靠右，否则靠左\n@en The primary content, if there is a label name, it is to the right, otherwise it is to the left", "name": "children", "tags": {"en": "The primary content, if there is a label name, it is to the right, otherwise it is to the left"}, "descWithTags": "主体内容，如果有标签名则靠右，否则靠左", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellProps"}, "required": false, "type": {"name": "ReactNode"}}, "showArrow": {"defaultValue": null, "description": "是否展示右侧箭头\n@en Whether to show the right arrow", "name": "showArrow", "tags": {"en": "Whether to show the right arrow"}, "descWithTags": "是否展示右侧箭头", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellProps"}, "required": false, "type": {"name": "boolean"}}, "arrow": {"defaultValue": null, "description": "右侧箭头内容\n@en Right arrow content", "name": "arrow", "tags": {"en": "Right arrow content"}, "descWithTags": "右侧箭头内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellProps"}, "required": false, "type": {"name": "ReactNode"}}, "prepend": {"defaultValue": null, "description": "单元格前缀内容，在单元格上方\n@en Cell prefix content, above the cell", "name": "prepend", "tags": {"en": "Cell prefix content, above the cell"}, "descWithTags": "单元格前缀内容，在单元格上方", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellProps"}, "required": false, "type": {"name": "ReactNode"}}, "append": {"defaultValue": null, "description": "单元格后缀内容，在单元格下方\n@en Cell suffix content, below the cell", "name": "append", "tags": {"en": "Cell suffix content, below the cell"}, "descWithTags": "单元格后缀内容，在单元格下方", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellProps"}, "required": false, "type": {"name": "ReactNode"}}, "bordered": {"defaultValue": {"value": "true"}, "description": "是否有外边框\n@en Whether there is a border", "name": "bordered", "tags": {"en": "Whether there is a border", "default": "true"}, "descWithTags": "是否有外边框", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellProps"}, "required": false, "type": {"name": "boolean"}}, "onClick": {"defaultValue": null, "description": "点击单元格事件回调\n@en Callback for cell click event", "name": "onClick", "tags": {"en": "Callback for cell click event"}, "descWithTags": "点击单元格事件回调", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellProps"}, "required": false, "type": {"name": "(e: MouseEvent<HTMLDivElement, MouseEvent>) => void"}}, "ref": {"defaultValue": null, "description": "", "name": "ref", "tags": {}, "descWithTags": "", "parent": {"fileName": "arcom-github/node_modules/@types/react/index.d.ts", "name": "RefAttributes"}, "required": false, "type": {"name": "Ref<CellRef>"}}}, "deps": {"CellRef": {"dom": {"name": "dom", "required": true, "description": "最外层元素 DOM\n@en The outermost element DOM", "defaultValue": null, "type": {"name": "HTMLDivElement"}, "tags": {"en": "The outermost element DOM"}, "descWithTags": "最外层元素 DOM"}}}, "depComps": {"Group": {"description": "", "descriptionTags": {}, "displayName": "Group", "methods": [], "props": {"style": {"defaultValue": null, "description": "自定义样式\n@en Custom stylesheet", "name": "style", "tags": {"en": "Custom stylesheet"}, "descWithTags": "自定义样式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellGroupProps"}, "required": false, "type": {"name": "CSSProperties"}}, "className": {"defaultValue": null, "description": "自定义类名\n@en Custom classname", "name": "className", "tags": {"en": "Custom classname"}, "descWithTags": "自定义类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellGroupProps"}, "required": false, "type": {"name": "string"}}, "header": {"defaultValue": null, "description": "自定义头部内容\n@en custom header content", "name": "header", "tags": {"en": "custom header content"}, "descWithTags": "自定义头部内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellGroupProps"}, "required": false, "type": {"name": "ReactNode"}}, "footer": {"defaultValue": null, "description": "自定义尾部内容\n@en custom footer content", "name": "footer", "tags": {"en": "custom footer content"}, "descWithTags": "自定义尾部内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellGroupProps"}, "required": false, "type": {"name": "ReactNode"}}, "bordered": {"defaultValue": {"value": "true"}, "description": "是否有外边框\n@en Whether there is a border", "name": "bordered", "tags": {"en": "Whether there is a border", "default": "true"}, "descWithTags": "是否有外边框", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellGroupProps"}, "required": false, "type": {"name": "boolean"}}, "options": {"defaultValue": null, "description": "单元格配置，优先级高于children\n@en Cell setting", "name": "options", "tags": {"en": "Cell setting"}, "descWithTags": "单元格配置，优先级高于children", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellGroupProps"}, "required": false, "type": {"name": "(CellProps & RefAttributes<CellRef>)[]"}}, "children": {"defaultValue": null, "description": "内部单元格\n@en inner cell", "name": "children", "tags": {"en": "inner cell"}, "descWithTags": "内部单元格", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellGroupProps"}, "required": false, "type": {"name": "ReactNode"}}, "onClick": {"defaultValue": null, "description": "点击单元格组回调\n@en Callback of clicking the cell group", "name": "onClick", "tags": {"en": "Callback of clicking the cell group"}, "descWithTags": "点击单元格组回调", "parent": {"fileName": "arcom-github/packages/arcodesign/components/cell/type.ts", "name": "CellGroupProps"}, "required": false, "type": {"name": "(e: MouseEvent<HTMLDivElement, MouseEvent>) => void"}}, "ref": {"defaultValue": null, "description": "", "name": "ref", "tags": {}, "descWithTags": "", "parent": {"fileName": "arcom-github/node_modules/@types/react/index.d.ts", "name": "RefAttributes"}, "required": false, "type": {"name": "Ref<CellGroupRef>"}}}, "deps": {"CellProps": {"style": {"name": "style", "required": false, "description": "自定义样式\n@en Custom stylesheet", "defaultValue": null, "type": {"name": "CSSProperties"}, "tags": {"en": "Custom stylesheet"}, "descWithTags": "自定义样式"}, "className": {"name": "className", "required": false, "description": "自定义类名\n@en Custom classname", "defaultValue": null, "type": {"name": "string"}, "tags": {"en": "Custom classname"}, "descWithTags": "自定义类名"}, "icon": {"name": "icon", "required": false, "description": "单元格左侧图标\n@en Cell left icon", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "Cell left icon"}, "descWithTags": "单元格左侧图标"}, "label": {"name": "label", "required": false, "description": "标签名\n@en Label name", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "Label name"}, "descWithTags": "标签名"}, "desc": {"name": "desc", "required": false, "description": "描述\n@en Description", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "Description"}, "descWithTags": "描述"}, "text": {"name": "text", "required": false, "description": "主体文字，如果有标签名则靠右，否则靠左\n@en The primary text, if there is a label name, it is to the right, otherwise it is to the left", "defaultValue": null, "type": {"name": "string"}, "tags": {"en": "The primary text, if there is a label name, it is to the right, otherwise it is to the left"}, "descWithTags": "主体文字，如果有标签名则靠右，否则靠左"}, "children": {"name": "children", "required": false, "description": "主体内容，如果有标签名则靠右，否则靠左\n@en The primary content, if there is a label name, it is to the right, otherwise it is to the left", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "The primary content, if there is a label name, it is to the right, otherwise it is to the left"}, "descWithTags": "主体内容，如果有标签名则靠右，否则靠左"}, "showArrow": {"name": "showArrow", "required": false, "description": "是否展示右侧箭头\n@en Whether to show the right arrow", "defaultValue": null, "type": {"name": "boolean"}, "tags": {"en": "Whether to show the right arrow"}, "descWithTags": "是否展示右侧箭头"}, "arrow": {"name": "arrow", "required": false, "description": "右侧箭头内容\n@en Right arrow content", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "Right arrow content"}, "descWithTags": "右侧箭头内容"}, "prepend": {"name": "prepend", "required": false, "description": "单元格前缀内容，在单元格上方\n@en Cell prefix content, above the cell", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "Cell prefix content, above the cell"}, "descWithTags": "单元格前缀内容，在单元格上方"}, "append": {"name": "append", "required": false, "description": "单元格后缀内容，在单元格下方\n@en Cell suffix content, below the cell", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "Cell suffix content, below the cell"}, "descWithTags": "单元格后缀内容，在单元格下方"}, "bordered": {"name": "bordered", "required": false, "description": "是否有外边框\n@en Whether there is a border", "defaultValue": {"value": "true"}, "type": {"name": "boolean"}, "tags": {"en": "Whether there is a border", "default": "true"}, "descWithTags": "是否有外边框"}, "onClick": {"name": "onClick", "required": false, "description": "点击单元格事件回调\n@en Callback for cell click event", "defaultValue": null, "type": {"name": "(e: MouseEvent<HTMLDivElement, MouseEvent>) => void"}, "tags": {"en": "Callback for cell click event"}, "descWithTags": "点击单元格事件回调"}}, "CellRef": {"dom": {"name": "dom", "required": true, "description": "最外层元素 DOM\n@en The outermost element DOM", "defaultValue": null, "type": {"name": "HTMLDivElement"}, "tags": {"en": "The outermost element DOM"}, "descWithTags": "最外层元素 DOM"}}, "CellGroupRef": {"dom": {"name": "dom", "required": true, "description": "最外层元素 DOM\n@en The outermost element DOM", "defaultValue": null, "type": {"name": "HTMLDivElement"}, "tags": {"en": "The outermost element DOM"}, "descWithTags": "最外层元素 DOM"}}}, "depComps": {}, "typeNameInfo": {"props": "CellGroupProps", "ref": "CellGroupRef"}}}, "typeNameInfo": {"props": "CellProps", "ref": "CellRef"}, "isDefaultExport": true}