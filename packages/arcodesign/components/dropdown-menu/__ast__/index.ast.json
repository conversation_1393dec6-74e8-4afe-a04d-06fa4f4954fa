{"description": "下拉选择组件，点击选择器(select)展开下拉框(dropdown)，展示选择项(options)，兼容多个选择器的情况。", "descriptionTags": {"en": "Dropdown component, click the selector (select) to expand the dropdown box (dropdown), display the options (options), compatible with multiple selectors.", "type": "导航", "type_en": "Navigation", "name": "下拉选择菜单", "name_en": "DropdownMenu"}, "displayName": "DropdownMenu", "methods": [], "props": {"className": {"defaultValue": null, "description": "自定义类名\n@en Custom classname", "name": "className", "tags": {"en": "Custom classname"}, "descWithTags": "自定义类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "string"}}, "style": {"defaultValue": null, "description": "自定义样式\n@en Custom stylesheet", "name": "style", "tags": {"en": "Custom stylesheet"}, "descWithTags": "自定义样式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "CSSProperties"}}, "defaultSelectIndex": {"defaultValue": null, "description": "初始被选择的选择器的序号\n@en The number of the selector that is initially selected", "name": "defaultSelectIndex", "tags": {"en": "The number of the selector that is initially selected"}, "descWithTags": "初始被选择的选择器的序号", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "number"}}, "selectIndex": {"defaultValue": null, "description": "(受控) 被选择的选择器的序号\n@en The selected number of selector (controlled)", "name": "selectIndex", "tags": {"en": "The selected number of selector (controlled)"}, "descWithTags": "(受控) 被选择的选择器的序号", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "number"}}, "disabled": {"defaultValue": null, "description": "选择器禁用状态\n@en Selector disabled state", "name": "disabled", "tags": {"en": "Selector disabled state"}, "descWithTags": "选择器禁用状态", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "boolean[]"}}, "selectTips": {"defaultValue": null, "description": "选择器的说明\n@en Description of selectors", "name": "selectTips", "tags": {"en": "Description of selectors"}, "descWithTags": "选择器的说明", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "string[]"}}, "onSelectChange": {"defaultValue": null, "description": "当选中的选择器改变的时候触发的回调函数\n@en Callback when the selected selector changes", "name": "onSelectChange", "tags": {"en": "Callback when the selected selector changes"}, "descWithTags": "当选中的选择器改变的时候触发的回调函数", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "(selectIndex: number) => void"}}, "icon": {"defaultValue": null, "description": "选择器右侧的图标\n@en Icon to the right of the selector", "name": "icon", "tags": {"en": "Icon to the right of the selector"}, "descWithTags": "选择器右侧的图标", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "ReactNode"}}, "defaultShowDropdown": {"defaultValue": null, "description": "初始状态,下拉框状态\n@en Initial dropdown state", "name": "defaultShowDropdown", "tags": {"en": "Initial dropdown state"}, "descWithTags": "初始状态,下拉框状态", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "boolean"}}, "showDropdown": {"defaultValue": null, "description": "可选受控，组件挂载时下拉框的状态\n@en Optionally controlled, the state of the dropdown box when the component is mounted", "name": "showDropdown", "tags": {"en": "Optionally controlled, the state of the dropdown box when the component is mounted"}, "descWithTags": "可选受控，组件挂载时下拉框的状态", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "boolean"}}, "onDropdownChange": {"defaultValue": null, "description": "下拉框展示状态改变时触发的回调函数\n@en Callback when displayed status of drop-down box changes", "name": "onDropdownChange", "tags": {"en": "Callback when displayed status of drop-down box changes"}, "descWithTags": "下拉框展示状态改变时触发的回调函数", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "(dropdown: boolean, selectIndex?: number) => void"}}, "getContainer": {"defaultValue": null, "description": "获得下拉框的容器\n@en Get the dropdown box's container", "name": "getContainer", "tags": {"en": "Get the dropdown box's container"}, "descWithTags": "获得下拉框的容器", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "() => HTMLDivElement"}}, "chooseAndClose": {"defaultValue": {"value": "true"}, "description": "选择后是否收起面板，仅单选有效\n@en Whether to close the panel after selection, only single selection is valid", "name": "chooseAndClose", "tags": {"en": "Whether to close the panel after selection, only single selection is valid", "default": "true"}, "descWithTags": "选择后是否收起面板，仅单选有效", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "boolean"}}, "onCancel": {"defaultValue": null, "description": "取消选择\n@en Callback for cancelling selection", "name": "onCancel", "tags": {"en": "Callback for cancelling selection"}, "descWithTags": "取消选择", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "() => void"}}, "children": {"defaultValue": null, "description": "自定义下拉框元素\n@en Custom dropdown element", "name": "children", "tags": {"en": "Custom dropdown element"}, "descWithTags": "自定义下拉框元素", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "ReactNode"}}, "extraForDropdown": {"defaultValue": null, "description": "dropdown参数\n@en Dropdown parameter", "name": "extraForDropdown", "tags": {"en": "Dropdown parameter"}, "descWithTags": "dropdown参数", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "DropdownMenuBasicProps"}, "required": false, "type": {"name": "DropdownCommonProps"}}, "multiple": {"defaultValue": {"value": "false"}, "description": "是否支持多选\n@en Whether to support multiple selection", "name": "multiple", "tags": {"en": "Whether to support multiple selection", "default": "false"}, "descWithTags": "是否支持多选", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "SingleOptionProps"}, "required": false, "type": {"name": "boolean"}}, "options": {"defaultValue": null, "description": "下拉框展示的选择项，其中级联选项仅单选支持\n@en The options displayed in the dropdown box, in which the cascade option only supports single selection", "name": "options", "tags": {"en": "The options displayed in the dropdown box, in which the cascade option only supports single selection"}, "descWithTags": "下拉框展示的选择项，其中级联选项仅单选支持", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "SingleOptionProps"}, "required": true, "type": {"name": "string[] | OptionsItem[][] | CascadeOptions[]"}}, "defaultValues": {"defaultValue": null, "description": "初始选择项\n@en initial selection item", "name": "defaultValues", "tags": {"en": "initial selection item"}, "descWithTags": "初始选择项", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "SingleOptionProps"}, "required": false, "type": {"name": "ReactText[] | ReactText[][]"}}, "values": {"defaultValue": null, "description": "(受控) 每个选择器选中的项\n@en (Controlled) The item selected by each selector", "name": "values", "tags": {"en": "(Controlled) The item selected by each selector"}, "descWithTags": "(受控) 每个选择器选中的项", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "SingleOptionProps"}, "required": false, "type": {"name": "ReactText[] | ReactText[][]"}}, "renderSelectLabel": {"defaultValue": null, "description": "自定义选择器\n@en Custom selector", "name": "renderSelectLabel", "tags": {"en": "Custom selector"}, "descWithTags": "自定义选择器", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "SingleOptionProps"}, "required": false, "type": {"name": "((op: OptionsItem, index: number) => ReactNode) | ((op: OptionsItem[], index: number) => ReactNode)"}}, "onOptionClick": {"defaultValue": null, "description": "点击选项时触发的回调函数\n@en Callback when clicking option", "name": "onOptionClick", "tags": {"en": "Callback when clicking option"}, "descWithTags": "点击选项时触发的回调函数", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "SingleOptionProps"}, "required": false, "type": {"name": "((value: ReactText, item: OptionsItem, selectIndex?: number) => void) | ((selected: boolean, val: ReactText, item: OptionsItem, selectIndex?: number) => void)"}}, "onOptionChange": {"defaultValue": null, "description": "选项改变时触发的回调函数\n@en Callback when options change", "name": "onOptionChange", "tags": {"en": "Callback when options change"}, "descWithTags": "选项改变时触发的回调函数", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "SingleOptionProps"}, "required": false, "type": {"name": "((value: ReactText, item: OptionsItem, selectIndex?: number) => void) | ((vals: ReactText[], item: OptionsItem, selectIndex?: number) => void)"}}, "onValuesChange": {"defaultValue": null, "description": "所有选择器选项总值改变时的回调函数\n@en Callback when the total value of all selector options changes", "name": "onValuesChange", "tags": {"en": "Callback when the total value of all selector options changes"}, "descWithTags": "所有选择器选项总值改变时的回调函数", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "SingleOptionProps"}, "required": false, "type": {"name": "((values: ReactText[]) => void) | ((values: ReactText[][]) => void)"}}, "getFormattedOptions": {"defaultValue": null, "description": "格式化传入的options\n@en Format the input options", "name": "getFormattedOptions", "tags": {"en": "Format the input options"}, "descWithTags": "格式化传入的options", "parent": {"fileName": "arcom-github/packages/arcodesign/components/dropdown-menu/type.ts", "name": "SingleOptionProps"}, "required": false, "type": {"name": "((options: string[] | OptionsItem[][] | CascadeOptions[], values?: ReactText[]) => { formattedOptions: OptionsItem[][]; formattedValue: ReactText[]; }) | ((options: string[] | ... 1 more ... | CascadeOptions[], values?: ReactText[][]) => { ...; })"}}, "ref": {"defaultValue": null, "description": "", "name": "ref", "tags": {}, "descWithTags": "", "parent": {"fileName": "arcom-github/node_modules/@types/react/index.d.ts", "name": "RefAttributes"}, "required": false, "type": {"name": "Ref<DropdownMenuRef>"}}}, "deps": {"DropdownCommonProps": {"className": {"name": "className", "required": false, "description": "自定义类名\n@en Custom classname", "defaultValue": null, "type": {"name": "string"}, "tags": {"en": "Custom classname"}, "descWithTags": "自定义类名"}, "style": {"name": "style", "required": false, "description": "自定义样式\n@en Custom stylesheet", "defaultValue": null, "type": {"name": "CSSProperties"}, "tags": {"en": "Custom stylesheet"}, "descWithTags": "自定义样式"}, "extraNode": {"name": "extraNode", "required": false, "description": "下拉框底部元素\n@en Dropdown bottom element", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "Dropdown bottom element"}, "descWithTags": "下拉框底部元素"}, "top": {"name": "top", "required": false, "description": "定位下拉框出现的位置\n@en Position where the dropDown box appears", "defaultValue": null, "type": {"name": "number"}, "tags": {"en": "Position where the dropDown box appears"}, "descWithTags": "定位下拉框出现的位置"}, "bottom": {"name": "bottom", "required": false, "description": "向上展开时下拉框出现的位置\n@en Position Where the drop-down box appears when expanding upwards", "defaultValue": null, "type": {"name": "number"}, "tags": {"en": "Position Where the drop-down box appears when expanding upwards"}, "descWithTags": "向上展开时下拉框出现的位置"}, "direction": {"name": "direction", "required": false, "description": "展开方向\n@en Expand direction", "defaultValue": {"value": "\"down\""}, "type": {"name": "enum", "raw": "\"up\" | \"down\"", "value": [{"value": "\"up\""}, {"value": "\"down\""}]}, "tags": {"en": "Expand direction", "default": "\"down\""}, "descWithTags": "展开方向"}, "height": {"name": "height", "required": false, "description": "受控模式，下拉框高度\n@en Dropdown box height in controlled mode", "defaultValue": null, "type": {"name": "number"}, "tags": {"en": "Dropdown box height in controlled mode"}, "descWithTags": "受控模式，下拉框高度"}, "maxHeight": {"name": "maxHeight", "required": false, "description": "下拉框最大的高度\n@en The maximum height of the dropdown", "defaultValue": {"value": "500"}, "type": {"name": "number"}, "tags": {"en": "The maximum height of the dropdown", "default": "500"}, "descWithTags": "下拉框最大的高度"}, "clickOtherToClose": {"name": "clickOtherToClose", "required": false, "description": "点击其他区域是否取消选择\n@en Click on other areas to cancel the selection", "defaultValue": {"value": "true"}, "type": {"name": "boolean"}, "tags": {"en": "Click on other areas to cancel the selection", "default": "true"}, "descWithTags": "点击其他区域是否取消选择"}, "touchToClose": {"name": "touchToClose", "required": false, "description": "是否在触发touchstart时就取消选择，否则在click之后再取消选择\n@en Whether to cancel the selection when touchstart is triggered, otherwise cancel the selection after the click", "defaultValue": {"value": "true"}, "type": {"name": "boolean"}, "tags": {"en": "Whether to cancel the selection when touchstart is triggered, otherwise cancel the selection after the click", "default": "true"}, "descWithTags": "是否在触发touchstart时就取消选择，否则在click之后再取消选择"}, "dropdownAnimationTimeout": {"name": "dropdownAnimationTimeout", "required": false, "description": "展开收起动画的时间\n@en Expand and collapse animation duration", "defaultValue": {"value": "300"}, "type": {"name": "number"}, "tags": {"en": "Expand and collapse animation duration", "default": "300"}, "descWithTags": "展开收起动画的时间"}, "dropdownAnimationFunction": {"name": "dropdownAnimationFunction", "required": false, "description": "展开收起动画曲线函数\n@en Expand and collapse animation curve function", "defaultValue": {"value": "\"cubic-bezier(0.32, 0.96, 0.6, 1)\""}, "type": {"name": "string"}, "tags": {"en": "Expand and collapse animation curve function", "default": "\"cubic-bezier(0.32, 0.96, 0.6, 1)\""}, "descWithTags": "展开收起动画曲线函数"}, "showMask": {"name": "showMask", "required": false, "description": "是否展示遮罩层\n@en Whether to show the mask layer", "defaultValue": {"value": "true"}, "type": {"name": "boolean"}, "tags": {"en": "Whether to show the mask layer", "default": "true"}, "descWithTags": "是否展示遮罩层"}, "maskAnimationTimeout": {"name": "maskAnimationTimeout", "required": false, "description": "蒙层动画时长\n@en Mask animation duration", "defaultValue": {"value": "500"}, "type": {"name": "number"}, "tags": {"en": "Mask animation duration", "default": "500"}, "descWithTags": "蒙层动画时长"}, "maskAnimationFunction": {"name": "maskAnimationFunction", "required": false, "description": "蒙层动画函数\n@en Mask animation function", "defaultValue": {"value": "\"cubic-bezier(0.32, 0.96, 0.6, 1)\""}, "type": {"name": "string"}, "tags": {"en": "Mask animation function", "default": "\"cubic-bezier(0.32, 0.96, 0.6, 1)\""}, "descWithTags": "蒙层动画函数"}, "useColumn": {"name": "useColumn", "required": false, "description": "使用多列标签样式，传 true 为 4 列，传数字为指定 n 列\n@en Use multi-column label style, input true, it's 4 columns, and input number n, it will be n columns", "defaultValue": {"value": "false"}, "type": {"name": "number | boolean"}, "tags": {"en": "Use multi-column label style, input true, it's 4 columns, and input number n, it will be n columns", "default": "false"}, "descWithTags": "使用多列标签样式，传 true 为 4 列，传数字为指定 n 列"}, "optionIcon": {"name": "optionIcon", "required": false, "description": "选项附带图标\n@en Icon in each option", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "Icon in each option"}, "descWithTags": "选项附带图标"}, "mountOnEnter": {"name": "mountOnEnter", "required": false, "description": "是否在打开下拉框时再加载内容\n@en Whether to reload the content when the dropdown box is opened", "defaultValue": {"value": "true"}, "type": {"name": "boolean"}, "tags": {"en": "Whether to reload the content when the dropdown box is opened", "default": "true"}, "descWithTags": "是否在打开下拉框时再加载内容"}, "unmountOnExit": {"name": "unmountOnExit", "required": false, "description": "是否在退出时卸载内容\n@en Whether to unmount content on exit", "defaultValue": {"value": "true"}, "type": {"name": "boolean"}, "tags": {"en": "Whether to unmount content on exit", "default": "true"}, "descWithTags": "是否在退出时卸载内容"}, "preventBodyScroll": {"name": "preventBodyScroll", "required": false, "description": "弹窗打开时是否禁止body的滚动\n@en Whether to prohibit the scrolling of the body when the dropdown box is opened", "defaultValue": {"value": "true"}, "type": {"name": "boolean"}, "tags": {"en": "Whether to prohibit the scrolling of the body when the dropdown box is opened", "default": "true"}, "descWithTags": "弹窗打开时是否禁止body的滚动"}, "initialBodyOverflow": {"name": "initialBodyOverflow", "required": false, "description": "页面初始 overflow 状态，即关闭弹窗时 overflow 应该还原的状态\n@en The initial overflow state of the page, that is, the state in which overflow should be restored when the dropdown box is closed\n@default_en The page overflow value when the first fullscreen component (popup, toast, etc.) is opened", "defaultValue": {"value": "第一个全屏组件（弹窗、toast等）打开时页面overflow值"}, "type": {"name": "string"}, "tags": {"en": "The initial overflow state of the page, that is, the state in which overflow should be restored when the dropdown box is closed", "default": "第一个全屏组件（弹窗、toast等）打开时页面overflow值", "default_en": "The page overflow value when the first fullscreen component (popup, toast, etc.) is opened"}, "descWithTags": "页面初始 overflow 状态，即关闭弹窗时 overflow 应该还原的状态"}, "getAnchorElement": {"name": "getAnchorElement", "required": false, "description": "用于定位的元素，优先级低于top/bottom\n@en Element used for positioning, with lower priority than top/bottom\n@default_en The parent element of the current DOM", "defaultValue": {"value": "当前DOM所在位置的父元素"}, "type": {"name": "() => HTMLElement"}, "tags": {"en": "Element used for positioning, with lower priority than top/bottom", "default": "当前DOM所在位置的父元素", "default_en": "The parent element of the current DOM"}, "descWithTags": "用于定位的元素，优先级低于top/bottom"}, "isStopTouchEl": {"name": "isStopTouchEl", "required": false, "description": "点击某元素时是否阻止关闭面板, 优先级高于`getStopTouchElement`，当 clickOtherToClose=true 时有效\n@en Whether to prevent the panel from closing when an element is clicked, the priority is higher than `getStopTouchElement`, valid when clickOtherToClose=true", "defaultValue": null, "type": {"name": "(el: HTMLElement) => boolean"}, "tags": {"en": "Whether to prevent the panel from closing when an element is clicked, the priority is higher than `getStopTouchElement`, valid when clickOtherToClose=true"}, "descWithTags": "点击某元素时是否阻止关闭面板, 优先级高于`getStopTouchElement`，当 clickOtherToClose=true 时有效"}, "getStopTouchElements": {"name": "getStopTouchElements", "required": false, "description": "可阻止关闭面板的元素，当 clickOtherToClose=true 时有效\n@en Element that prevents the panel from closing, valid when clickOtherToClose=true\n@default_en The parent element of the current component", "defaultValue": {"value": "当前组件的父元素"}, "type": {"name": "() => HTMLElement[]"}, "tags": {"en": "Element that prevents the panel from closing, valid when clickOtherToClose=true", "default": "当前组件的父元素", "default_en": "The parent element of the current component"}, "descWithTags": "可阻止关闭面板的元素，当 clickOtherToClose=true 时有效"}, "getScrollContainer": {"name": "getScrollContainer", "required": false, "description": "内容内部滚动区域容器，在该容器中未滚动到顶部或底部时会释放滚动\n@en Content inner scroll container, scrolling will be releases when it is not scrolled to the top or bottom", "defaultValue": null, "type": {"name": "() => HTMLElement | HTMLElement[]"}, "tags": {"en": "Content inner scroll container, scrolling will be releases when it is not scrolled to the top or bottom"}, "descWithTags": "内容内部滚动区域容器，在该容器中未滚动到顶部或底部时会释放滚动"}, "getPortalContainer": {"name": "getPortalContainer", "required": false, "description": "获取挂载容器\n@en Get mounted container", "defaultValue": null, "type": {"name": "() => HTMLElement"}, "tags": {"en": "Get mounted container"}, "descWithTags": "获取挂载容器"}}, "OptionsItem": {"label": {"name": "label", "required": true, "description": "选项名称\n@en option label", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "option label"}, "descWithTags": "选项名称"}, "value": {"name": "value", "required": true, "description": "选项标识\n@en option value", "defaultValue": null, "type": {"name": "ReactText"}, "tags": {"en": "option value"}, "descWithTags": "选项标识"}, "disabled": {"name": "disabled", "required": false, "description": "选项是否可用，默认false表示可用\n@en Whether the option is available, the default false means available", "defaultValue": null, "type": {"name": "boolean"}, "tags": {"en": "Whether the option is available, the default false means available"}, "descWithTags": "选项是否可用，默认false表示可用"}}, "CascadeOptions": {"children": {"name": "children", "required": false, "description": "级联选择的下一级内容\n@en The next level content of the cascade selection", "defaultValue": null, "type": {"name": "OptionsItem[]"}, "tags": {"en": "The next level content of the cascade selection"}, "descWithTags": "级联选择的下一级内容"}, "label": {"name": "label", "required": true, "description": "选项名称\n@en option label", "defaultValue": null, "type": {"name": "ReactNode"}, "tags": {"en": "option label"}, "descWithTags": "选项名称"}, "value": {"name": "value", "required": true, "description": "选项标识\n@en option value", "defaultValue": null, "type": {"name": "ReactText"}, "tags": {"en": "option value"}, "descWithTags": "选项标识"}, "disabled": {"name": "disabled", "required": false, "description": "选项是否可用，默认false表示可用\n@en Whether the option is available, the default false means available", "defaultValue": null, "type": {"name": "boolean"}, "tags": {"en": "Whether the option is available, the default false means available"}, "descWithTags": "选项是否可用，默认false表示可用"}}, "DropdownMenuRef": {"dom": {"name": "dom", "required": true, "description": "最外层元素 DOM\n@en The outermost element DOM", "defaultValue": null, "type": {"name": "HTMLDivElement"}, "tags": {"en": "The outermost element DOM"}, "descWithTags": "最外层元素 DOM"}}}, "depComps": {}, "typeNameInfo": {"props": "DropdownMenuBasicProps", "ref": "DropdownMenuRef"}, "isDefaultExport": true}