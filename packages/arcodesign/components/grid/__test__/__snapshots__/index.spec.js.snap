// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`grid demo test grid demo: index.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-grid demo-multi-grid col-2"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item horizontal"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item horizontal"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
      </div>
    </div>
  </div>
  <div
    class="arco-grid demo-multi-grid col-3"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
      </div>
    </div>
  </div>
  <div
    class="arco-grid demo-multi-grid col-4"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
      </div>
    </div>
  </div>
  <div
    class="arco-grid demo-multi-grid col-5"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`grid demo test grid demo: index2.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-grid"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
          style="border-radius: 20px; width: 40px; height: 40px;"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
          style="border-radius: 20px; width: 40px; height: 40px;"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
          style="border-radius: 20px; width: 40px; height: 40px;"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`grid demo test grid demo: index3.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-grid"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item border"
        style="margin-bottom: 0px; margin-right: 0px; padding: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item border"
        style="margin-bottom: 0px; margin-right: 0px; padding: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item border"
        style="margin-bottom: 0px; margin-right: 0px; padding: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`grid demo test grid demo: index4.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-grid demo-multi-grid"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item horizontal"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
          <span
            class="arco-grid-rows-item-content"
          >
            Description Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item horizontal"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
          <span
            class="arco-grid-rows-item-content"
          >
            Description Text
          </span>
        </div>
      </div>
    </div>
  </div>
  <div
    class="arco-grid demo-multi-grid"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
          <span
            class="arco-grid-rows-item-content"
          >
            Description Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
          <span
            class="arco-grid-rows-item-content"
          >
            Description Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
          <span
            class="arco-grid-rows-item-content"
          >
            Description Text
          </span>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`grid demo test grid demo: index5.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-grid sliding"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; min-width: 63px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`grid demo test grid demo: index6.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-grid"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item grid-item-more"
        style="margin-bottom: 0px; margin-right: 0px;"
      >
        <img
          class="arco-grid-rows-item-icon "
          src="https://sf1-cdn-tos.toutiaostatic.com/obj/arco-mobile/_static_/grid-demo-more.png"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            More
          </span>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`grid demo test grid demo: index7.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-grid"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item horizontal"
        style="margin-bottom: 0px; margin-right: 0px; padding: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
        <i
          class="horizontal-border"
        />
      </div>
      <div
        class="arco-grid-rows-item horizontal"
        style="margin-bottom: 0px; margin-right: 0px; padding: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="horizontal-border"
        />
      </div>
    </div>
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item horizontal"
        style="margin-bottom: 0px; margin-right: 0px; padding: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
        <i
          class="horizontal-border"
        />
      </div>
      <div
        class="arco-grid-rows-item horizontal"
        style="margin-bottom: 0px; margin-right: 0px; padding: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="horizontal-border"
        />
      </div>
    </div>
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item horizontal"
        style="margin-bottom: 0px; margin-right: 0px; padding: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item horizontal"
        style="margin-bottom: 0px; margin-right: 0px;"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`grid demo test grid demo: index8.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-grid"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; padding: 10px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
        <i
          class="horizontal-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; padding: 10px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
        <i
          class="horizontal-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; padding: 10px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="horizontal-border"
        />
      </div>
    </div>
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; padding: 10px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
        <i
          class="horizontal-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; padding: 10px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
        <i
          class="horizontal-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; padding: 10px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="horizontal-border"
        />
      </div>
    </div>
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; padding: 10px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; padding: 10px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px; padding: 10px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`grid demo test grid demo: index9.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-grid"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 16px; margin-right: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
        <i
          class="horizontal-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 16px; margin-right: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
        <i
          class="horizontal-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 16px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="horizontal-border"
        />
      </div>
    </div>
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 16px; margin-right: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
        <i
          class="horizontal-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 16px; margin-right: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
        <i
          class="horizontal-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 16px; margin-right: 0px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="horizontal-border"
        />
      </div>
    </div>
    <div
      class="arco-grid-rows"
    >
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 16px;"
      >
        <div
          class="grid-demo-block"
        />
        <div
          class="arco-grid-rows-item-text"
        >
          <span
            class="arco-grid-rows-item-title"
          >
            Title Text
          </span>
        </div>
        <i
          class="vertical-border"
        />
      </div>
      <div
        class="arco-grid-rows-item"
        style="margin-bottom: 0px; margin-right: 0px;"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`grid demo test grid demo: index10.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-grid"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="grid-demo-rows-item"
      >
        <div
          class="grid-demo-block"
        >
          <div
            class="arco-badge arco-badge-dot dot arco-badge-absolute absolute"
          />
        </div>
        <div
          class="grid-demo-rows-item-text"
        >
          <span
            class="grid-demo-rows-item-title"
          >
            Title Text
          </span>
        </div>
      </div>
      <div
        class="grid-demo-rows-item"
      >
        <div
          class="grid-demo-block"
        >
          <div
            class="arco-badge arco-badge-absolute absolute"
          >
            <span
              class="arco-badge-text badge-text"
            >
              New
            </span>
          </div>
        </div>
        <div
          class="grid-demo-rows-item-text"
        >
          <span
            class="grid-demo-rows-item-title"
          >
            Title Text
          </span>
        </div>
      </div>
      <div
        class="grid-demo-rows-item"
      >
        <div
          class="grid-demo-block"
        >
          <div
            class="arco-badge arco-badge-absolute absolute"
          >
            <span
              class="arco-badge-text badge-text"
            >
              99+
            </span>
          </div>
        </div>
        <div
          class="grid-demo-rows-item-text"
        >
          <span
            class="grid-demo-rows-item-title"
          >
            Title Text
          </span>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`grid demo test grid demo: index11.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="arco-grid"
  >
    <div
      class="arco-grid-rows"
    >
      <div
        class="grid-demo-rows-item"
      >
        <img
          class="grid-demo-rows-item-icon"
          src="[object Object]"
        />
        <div
          class="grid-demo-rows-item-text"
        >
          <span
            class="grid-demo-rows-item-title"
          >
            Title Text
          </span>
        </div>
      </div>
      <div
        class="grid-demo-rows-item"
      >
        <img
          class="grid-demo-rows-item-icon"
          src="[object Object]"
        />
        <div
          class="grid-demo-rows-item-text"
        >
          <span
            class="grid-demo-rows-item-title"
          >
            Title Text
          </span>
        </div>
      </div>
      <div
        class="grid-demo-rows-item"
      >
        <img
          class="grid-demo-rows-item-icon"
          src="[object Object]"
        />
        <div
          class="grid-demo-rows-item-text"
        >
          <span
            class="grid-demo-rows-item-title"
          >
            Title Text
          </span>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
