{"description": "环形进度条组件，以圆环形式表示百分比进度", "descriptionTags": {"en": "Annular progress bar component, representing percentage progress in the form of a circle", "type": "反馈", "type_en": "<PERSON><PERSON><PERSON>", "name": "环形进度条", "name_en": "CircleProgress"}, "displayName": "CircleProgress", "methods": [], "props": {"className": {"defaultValue": null, "description": "自定义类名\n@en Custom classname", "name": "className", "tags": {"en": "Custom classname"}, "descWithTags": "自定义类名", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "string"}}, "style": {"defaultValue": null, "description": "自定义样式\n@en Custom stylesheet", "name": "style", "tags": {"en": "Custom stylesheet"}, "descWithTags": "自定义样式", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "CSSProperties"}}, "percentPosition": {"defaultValue": {"value": "\"center\""}, "description": "文字显示位置\n@en Text display position", "name": "percentPosition", "tags": {"en": "Text display position", "default": "\"center\""}, "descWithTags": "文字显示位置", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "enum", "raw": "\"center\" | \"bottom\"", "value": [{"value": "\"center\""}, {"value": "\"bottom\""}]}}, "showPercent": {"defaultValue": {"value": "true"}, "description": "是否显示圆环内部文字\n@en Whether to display the text inside the ring", "name": "showPercent", "tags": {"en": "Whether to display the text inside the ring", "default": "true"}, "descWithTags": "是否显示圆环内部文字", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "boolean"}}, "percentage": {"defaultValue": null, "description": "进度环百分比\n@en Progress ring Percentage", "name": "percentage", "tags": {"en": "Progress ring Percentage"}, "descWithTags": "进度环百分比", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": true, "type": {"name": "number"}}, "renderPercent": {"defaultValue": null, "description": "圆环内部文字显示（一个函数，返回ReactNode类型，回调参数为当前进度百分比）\n@en The text inside the ring (a function that returns the ReactNode type, and the callback parameter is the current progress percentage)", "name": "renderPercent", "tags": {"en": "The text inside the ring (a function that returns the ReactNode type, and the callback parameter is the current progress percentage)"}, "descWithTags": "圆环内部文字显示（一个函数，返回ReactNode类型，回调参数为当前进度百分比）", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "(percentage: number) => ReactNode"}}, "trackColor": {"defaultValue": null, "description": "轨道颜色\n@en Track color", "name": "trackColor", "tags": {"en": "Track color"}, "descWithTags": "轨道颜色", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "string"}}, "progressColor": {"defaultValue": null, "description": "进度环颜色\n@en progress ring color", "name": "progressColor", "tags": {"en": "progress ring color"}, "descWithTags": "进度环颜色", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "string"}}, "disabled": {"defaultValue": {"value": "false"}, "description": "是否置灰\n@en Whether to disable", "name": "disabled", "tags": {"en": "Whether to disable", "default": "false"}, "descWithTags": "是否置灰", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "boolean"}}, "trackStroke": {"defaultValue": {"value": "10"}, "description": "轨道宽度\n@en Track width", "name": "trackStroke", "tags": {"en": "Track width", "default": "10"}, "descWithTags": "轨道宽度", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "number"}}, "progressStroke": {"defaultValue": {"value": "12"}, "description": "进度环宽度\n@en Progress ring width", "name": "progressStroke", "tags": {"en": "Progress ring width", "default": "12"}, "descWithTags": "进度环宽度", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "number"}}, "progressColorStart": {"defaultValue": null, "description": "进度环渐变开始的颜色(当设置progressColorStart和progressColorEnd其中一个时，颜色会覆盖掉progressColor的颜色)\n@en The start gradient color of the progress ring (when one of progressColorStart and progressColorEnd is set, the color will override the color of progressColor)", "name": "progressColorStart", "tags": {"en": "The start gradient color of the progress ring (when one of progressColorStart and progressColorEnd is set, the color will override the color of progressColor)"}, "descWithTags": "进度环渐变开始的颜色(当设置progressColorStart和progressColorEnd其中一个时，颜色会覆盖掉progressColor的颜色)", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "string"}}, "progressColorEnd": {"defaultValue": null, "description": "进度环渐变结束的颜色\n@en The end gradient color of the progress ring", "name": "progressColorEnd", "tags": {"en": "The end gradient color of the progress ring"}, "descWithTags": "进度环渐变结束的颜色", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "string"}}, "duration": {"defaultValue": {"value": "30"}, "description": "每增加step步长所需的毫秒数\n@en The number of milliseconds required to increase the step size", "name": "duration", "tags": {"en": "The number of milliseconds required to increase the step size", "default": "30"}, "descWithTags": "每增加step步长所需的毫秒数", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "number"}}, "step": {"defaultValue": {"value": "1"}, "description": "步长（增加步长，以step增长）\n@en Step size (increase the step size, grow by step)", "name": "step", "tags": {"en": "Step size (increase the step size, grow by step)", "default": "1"}, "descWithTags": "步长（增加步长，以step增长）", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "number"}}, "clockwise": {"defaultValue": {"value": "false"}, "description": "是否逆时针增加\n@en Whether to increase counterclockwise", "name": "clockwise", "tags": {"en": "Whether to increase counterclockwise", "default": "false"}, "descWithTags": "是否逆时针增加", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "boolean"}}, "radius": {"defaultValue": null, "description": "圆环半径\n@en Ring radius", "name": "radius", "tags": {"en": "Ring radius"}, "descWithTags": "圆环半径", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "number"}}, "filleted": {"defaultValue": {"value": "true"}, "description": "进度条两端是否圆角\n@en Whether the ends of the progress bar are rounded", "name": "filleted", "tags": {"en": "Whether the ends of the progress bar are rounded", "default": "true"}, "descWithTags": "进度条两端是否圆角", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "boolean"}}, "mountedTransition": {"defaultValue": {"value": "true"}, "description": "初始化percentage时是否以动画形式过渡到目的值\n@en Whether to animate the transition to the destination value when initializing percentage", "name": "mountedTransition", "tags": {"en": "Whether to animate the transition to the destination value when initializing percentage", "default": "true"}, "descWithTags": "初始化percentage时是否以动画形式过渡到目的值", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "boolean"}}, "size": {"defaultValue": {"value": "\"default\""}, "description": "进度环尺寸\n@en Progress ring size", "name": "size", "tags": {"en": "Progress ring size", "default": "\"default\""}, "descWithTags": "进度环尺寸", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "enum", "raw": "\"mini\" | \"default\"", "value": [{"value": "\"mini\""}, {"value": "\"default\""}]}}, "mountedBezier": {"defaultValue": {"value": "[0.34, 0.69, 0.1, 1]"}, "description": "初始化时动画的贝塞尔曲线\n@en Bezier curve of animation at initialization", "name": "mountedBezier", "tags": {"en": "Bezier curve of animation at initialization", "default": "[0.34, 0.69, 0.1, 1]"}, "descWithTags": "初始化时动画的贝塞尔曲线", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "[number, number, number, number]"}}, "svgKey": {"defaultValue": null, "description": "区分不同svg的`<def>`内容\n@en Distinguish `<def>` content of different svg", "name": "svgKey", "tags": {"en": "Distinguish `<def>` content of different svg"}, "descWithTags": "区分不同svg的`<def>`内容", "parent": {"fileName": "arcom-github/packages/arcodesign/components/circle-progress/index.tsx", "name": "CircleProgressProps"}, "required": false, "type": {"name": "string"}}, "ref": {"defaultValue": null, "description": "", "name": "ref", "tags": {}, "descWithTags": "", "parent": {"fileName": "arcom-github/node_modules/@types/react/index.d.ts", "name": "RefAttributes"}, "required": false, "type": {"name": "Ref<CircleProgressRef>"}}}, "deps": {"CircleProgressRef": {"dom": {"name": "dom", "required": true, "description": "最外层元素 DOM\n@en The outermost element DOM", "defaultValue": null, "type": {"name": "HTMLDivElement"}, "tags": {"en": "The outermost element DOM"}, "descWithTags": "最外层元素 DOM"}}}, "depComps": {}, "typeNameInfo": {"props": "CircleProgressProps", "ref": "CircleProgressRef"}, "isDefaultExport": true}