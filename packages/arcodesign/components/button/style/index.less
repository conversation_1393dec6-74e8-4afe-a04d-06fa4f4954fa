@import '../../../style/mixin.less';

@size-map: huge, large, medium, small, mini;

.button-has-border(@color) {
    .use-var(border, @color, 1PX solid);
    &.half-border {
        border-width: 0;
        .hairline-var(@color);
    }
}

.button-has-border-value(@value) {
    border: 1PX solid @value;
    &.half-border {
        border-width: 0;
        .hairline(@value);
    }
}

.button-no-border() {
    border-width: 0;
    box-shadow: none;
}

.button-text-size(@font-size) {
    .@{prefix}-button-icon,
    i,
    .@{prefix}-button-text,
    svg {
        .use-var(font-size, @font-size);
    }
}

.button-size-height(@height) {
    .use-var(height, @height);
    &-is-round {
        .use-var(border-radius, @height);
    }
    &-is-square {
        .rem(border-radius, 0);
    }
}

.@{prefix}-button {
    cursor: pointer;
    text-align: center;
    line-height: 1.2;
    font-size: 0;
    display: block;
    width: 100%;
    box-sizing: border-box;
    .use-var(line-height, button-line-height);
    .use-var(border-radius, button-radius);
    .noselect();

    &&-inline {
        width: auto;
        display: inline-block;
    }

    &&-type-primary {
        .use-var(background, button-primary-background);
        .use-var(color, button-primary-text-color);
        &-disabled {
            .use-var(background, button-primary-disabled-background);
            .use-var(color, button-primary-disabled-text-color)
        }
        &-active {
            .use-var(background, button-primary-clicked-background);
        }
    }
    &-type-primary {
        .set-loading-color-var(button-primary-text-color);
    }

    &&-type-default {
        .use-var(background, button-default-background);
        .use-var(color, button-default-text-color);
        &-disabled {
            .use-var(background, button-default-disabled-background);
            .use-var(color, button-default-disabled-text-color)
        }
        &-active {
            .use-var(background, button-default-clicked-background);
        }
    }
    &-type-default {
        .set-loading-color-var(button-default-text-color);
    }

    &&-type-ghost {
        .use-var(background, button-ghost-background);
        .use-var(color, button-ghost-text-color);
        &-disabled {
            .use-var(background, button-ghost-disabled-background);
            .use-var(color, button-ghost-disabled-text-color)
        }
        &-active {
            .use-var(background, button-ghost-clicked-background);
        }
    }
    &-type-ghost {
        .set-loading-color-var(button-ghost-text-color);
    }

    &&-type-ghost {
        .button-has-border-value(currentColor);
    }

    .set-button-size(length(@size-map));

    &-text-android {
        .rem(padding-top, 2);
    }

    &-icon {
        vertical-align: middle;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    &-text {
        display: inline-block;
        vertical-align: middle;
    }
    &-text-has-icon {
        .use-var-with-rtl(margin-left, button-icon-text-gutter);
    }

    &-loading-icon {
        display: inline-block;
        vertical-align: middle;
    }

    &.has-custom-border {
        border: 1PX solid;
    }
}

.set-button-size(@index) when (@index > 0) {
    @size: extract(@size-map, @index);

    &&-size-@{size} {
        .use-var(padding, "button-@{size}-padding");
        .button-size-height("button-@{size}-height");
        .button-text-size("button-@{size}-text-size");
    }

    .set-button-size(@index - 1);
}
