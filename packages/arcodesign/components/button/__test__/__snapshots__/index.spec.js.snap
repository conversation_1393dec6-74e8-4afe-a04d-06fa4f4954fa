// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`button demo test button demo: color.md renders correctly 1`] = `
<DocumentFragment>
  <button
    class="arco-button arco-button-type-primary type-primary arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-inline inline has-custom-border"
    style="border-color: #ff5722; background: rgb(255, 87, 34); margin-right: 20px;"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Button
    </div>
  </button>
  <button
    class="arco-button arco-button-type-ghost type-ghost arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-inline inline has-custom-border"
    style="color: rgb(255, 87, 34); border-color: #ff5722; background: rgb(255, 255, 255); margin-right: 20px;"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Button
    </div>
  </button>
  <button
    class="arco-button arco-button-type-default type-default arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-inline inline"
    style="color: white;"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Gradient
    </div>
  </button>
</DocumentFragment>
`;

exports[`button demo test button demo: default.md renders correctly 1`] = `
<DocumentFragment>
  <button
    class="arco-button arco-button-type-default type-default arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Auxiliary
    </div>
  </button>
  <button
    class="arco-button arco-button-type-default type-default arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-type-default-disabled disabled"
    style="margin-top: 20px;"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Disabled
    </div>
  </button>
</DocumentFragment>
`;

exports[`button demo test button demo: ghost.md renders correctly 1`] = `
<DocumentFragment>
  <button
    class="arco-button arco-button-type-ghost type-ghost arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Wireframe
    </div>
  </button>
  <button
    class="arco-button arco-button-type-ghost type-ghost arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-type-ghost-disabled disabled"
    style="margin-top: 20px;"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Disabled
    </div>
  </button>
</DocumentFragment>
`;

exports[`button demo test button demo: icon.md renders correctly 1`] = `
<DocumentFragment>
  <button
    class="arco-button arco-button-type-primary type-primary arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-inline inline"
    style="margin-right: 20px; width: 0.72rem; padding: 0px;"
    type="button"
  >
    <div
      class="arco-button-icon btn-icon"
    >
      <svg
        class="arco-icon arco-icon-setting "
        fill="currentColor"
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs />
        <path
          d="M512 46.55c34.517 0 68.565 3.797 101.696 11.263l19.136 4.288L692.48 198.08l146.837-16.043 13.27 14.443a465.493 465.493 0 01101.802 176.939l5.76 18.602-11.456 15.723L872.747 512l87.402 119.979-5.76 18.602A465.493 465.493 0 01852.587 827.52l-13.27 14.443L692.48 825.92l-59.648 135.979-19.136 4.288c-33.13 7.466-67.2 11.264-101.696 11.264-34.517 0-68.587-3.798-101.717-11.264l-19.136-4.31-7.894-17.941L331.52 825.92l-146.837 16.043-13.27-14.443A465.493 465.493 0 0169.611 650.56l-5.76-18.603 11.456-15.722L151.253 512 63.851 392.043l5.76-18.603a465.493 465.493 0 01101.802-176.96l13.27-14.443L331.52 198.08l59.627-135.957 19.136-4.31c33.13-7.466 67.2-11.264 101.717-11.264zm0 77.567a387.58 387.58 0 00-65.792 5.611L398.421 238.72a63.488 63.488 0 01-65.066 37.61l-117.483-12.863a387.947 387.947 0 00-66.048 114.837l70.165 96.32c16.214 22.272 16.214 52.48 0 74.752l-70.186 96.32a387.947 387.947 0 0066.069 114.837l117.504-12.864a63.488 63.488 0 0165.045 37.632l47.787 108.971a387.393 387.393 0 0065.792 5.61 387.33 387.33 0 0065.77-5.61L625.58 785.28a63.488 63.488 0 0165.066-37.61l117.483 12.863a387.947 387.947 0 0066.048-114.816l-70.165-96.341a63.488 63.488 0 010-74.752l70.165-96.341a387.947 387.947 0 00-66.048-114.816L690.624 276.33a63.488 63.488 0 01-65.045-37.611l-47.787-108.992a386.325 386.325 0 00-65.792-5.61zm0 193.942c106.752 0 193.195 86.869 193.195 193.941S618.752 705.92 512 705.92 318.805 619.093 318.805 512 405.248 318.08 512 318.08zm0 77.568c-63.808 0-115.627 52.053-115.627 116.373 0 64.299 51.819 116.373 115.627 116.373S627.627 576.32 627.627 512c0-64.299-51.819-116.373-115.627-116.373z"
        />
      </svg>
    </div>
  </button>
  <button
    class="arco-button arco-button-type-primary type-primary arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-inline inline"
    style="margin-right: 20px;"
    type="button"
  >
    <div
      class="arco-button-icon btn-icon"
    >
      <svg
        class="arco-icon arco-icon-scan "
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M85.333 320V128A42.667 42.667 0 01128 85.333h192a21.333 21.333 0 0121.333 21.334v42.666A21.333 21.333 0 01320 170.667H170.667V320a21.333 21.333 0 01-21.334 21.333h-42.666A21.333 21.333 0 0185.333 320zm85.334 384v149.333H320a21.333 21.333 0 0121.333 21.334v42.666A21.333 21.333 0 01320 938.667H128A42.667 42.667 0 0185.333 896V704a21.333 21.333 0 0121.334-21.333h42.666A21.333 21.333 0 01170.667 704zm746.666-362.667h-42.666A21.333 21.333 0 01853.333 320V170.667H704a21.333 21.333 0 01-21.333-21.334v-42.666A21.333 21.333 0 01704 85.333h192A42.667 42.667 0 01938.667 128v192a21.333 21.333 0 01-21.334 21.333zM938.667 704v192A42.667 42.667 0 01896 938.667H704a21.333 21.333 0 01-21.333-21.334v-42.666A21.333 21.333 0 01704 853.333h149.333V704a21.333 21.333 0 0121.334-21.333h42.666A21.333 21.333 0 01938.667 704zm-832-234.667h810.666a21.333 21.333 0 0121.334 21.334v42.666a21.333 21.333 0 01-21.334 21.334H106.667a21.333 21.333 0 01-21.334-21.334v-42.666a21.333 21.333 0 0121.334-21.334z"
          fill="currentColor"
        />
      </svg>
    </div>
    <div
      class="arco-button-text arco-button-text-pc btn-text arco-button-text-has-icon has-icon"
    >
      Button
    </div>
  </button>
  <button
    class="arco-button arco-button-type-ghost type-ghost arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-inline inline"
    type="button"
  >
    <div
      class="arco-button-icon btn-icon"
    >
      <svg
        class="arco-icon arco-icon-scan "
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M85.333 320V128A42.667 42.667 0 01128 85.333h192a21.333 21.333 0 0121.333 21.334v42.666A21.333 21.333 0 01320 170.667H170.667V320a21.333 21.333 0 01-21.334 21.333h-42.666A21.333 21.333 0 0185.333 320zm85.334 384v149.333H320a21.333 21.333 0 0121.333 21.334v42.666A21.333 21.333 0 01320 938.667H128A42.667 42.667 0 0185.333 896V704a21.333 21.333 0 0121.334-21.333h42.666A21.333 21.333 0 01170.667 704zm746.666-362.667h-42.666A21.333 21.333 0 01853.333 320V170.667H704a21.333 21.333 0 01-21.333-21.334v-42.666A21.333 21.333 0 01704 85.333h192A42.667 42.667 0 01938.667 128v192a21.333 21.333 0 01-21.334 21.333zM938.667 704v192A42.667 42.667 0 01896 938.667H704a21.333 21.333 0 01-21.333-21.334v-42.666A21.333 21.333 0 01704 853.333h149.333V704a21.333 21.333 0 0121.334-21.333h42.666A21.333 21.333 0 01938.667 704zm-832-234.667h810.666a21.333 21.333 0 0121.334 21.334v42.666a21.333 21.333 0 01-21.334 21.334H106.667a21.333 21.333 0 01-21.334-21.334v-42.666a21.333 21.333 0 0121.334-21.334z"
          fill="currentColor"
        />
      </svg>
    </div>
    <div
      class="arco-button-text arco-button-text-pc btn-text arco-button-text-has-icon has-icon"
    >
      Icon button
    </div>
  </button>
</DocumentFragment>
`;

exports[`button demo test button demo: loading.md renders correctly 1`] = `
<DocumentFragment>
  <button
    class="arco-button arco-button-type-primary type-primary arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-inline inline loading"
    style="margin-right: 11px;"
    type="button"
  >
    <div
      class="arco-button-icon btn-icon"
    >
      <div
        class="arco-loading all-border-box circle arco-button-loading-icon loading-icon"
        style="animation-duration: 1000ms; width: 14px; height: 14px;"
      >
        <svg
          viewBox="0 0 14 14"
        >
          <defs>
            <lineargradient
              id="grad1-inner-0"
              x1="0%"
              x2="100%"
              y1="0%"
              y2="0%"
            >
              <stop
                class="loading-circle-middle stop-color-with-config"
                offset="0%"
              />
              <stop
                class="loading-circle-start stop-color-with-config"
                offset="100%"
              />
            </lineargradient>
            <lineargradient
              id="grad2-inner-0"
              x1="0%"
              x2="100%"
              y1="0%"
              y2="0%"
            >
              <stop
                class="loading-circle-middle stop-color-with-config"
                offset="0%"
              />
              <stop
                class="loading-circle-end stop-color-with-config"
                offset="100%"
              />
            </lineargradient>
          </defs>
          <circle
            cx="7"
            cy="7"
            fill="none"
            r="6"
            stroke="url(#grad1-inner-0)"
            stroke-dasharray="18.84955592153876"
            stroke-dashoffset="18.84955592153876"
            stroke-width="2"
          />
          <circle
            cx="7"
            cy="7"
            fill="none"
            r="6"
            stroke="url(#grad2-inner-0)"
            stroke-dasharray="18.84955592153876"
            stroke-width="2"
          />
          <circle
            class="loading-circle-filleted fill-color-with-config"
            cx="13"
            cy="7"
            r="1"
          />
        </svg>
      </div>
    </div>
    <div
      class="arco-button-text arco-button-text-pc btn-text arco-button-text-has-icon has-icon"
    >
      Primary
    </div>
  </button>
  <button
    class="arco-button arco-button-type-ghost type-ghost arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-inline inline loading"
    type="button"
  >
    <div
      class="arco-button-icon btn-icon"
    >
      <div
        class="arco-loading all-border-box circle arco-button-loading-icon loading-icon"
        style="animation-duration: 1000ms; width: 14px; height: 14px;"
      >
        <svg
          viewBox="0 0 14 14"
        >
          <defs>
            <lineargradient
              id="grad1-inner-1"
              x1="0%"
              x2="100%"
              y1="0%"
              y2="0%"
            >
              <stop
                class="loading-circle-middle stop-color-with-config"
                offset="0%"
              />
              <stop
                class="loading-circle-start stop-color-with-config"
                offset="100%"
              />
            </lineargradient>
            <lineargradient
              id="grad2-inner-1"
              x1="0%"
              x2="100%"
              y1="0%"
              y2="0%"
            >
              <stop
                class="loading-circle-middle stop-color-with-config"
                offset="0%"
              />
              <stop
                class="loading-circle-end stop-color-with-config"
                offset="100%"
              />
            </lineargradient>
          </defs>
          <circle
            cx="7"
            cy="7"
            fill="none"
            r="6"
            stroke="url(#grad1-inner-1)"
            stroke-dasharray="18.84955592153876"
            stroke-dashoffset="18.84955592153876"
            stroke-width="2"
          />
          <circle
            cx="7"
            cy="7"
            fill="none"
            r="6"
            stroke="url(#grad2-inner-1)"
            stroke-dasharray="18.84955592153876"
            stroke-width="2"
          />
          <circle
            class="loading-circle-filleted fill-color-with-config"
            cx="13"
            cy="7"
            r="1"
          />
        </svg>
      </div>
    </div>
    <div
      class="arco-button-text arco-button-text-pc btn-text arco-button-text-has-icon has-icon"
    >
      Secondary
    </div>
  </button>
  <button
    class="arco-button arco-button-type-ghost type-ghost arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-inline inline"
    style="margin-top: 20px;"
    type="button"
  >
    <div
      class="arco-button-icon btn-icon"
    >
      <svg
        class="arco-icon arco-icon-setting "
        fill="currentColor"
        height="1em"
        viewBox="0 0 1024 1024"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs />
        <path
          d="M512 46.55c34.517 0 68.565 3.797 101.696 11.263l19.136 4.288L692.48 198.08l146.837-16.043 13.27 14.443a465.493 465.493 0 01101.802 176.939l5.76 18.602-11.456 15.723L872.747 512l87.402 119.979-5.76 18.602A465.493 465.493 0 01852.587 827.52l-13.27 14.443L692.48 825.92l-59.648 135.979-19.136 4.288c-33.13 7.466-67.2 11.264-101.696 11.264-34.517 0-68.587-3.798-101.717-11.264l-19.136-4.31-7.894-17.941L331.52 825.92l-146.837 16.043-13.27-14.443A465.493 465.493 0 0169.611 650.56l-5.76-18.603 11.456-15.722L151.253 512 63.851 392.043l5.76-18.603a465.493 465.493 0 01101.802-176.96l13.27-14.443L331.52 198.08l59.627-135.957 19.136-4.31c33.13-7.466 67.2-11.264 101.717-11.264zm0 77.567a387.58 387.58 0 00-65.792 5.611L398.421 238.72a63.488 63.488 0 01-65.066 37.61l-117.483-12.863a387.947 387.947 0 00-66.048 114.837l70.165 96.32c16.214 22.272 16.214 52.48 0 74.752l-70.186 96.32a387.947 387.947 0 0066.069 114.837l117.504-12.864a63.488 63.488 0 0165.045 37.632l47.787 108.971a387.393 387.393 0 0065.792 5.61 387.33 387.33 0 0065.77-5.61L625.58 785.28a63.488 63.488 0 0165.066-37.61l117.483 12.863a387.947 387.947 0 0066.048-114.816l-70.165-96.341a63.488 63.488 0 010-74.752l70.165-96.341a387.947 387.947 0 00-66.048-114.816L690.624 276.33a63.488 63.488 0 01-65.045-37.611l-47.787-108.992a386.325 386.325 0 00-65.792-5.61zm0 193.942c106.752 0 193.195 86.869 193.195 193.941S618.752 705.92 512 705.92 318.805 619.093 318.805 512 405.248 318.08 512 318.08zm0 77.568c-63.808 0-115.627 52.053-115.627 116.373 0 64.299 51.819 116.373 115.627 116.373S627.627 576.32 627.627 512c0-64.299-51.819-116.373-115.627-116.373z"
        />
      </svg>
    </div>
    <div
      class="arco-button-text arco-button-text-pc btn-text arco-button-text-has-icon has-icon"
    >
      Secondary
    </div>
  </button>
</DocumentFragment>
`;

exports[`button demo test button demo: pass-button.md renders correctly 1`] = `
<DocumentFragment>
  <button
    class="arco-button arco-button-type-primary type-primary arco-button-size-huge arco-button-size-huge-is-semi size-huge is-semi pc"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Banner
    </div>
  </button>
</DocumentFragment>
`;

exports[`button demo test button demo: primary.md renders correctly 1`] = `
<DocumentFragment>
  <button
    class="arco-button arco-button-type-primary type-primary arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Primary
    </div>
  </button>
  <button
    class="arco-button arco-button-type-primary type-primary arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-type-primary-disabled disabled"
    style="margin-top: 20px;"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Disabled
    </div>
  </button>
</DocumentFragment>
`;

exports[`button demo test button demo: round.md renders correctly 1`] = `
<DocumentFragment>
  <div
    style="display: flex; justify-content: space-between;"
  >
    <button
      class="arco-button arco-button-type-primary type-primary arco-button-size-large arco-button-size-large-is-round size-large is-round pc arco-button-inline inline"
      style="margin-right: 20px; flex: 1;"
      type="button"
    >
      <div
        class="arco-button-text arco-button-text-pc btn-text"
      >
        Rounded
      </div>
    </button>
    <button
      class="arco-button arco-button-type-primary type-primary arco-button-size-large arco-button-size-large-is-square size-large is-square pc arco-button-inline inline"
      style="flex: 1;"
      type="button"
    >
      <div
        class="arco-button-text arco-button-text-pc btn-text"
      >
        Right angle
      </div>
    </button>
  </div>
</DocumentFragment>
`;

exports[`button demo test button demo: size.md renders correctly 1`] = `
<DocumentFragment>
  <button
    class="arco-button arco-button-type-primary type-primary arco-button-size-huge arco-button-size-huge-is-semi size-huge is-semi pc"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Huge button
    </div>
  </button>
  <button
    class="arco-button arco-button-type-primary type-primary arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-inline inline"
    style="margin-right: 40px; margin-top: 20px;"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
       Regular
    </div>
  </button>
  <button
    class="arco-button arco-button-type-primary type-primary arco-button-size-medium arco-button-size-medium-is-semi size-medium is-semi pc arco-button-inline inline"
    style="margin-right: 40px;"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Middle
    </div>
  </button>
  <button
    class="arco-button arco-button-type-primary type-primary arco-button-size-small arco-button-size-small-is-semi size-small is-semi pc arco-button-inline inline"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Small button
    </div>
  </button>
  <button
    class="arco-button arco-button-type-primary type-primary arco-button-size-mini arco-button-size-mini-is-semi size-mini is-semi pc arco-button-inline inline"
    style="margin-top: 20px;"
    type="button"
  >
    <div
      class="arco-button-text arco-button-text-pc btn-text"
    >
      Mini
    </div>
  </button>
</DocumentFragment>
`;

exports[`button demo test button demo: type.md renders correctly 1`] = `
<DocumentFragment>
  <div
    class="button-inline-flex"
  >
    <button
      class="arco-button arco-button-type-primary type-primary arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc"
      type="button"
    >
      <div
        class="arco-button-text arco-button-text-pc btn-text"
      >
        Information
      </div>
    </button>
    <button
      class="arco-button arco-button-type-primary type-primary arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-type-primary-disabled disabled"
      type="button"
    >
      <div
        class="arco-button-text arco-button-text-pc btn-text"
      >
        Disabled
      </div>
    </button>
  </div>
  <div
    class="button-inline-flex"
  >
    <button
      class="arco-button arco-button-type-danger type-danger arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc has-custom-border"
      style="color: rgb(255, 255, 255); border-color: #f53f3f; background: rgb(245, 63, 63);"
      type="button"
    >
      <div
        class="arco-button-text arco-button-text-pc btn-text"
      >
        Information
      </div>
    </button>
    <button
      class="arco-button arco-button-type-danger type-danger arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-type-danger-disabled disabled has-custom-border"
      style="color: rgb(255, 236, 232); border-color: #fbaca3; background: rgb(251, 172, 163);"
      type="button"
    >
      <div
        class="arco-button-text arco-button-text-pc btn-text"
      >
        Disabled
      </div>
    </button>
  </div>
  <div
    class="button-inline-flex"
  >
    <button
      class="arco-button arco-button-type-warning type-warning arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc has-custom-border"
      style="color: rgb(255, 255, 255); border-color: #ff7d00; background: rgb(255, 125, 0);"
      type="button"
    >
      <div
        class="arco-button-text arco-button-text-pc btn-text"
      >
        Information
      </div>
    </button>
    <button
      class="arco-button arco-button-type-warning type-warning arco-button-size-large arco-button-size-large-is-semi size-large is-semi pc arco-button-type-warning-disabled disabled has-custom-border"
      style="color: rgb(255, 247, 232); border-color: #ffcf8b; background: rgb(255, 207, 139);"
      type="button"
    >
      <div
        class="arco-button-text arco-button-text-pc btn-text"
      >
        Disabled
      </div>
    </button>
  </div>
</DocumentFragment>
`;
