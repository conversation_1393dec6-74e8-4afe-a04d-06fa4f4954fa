# yk-common 独立测试环境

## 📍 位置信息

- **当前位置**: `/mnt/d/source/gitlab/yk-common-standalone-test/`
- **独立性**: 完全独立于arcoxc项目，与arcoxc项目同级目录
- **安全性**: 不会修改arcoxc项目的任何文件

## 🎯 特性优势

- ✅ **模块化设计**：使用ES模块和CommonJS模块，代码结构清晰
- ✅ **零配置**：无需安装任何依赖
- ✅ **多平台支持**：浏览器和Node.js环境
- ✅ **代码复用**：函数分离，易于维护和扩展
- ✅ **工具函数**：新增验证和工具函数
- ✅ **全面测试**：功能测试、格式验证、边界测试
- ✅ **可视化界面**：美观的HTML测试页面
- ✅ **命令行工具**：Node.js脚本支持

## 🚀 快速使用

### 方法1: 浏览器测试（推荐）
```bash
# 模块化版本（推荐）
index-modular.html

# 内联版本
index.html
```

### 方法2: Node.js测试
```bash
# 运行演示（模块化版本）
node test-demo.js
```

## 🎯 功能验证

### 基础功能测试
```javascript
// 最小参数
generateBusinessRequestNo(BusinessType.PAYMENT, 12345)
// 结果: PAYMENT_20250701143023_12345_338481

// 完整参数
generateBusinessRequestNo(BusinessType.APPLYMENT, 67890, MerchantType.ENTERPRISE, "BATCH01")
// 结果: APPLYMENT_20250701143023_ENT_67890_BATCH01_719912

// 商户进件申请
generateOutRequestNo(MerchantType.INDIVIDUAL, 11111)
// 结果: APPLYMENT_20250701143023_IND_11111_565448
```

## 📁 文件说明

### 核心模块
- `yk-common.js` - CommonJS模块版本（Node.js使用）
- `yk-common.mjs` - ES模块版本（浏览器使用）

### 测试文件
- `index.html` - 浏览器可视化测试页面（内联版本）
- `index-modular.html` - 浏览器可视化测试页面（模块化版本）
- `test-demo.js` - Node.js命令行测试脚本（模块化版本）

### 文档
- `README.md` - 使用说明文档

## ✅ 验证清单

- [x] 基础功能正常
- [x] 所有业务类型支持
- [x] 所有商户类型支持
- [x] 时间戳格式正确
- [x] 随机数范围正确
- [x] 完全独立于原项目

## 🎉 总结

这个独立测试环境：
- ✅ 位于arcoxc项目外部
- ✅ 不依赖原项目任何文件
- ✅ 不会修改原项目任何文件
- ✅ 可以安全删除而不影响原项目
- ✅ 提供完整的函数测试验证

测试通过后，可以确信yk-common中的业务编号生成函数实现完全正确！
