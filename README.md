# taro+react+arco-design项目

#### 介绍
taro+react+arco-design项目:
基于taro：react框架开发、arco-design UI组件库开发的H5移动端项目，app内嵌H5

#### 软件架构
taro4+react18+arco-design


#### 安装教程

1.  npm i
2.  npm run dev:h5

#### 使用说明

1.  arco-iconbox图标引入以及使用方式：
    
    import {IconArrowUp} from "@arco-iconbox/react-yk-arco";

    <IconArrowUp className="ykIconClass" />

    .ykIconClass [fill] {
        fill: var(--primary-color);
        .use-dark-mode-query({
            fill: var(--dark-primary-color);
        });
    }
    .ykIconClass [stroke] {
        stroke: var(--primary-color);
        .use-dark-mode-query({
            stroke: var(--dark-primary-color);
        });
    }

    注：1、通过设置stroke，fill用来设置图标的样式。
        2、由面性和线性的图标组合的图标不适合通过此类方式设置图标颜色。
        3、图标大小可通过设置字体大小来控制




