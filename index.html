<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>yk-common 独立测试环境</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; text-align: center; border-radius: 8px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        button { background: #3498db; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #2980b9; }
        .output { background: #2c3e50; color: #ecf0f1; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }
        .info { background: #d4edda; border: 1px solid #28a745; padding: 15px; border-radius: 4px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 yk-common 独立测试环境</h1>
        <p>完全独立的业务编号生成函数测试工具</p>
    </div>
    
    <div class="info">
        <h4>📍 独立测试环境</h4>
        <p><strong>当前位置：</strong>/mnt/d/source/gitlab/yk-common-standalone-test/</p>
        <p><strong>独立性：</strong>完全独立于arcoxc项目，与arcoxc项目同级目录</p>
        <p><strong>安全性：</strong>不会修改arcoxc项目的任何文件</p>
    </div>
    
    <div class="section">
        <h3>🚀 快速测试</h3>
        <button onclick="runDemo()">快速演示</button>
        <button onclick="runTests()">运行测试</button>
        <button onclick="clearOutput()">清空输出</button>
    </div>
    
    <div class="section">
        <h3>💻 测试输出</h3>
        <div class="output" id="output">点击上方按钮开始测试...</div>
    </div>
    
    <script>
        // yk-common 函数实现
        const BusinessType = {
            APPLYMENT: 'APPLYMENT',
            PAYMENT: 'PAYMENT',
            REFUND: 'REFUND',
            WITHDRAW: 'WITHDRAW',
            TRANSFER: 'TRANSFER',
            SETTLEMENT: 'SETTLEMENT',
        };
        
        const MerchantType = {
            ENTERPRISE: 0,
            INDIVIDUAL: 1,
            PERSONAL: 2
        };
        
        function generateBusinessRequestNo(businessType, userId, merchantType, customSuffix) {
            const now = new Date();
            const timestamp = now.getFullYear().toString() +
                (now.getMonth() + 1).toString().padStart(2, '0') +
                now.getDate().toString().padStart(2, '0') +
                now.getHours().toString().padStart(2, '0') +
                now.getMinutes().toString().padStart(2, '0') +
                now.getSeconds().toString().padStart(2, '0');
            
            const typeCode = merchantType !== undefined ? {
                [MerchantType.ENTERPRISE]: 'ENT',
                [MerchantType.INDIVIDUAL]: 'IND',
                [MerchantType.PERSONAL]: 'PER'
            }[merchantType] || 'UNK' : '';
            
            const randomNum = Math.floor(Math.random() * 900000) + 100000;
            const parts = [businessType, timestamp];
            
            if (typeCode) parts.push(typeCode);
            parts.push(userId.toString());
            if (customSuffix) parts.push(customSuffix);
            parts.push(randomNum.toString());
            
            return parts.join('_');
        }
        
        function generateOutRequestNo(merchantType, userId) {
            return generateBusinessRequestNo(BusinessType.APPLYMENT, userId, merchantType);
        }
        
        function appendOutput(text) {
            const output = document.getElementById('output');
            output.textContent += text + '\n';
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('output').textContent = '';
        }
        
        function runDemo() {
            clearOutput();
            appendOutput('🎯 yk-common 业务编号生成演示');
            appendOutput('📍 位置: /mnt/d/source/gitlab/yk-common-standalone-test/');
            appendOutput('🔒 完全独立于arcoxc项目\n');
            
            appendOutput('1. 基础用法:');
            const result1 = generateBusinessRequestNo(BusinessType.PAYMENT, 12345);
            appendOutput('generateBusinessRequestNo(BusinessType.PAYMENT, 12345)');
            appendOutput('结果: ' + result1);
            
            appendOutput('\n2. 完整参数:');
            const result2 = generateBusinessRequestNo(BusinessType.APPLYMENT, 67890, MerchantType.ENTERPRISE, 'BATCH01');
            appendOutput('generateBusinessRequestNo(BusinessType.APPLYMENT, 67890, MerchantType.ENTERPRISE, "BATCH01")');
            appendOutput('结果: ' + result2);
            
            appendOutput('\n3. 商户进件申请:');
            const result3 = generateOutRequestNo(MerchantType.INDIVIDUAL, 11111);
            appendOutput('generateOutRequestNo(MerchantType.INDIVIDUAL, 11111)');
            appendOutput('结果: ' + result3);
            
            appendOutput('\n✨ 演示完成！');
        }
        
        function runTests() {
            clearOutput();
            appendOutput('🚀 开始运行基础测试\n');
            
            let passed = 0;
            let failed = 0;
            
            // 测试1: 基础功能
            try {
                const result = generateBusinessRequestNo(BusinessType.PAYMENT, 12345);
                if (result.startsWith('PAYMENT_') && result.includes('_12345_')) {
                    appendOutput('✅ 基础功能测试通过');
                    passed++;
                } else {
                    appendOutput('❌ 基础功能测试失败');
                    failed++;
                }
            } catch (error) {
                appendOutput('❌ 基础功能测试错误: ' + error.message);
                failed++;
            }
            
            // 测试2: 完整参数
            try {
                const result = generateBusinessRequestNo(BusinessType.APPLYMENT, 67890, MerchantType.ENTERPRISE, 'BATCH01');
                if (result.includes('APPLYMENT_') && result.includes('_ENT_') && result.includes('_67890_') && result.includes('_BATCH01_')) {
                    appendOutput('✅ 完整参数测试通过');
                    passed++;
                } else {
                    appendOutput('❌ 完整参数测试失败');
                    failed++;
                }
            } catch (error) {
                appendOutput('❌ 完整参数测试错误: ' + error.message);
                failed++;
            }
            
            // 测试3: 商户进件申请
            try {
                const result = generateOutRequestNo(MerchantType.INDIVIDUAL, 11111);
                if (result.startsWith('APPLYMENT_') && result.includes('_IND_') && result.includes('_11111_')) {
                    appendOutput('✅ 商户进件申请测试通过');
                    passed++;
                } else {
                    appendOutput('❌ 商户进件申请测试失败');
                    failed++;
                }
            } catch (error) {
                appendOutput('❌ 商户进件申请测试错误: ' + error.message);
                failed++;
            }
            
            const total = passed + failed;
            appendOutput('\n📊 测试结果汇总:');
            appendOutput('总测试数: ' + total);
            appendOutput('通过数: ' + passed);
            appendOutput('失败数: ' + failed);
            appendOutput('通过率: ' + ((passed / total) * 100).toFixed(1) + '%');
            
            if (failed === 0) {
                appendOutput('\n🎉 所有测试通过！');
            } else {
                appendOutput('\n⚠️ 部分测试失败');
            }
        }
        
        // 添加到全局对象
        window.generateBusinessRequestNo = generateBusinessRequestNo;
        window.generateOutRequestNo = generateOutRequestNo;
        window.BusinessType = BusinessType;
        window.MerchantType = MerchantType;
        
        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            console.log('🔧 yk-common 独立测试环境已加载');
            console.log('📍 位置: /mnt/d/source/gitlab/yk-common-standalone-test/');
            console.log('🔒 完全独立于arcoxc项目');
            console.log('💡 可以在控制台中调用函数进行测试');
        });
    </script>
</body>
</html>
