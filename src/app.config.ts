export default defineAppConfig({
  pages: [
    'pages/login/index',
    'pages/index/index',
    "pages/my/index",
    'pages/editUserInfoItem/index',   
    'pages/editUserInfo/index',   
    'pages/login/index',
    'pages/phoneLogin/index',
    'pages/codeLogin/index',
    'pages/codeLogin/code',
    'pages/settings/index',
    'pages/resetPassword/index',
    "pages/bindMobilePhone/index",
    "pages/loginedResetPassword/index",
    "pages/bindMobilePhone/index",
    "pages/webView/index",
    "pages/aboutUs/index",
    "pages/PICC/index",
    "pages/PICCAvatar/index",
    "pages/PICCNickName/index",
    "pages/PICCPhone/index",
    "pages/colorSettings/index",
    "pages/arcoIconShow/index",
    "pages/permissionSetting/index",
    "pages/control/index",
    "pages/friends/index",
    "pages/message/index",
    "pages/my/mobileCustomerServer/index",
    "pages/my/qrcode/index",
    "pages/my/album/index",
    "pages/my/album/batchDelete/index",
    "pages/my/album/upAndDownShelves/index",
    "pages/my/album/classDynamic/index",
    "pages/my/album/classDynamic/batchShare/index",
    "pages/releaseDynamic/selectTag/index",
    "pages/releaseDynamic/selectProductColor/index",
    "pages/order/index",
    "pages/order/details/index",
    "pages/sellOrder/index",
    "pages/sellOrder/details/index",
    "pages/confirmOrder/index",
    "pages/cart/index",
    "pages/order/pay/index",
    "pages/order/pay/address",
    //"pages/my/album/albumDetail/index",
    "pages/detail/index",
    "pages/releaseDynamic/index",
    "pages/albumManage/index",
    "pages/albumManage/tagManage/index",
    "pages/albumManage/tagManage/tag/index",
    "pages/albumManage/tagManage/tag/createTag",
    "pages/albumManage/tagManage/tag/editTag",
    "pages/albumManage/tagManage/tag/setSort",
    "pages/albumManage/tagManage/tag/addContent",
    "pages/albumManage/tagManage/catalog/index",
    "pages/albumManage/tagManage/catalog/createCatalog",
    "pages/albumManage/tagManage/catalog/addTag",
    "pages/albumManage/tagManage/catalog/editCatalog",
    "pages/moveMaterials/selectMaterials/index",
    "pages/moveMaterials/selectMaterials/edit",
    "pages/moveMaterials/collectLink/index",
    "pages/orderSetting/index",
    "pages/orderSetting/deliveryManager/index",
    "pages/orderSetting/emsTemplate/index",
    "pages/orderSetting/emsTemplate/editEmsTemplate",
    "pages/orderSetting/emsTemplate/emsArea",
    "pages/orderSetting/emsTemplate/emsRule",
    "pages/followUser/index",
    "pages/onlinePayment/index",
    "pages/onlinePayment/merchantApplication/index",
    "pages/onlinePayment/information/index",
    "pages/onlinePayment/information/editContact",
    "pages/onlinePayment/information/editSettlement",
    "pages/onlinePayment/verifyAccount",
    "pages/onlinePayment/signAgreement",
    "pages/vipCenter/index",
    "pages/vipCenter/paySuccess",
    "pages/vipCenter/payOrder",
    "pages/wallet/index",
    "pages/wallet/withdrawal/index",
    "pages/wallet/withdrawal/result",
    "pages/wallet/withdrawal/history",
    "pages/wallet/withdrawal/updateAccount",
    "pages/wallet/transactions/history",
    "pages/wallet/transactions/detail",
    "pages/wallet/help",
    "pages/debug/test"
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'WeChat',
    navigationBarTextStyle: 'black',
    onReachBottomDistance: 80
  },
  animation: false
})
