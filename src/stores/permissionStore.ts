import { atom,selector, selectorFamily, useSetRecoilState, useRecoilValue  } from 'recoil';
import { AuthTypes,AuthStatusIos,AuthStatusAndroid } from "@/utils/config/authTypes";
// 初始权限数组
const initialPermissionsArray = window.navigator.userAgent.indexOf(`android_${APP_NAME}`) > -1?[
  { imgPath: 'IconCamera', name: '相机', state: false, authType: AuthTypes.CAMERA ,currentStatus:0},
  { imgPath: 'IconFileImage', name: '相册图片', state: true, authType: AuthTypes.GALLERY_PHOTO ,currentStatus:0},
  { imgPath: 'IconFileVideo', name: '相册视频', state: false, authType: AuthTypes.GALLERY_VIDEO ,currentStatus:0},
  { imgPath: 'IconFileAudio', name: '相册音频', state: false, authType: AuthTypes.GALLERY_AUDIO ,currentStatus:0},
  { imgPath: 'IconStorage', name: '低版本相册权限（存储）', state: false, authType: AuthTypes.STORAGE ,currentStatus:0},
  { imgPath: 'IconVoice', name: '麦克风', state: false, authType: AuthTypes.AUDIO ,currentStatus:0},
  { imgPath: 'IconNotification', name: '通知', state: false, authType: AuthTypes.NOTICE ,currentStatus:0},
  { imgPath: 'IconLocation', name: '定位', state: false, authType: AuthTypes.LOCATION ,currentStatus:0},
  { imgPath: 'IconMobile', name: '通讯录', state: false, authType: AuthTypes.CONTACT ,currentStatus:0},
  { imgPath: 'IconRecord', name: '悬浮窗', state: false, authType: AuthTypes.FLOATWIN ,currentStatus:0},
  { imgPath: 'IconEye', name: '无障碍服务', state: false, authType: AuthTypes.ACCESS ,currentStatus:0},
  { imgPath: 'IconShake', name: '自启动权限', state: false, authType: AuthTypes.AUTOSTART ,currentStatus:0},
]:[
  { imgPath: 'IconCamera', name: '相机', state: false, authType: AuthTypes.CAMERA ,currentStatus:0},
  { imgPath: 'IconStorage', name: '存储', state: false, authType: AuthTypes.STORAGE ,currentStatus:0},
  { imgPath: 'IconVoice', name: '麦克风', state: false, authType: AuthTypes.AUDIO ,currentStatus:0},
  { imgPath: 'IconFileImage', name: '相册图片', state: true, authType: AuthTypes.GALLERY_PHOTO ,currentStatus:0},
  // { imgPath: 'IconNotification', name: '通知', state: false, authType: AuthTypes.NOTICE ,currentStatus:0},
  { imgPath: 'IconLocation', name: '定位', state: false, authType: AuthTypes.LOCATION ,currentStatus:0},
  { imgPath: 'IconMobile', name: '通讯录', state: false, authType: AuthTypes.CONTACT ,currentStatus:0},
  { imgPath: 'IconWifi', name: '本地网络', state: false, authType: AuthTypes.LOCALNET ,currentStatus:0},
];

const keyToAuthType = {
  openCamera: AuthTypes.CAMERA,
  openGalleryPhoto: AuthTypes.GALLERY_PHOTO,
  openGalleryVideo: AuthTypes.GALLERY_VIDEO,
  openGalleryAudio: AuthTypes.GALLERY_AUDIO,
  openStorage: AuthTypes.STORAGE,
  openAudio: AuthTypes.AUDIO,
  openNotice: AuthTypes.NOTICE,
  openLocation: AuthTypes.LOCATION,
  openContacts: AuthTypes.CONTACT,
  openFloatingWindow: AuthTypes.FLOATWIN,
  openAccessibility: AuthTypes.ACCESS,
  openAutoStarting: AuthTypes.AUTOSTART,
  openLocalNet: AuthTypes.LOCALNET,
}; 

// 权限状态 atom
export const permissionsAtomArray = atom({
  key: 'permissionsAtomArray',
  default: initialPermissionsArray,
});

// 检查单个权限
export const hasPermissionSelector = selectorFamily({
  key: 'hasPermissionSelector',
  get: (authType) => ({ get }) => {
    const permissions = get(permissionsAtomArray);
    const permission = permissions.find((perm) => perm.authType === authType);
    return permission ? permission.state : false;
  },
});


// 更新权限函数
export const useSetPermission = () => {
  const setPermissions = useSetRecoilState(permissionsAtomArray);

  return async (newPermissions) => {
    if (!newPermissions || typeof newPermissions !== "object") {
      console.error("Invalid permissions object provided.");
      return;
    }

    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;

    return new Promise((resolve) => {
      setPermissions((prevState) => {
        const updatedState = prevState.map((perm) => {
          // 使用 keyToAuthType 来获取每个权限的 authType
          const newState = newPermissions[Object.keys(keyToAuthType).find(key => keyToAuthType[key] === perm.authType)];
           // 根据平台解析权限状态
           let parsedState = perm.state; // 默认保留原状态
           if (isAndroid) {
            // parsedState = typeof newState === "boolean" ? newState : perm.state;
            parsedState = [AuthStatusAndroid.AUTHORIZED].includes(newState) ? true : [AuthStatusAndroid.DENIEDANDNOTASK, AuthStatusAndroid.DENIED].includes(newState) ? false : perm.state;
          } else if (isIos) {
            parsedState = [AuthStatusIos.AUTHORIZED, AuthStatusIos.LIMIT].includes(newState) ? true : [AuthStatusIos.NOTDETERMINED, AuthStatusIos.RESTRICTED, AuthStatusIos.DENIED].includes(newState) ? false : perm.state;
          }

          return {
            ...perm,
            state: parsedState,
            currentStatus:newState !== undefined ? newState : perm.currentStatus
          };
        });
        resolve(updatedState); // 返回更新后的状态
        return updatedState;
      });
    });
  };
};

// 更新指定权限状态
export const updatePermissionByAuthType = (authType, newState) => {
  const setPermissions = useSetRecoilState(permissionsAtomArray);

  setPermissions((prevState) =>
    prevState.map((perm) =>
      perm.authType === authType
        ? { ...perm, state: newState }
        : perm
    )
  );
};

// Hook: 检查权限
export const useHasPermission = (authType) => {
  return useRecoilValue(hasPermissionSelector(authType));
};

// 查询所有权限
export const allPermissionsSelector = selector({
  key: 'allPermissionsSelector',
  get: ({ get }) => {
    return get(permissionsAtomArray);
  },
});
 