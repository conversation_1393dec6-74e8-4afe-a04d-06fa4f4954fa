import { PropsWithChildren } from "react";
import { useLaunch } from "@tarojs/taro";
import PageAnimation from "./components/PageAnimation";

import Arco from "@arco-design/mobile-react";
import setRootPixel from "@arco-design/mobile-react/tools/flexible";
import "@arco-design/mobile-react/esm/style";
import React from "react";
import { ContextProvider } from "@arco-design/mobile-react";
import VConsole from 'vconsole';
setRootPixel(20);
import "./app.less";
import Taro from "@tarojs/taro";
import { RecoilRoot } from "recoil";
// 导入路由拦截器
import './utils/router.js';
// 导入键盘兼容性工具
import { keyboardCompatibility } from './utils/keyboardCompatibility';

new VConsole();
function App({ children }: PropsWithChildren<any>) {
  const [httpPlatform, setHttpPlatform] = React.useState("H5");
  // 暗黑模式
  const [darkMode, setDarkMode] = React.useState(false);

  // 主题
  const [theme, setTheme] = React.useState<any>({});


  useLaunch(() => {
    let _window: any = window;

    // 初始化键盘兼容性工具
    if (typeof window !== 'undefined') {
      // 启用调试模式（开发环境）
      const isDebugMode = process.env.NODE_ENV === 'development';
      keyboardCompatibility.init(isDebugMode);

      // 输出键盘方法状态
      console.log('=== 键盘兼容性状态 ===');
      const keyboardStatus = keyboardCompatibility.checkKeyboardMethods();
      console.table(keyboardStatus);
    }

    let t_darkMode = !!Taro.getStorageSync("darkMode")
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    if (isAndroid) {
      if (t_darkMode) {
        console.log("httpPlatform StatusBarDarkMode");
        _window.StatusBarLightMode.StatusBarLightMode();

      } else {
        console.log("httpPlatform StatusBarLightMode");
        _window.StatusBarDarkMode.StatusBarDarkMode();

      }
    } else if (isIos) {
      if (t_darkMode) {
        _window.webkit.messageHandlers.configStatusBarStyle.postMessage(
          "white"
        );
      } else {
        _window.webkit.messageHandlers.configStatusBarStyle.postMessage(
          "black"
        );
      }
    } else {
      setHttpPlatform("H5");
    }
    getInfo();
  });

  const getInfo = () => {
    setDarkMode(!!Taro.getStorageSync("darkMode"));
  };

  React.useEffect(() => {
    setTheme(Taro.getStorageSync("theme"));
    console.log("theme", theme);

    // 键盘兼容性工具已经处理了相关错误，这里只需要记录其他错误
    const handleError = (event: ErrorEvent) => {
      console.error('Global error caught:', event.error);
      // 键盘相关错误已由 keyboardCompatibility 处理
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, [children]);

  return (
    <RecoilRoot>
      <ContextProvider
        theme={theme}
        isDarkMode={darkMode}
        darkModeSelector="arco-theme-dark"
      >
        {/* <PageAnimation> */}
          {children}
        {/* </PageAnimation> */}
      </ContextProvider>
    </RecoilRoot>
  );
}

export default App;
