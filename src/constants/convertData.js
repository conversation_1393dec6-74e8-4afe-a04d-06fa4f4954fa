const fs = require('fs');
const path = require('path');

// 读取原始数据
const areaJs = require('./area.js');

// 转换数据格式
function convertData(data) {
  return data.map(province => ({
    label: province.name,
    value: province.name,
    children: province.city.map(city => ({
      label: city.name,
      value: city.name,
      children: city.area.map(area => ({
        label: area,
        value: area
      }))
    }))
  }));
}

// 转换数据
const convertedData = convertData(areaJs);

// 写入新文件
fs.writeFileSync(
  path.join(__dirname, 'area.json'),
  JSON.stringify(convertedData, null, 2),
  'utf8'
);

console.log('数据转换完成！'); 