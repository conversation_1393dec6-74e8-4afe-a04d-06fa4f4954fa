import { DatePicker } from "@arco-design/mobile-react";
import type { DatePickerProps } from '@arco-design/mobile-react/esm/date-picker';
import React, { useState } from "react";
import "./index.less";

export interface YkDatePickerProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (value: string) => void;
  title?: string;
  currentValue?: string;
  mode?: DatePickerProps['mode'];
  disabled?: boolean;
  onChange?: (timestamp: number, obj: any) => void;
  formatter?: (value: number | string, type: string) => string;
}

const YkDatePicker: React.FC<YkDatePickerProps> = ({
  visible,
  onClose,
  onConfirm,
  title = '选择日期',
  currentValue,
  mode = 'date',
  disabled = false,
  onChange,
  formatter = (value, type) => {
    if (type === 'year') {
      return `${value}年`;
    } else if (type === 'month') {
      return `${value}月`;
    } else if (type === 'date') {
      return `${value}日`;
    }
    return `${value}`;
  }
}) => {
  const handleChange = (timestamp: number, obj: any) => {
    console.info('日期变化:', timestamp);
    onChange?.(timestamp, obj);
  };

  const handleOk = (timestamp: number) => {
    // Convert timestamp to YYYY-MM-DD format
    const date = new Date(timestamp);
    const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    onConfirm(formattedDate);
    onClose();
  };

  // Convert currentValue string to timestamp if provided, otherwise use current time
  const currentTs = currentValue 
    ? new Date(currentValue).getTime() 
    : Date.now();

  return (
    <DatePicker
      visible={visible}
      title={title}
      maskClosable
      disabled={disabled}
      currentTs={currentTs}
      mode={mode}
      onHide={onClose}
      onChange={handleChange}
      onOk={handleOk}
      formatter={formatter}
      className="picker-view"
    />
  );
};

export default YkDatePicker; 