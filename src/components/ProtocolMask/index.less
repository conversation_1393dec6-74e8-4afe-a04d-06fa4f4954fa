@import '@arco-design/mobile-react/style/mixin.less';

.popup-content {
    .rem(width, 270);
    .rem(padding, 24);
    .use-var(background-color, background-color);
    .use-dark-mode-query({
        background-color: @dark-background-color;
    });
    .rem(border-radius, 5);
    
    .popup-title {
      .text-medium();
      .rem(font-size, 16);
      .rem(margin-bottom, 20);
      text-align: center;

      .use-var(color, font-color);
      .use-dark-mode-query({
        color: @dark-font-color;
      });
    }
    
    .popup-desc {
      .rem(font-size, 13);
      .use-var(color, sub-info-font-color);
      .rem(margin-bottom, 24);
      text-align: left;
      line-height: 1.6;
  
      .link {
        .use-var(color, primary-color);
      }
    }
    
    .popup-confirm {
      width: 100%;
      height: 44px;
      .rem(border-radius, 6);
      .rem(font-size, 15);
      .rem(margin-bottom, 12);
    }
  
    .popup-reject {
      text-align: center;
      .rem(font-size, 14);
      .use-var(color, sub-info-font-color);
    }
  }