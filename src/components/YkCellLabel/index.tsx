import React from 'react';
import { View, Text } from '@tarojs/components';
import './index.less';

export interface YkCellLabelProps {
  label: string;
  className?: string;
  style?: React.CSSProperties;
  icon?: React.ReactNode;
  hint?: React.ReactNode;
  children?: React.ReactNode;
}

const YkCellLabel: React.FC<YkCellLabelProps> = ({ 
  label,
  className = '',
  style = {},
  icon,
  hint,
  children
}) => {
  return (
    <View className={`yk-cell-label ${className}`} style={style}>
      <Text
        className="yk-cell-label-text"
        dangerouslySetInnerHTML={{ __html: label }}
      />
      {hint &&
        (typeof hint === 'string'
          ? <Text className="yk-cell-label-hint">{hint}</Text>
          : hint)
      }
      {icon &&
        React.isValidElement(icon)
          ? React.cloneElement(icon as any, {
              className: `yk-cell-label-icon ${(icon.props as any).className || ''}`.trim()
            })
          : icon
      }
      {children}
    </View>
  );
};

export default YkCellLabel; 