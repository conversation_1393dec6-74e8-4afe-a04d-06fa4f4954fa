@import '@arco-design/mobile-react/style/mixin.less';


.yk-cell-label {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  max-width: 120px; // 限制最大宽度以确保长文本换行

  &-text {
    font-size: 14px;
    .use-var(color, font-color);
    .use-dark-mode-query({
      color: var(--dark-font-color) !important;
    });
    line-height: 24px;
    word-wrap: break-word;
    word-break: break-all;
  }

  &-required {
    font-size: 14px;
    color: #ff4d4f;
    margin-left: 2px;
  }

  &-hint {
    font-size: 12px;
    margin-left: 4px;
    .use-var(color, sub-info-font-color);
    .use-dark-mode-query({
      color: var(--dark-sub-info-font-color) !important;
    });
    line-height: 18px;
  }

  &-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    color: var(--sub-info-font-color) !important;
    .use-dark-mode-query({
      color: @dark-sub-info-font-color !important;
    });
    margin-left: 4px;
  }
} 