import { View, Text } from '@tarojs/components';
import { Loading } from '@arco-design/mobile-react'; 
import './index.less';

/**
 * 全屏加载遮罩组件
 * 
 * ### 功能
 * 1. 在网络请求或长时间任务处理中，显示全屏遮罩防止用户误操作。
 * 2. 支持自定义加载文案（默认显示 "加载中"）。
 * 
 * ### Props
 * - `visible` (boolean, 必填): 控制遮罩是否显示。
 * - `text` (string, 可选): 自定义加载文案，默认值为 "加载中"。
 * 
 * ### 示例用法
 * ```tsx
 * import FullScreenLoading from './components/FullScreenLoading';
 * 
 * const MyComponent = () => {
 *   const [loading, setLoading] = useState(false);
 * 
 *   const fetchData = async () => {
 *     setLoading(true);
 *     await someAsyncOperation();
 *     setLoading(false);
 *   };
 * 
 *   return (
 *     <View>
 *       <Button onClick={fetchData}>加载数据</Button>
 *       <FullScreenLoading visible={loading} text="正在加载，请稍候..." />
 *     </View>
 *   );
 * };
 * ```
 */


const FullScreenLoading = ({ visible, text = '加载中' }) => {
  return visible ? (
    <View className="fullscreen-mask">
      <View className="loading-container">
        <Text className="loading-text">{text}</Text>
        <Loading size="large" />
      </View>
    </View>
  ) : null;
};

export default FullScreenLoading;
