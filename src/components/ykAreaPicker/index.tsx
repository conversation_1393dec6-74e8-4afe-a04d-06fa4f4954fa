import { View } from "@tarojs/components";
import { Popup, PickerView } from "@arco-design/mobile-react";
import React, { useState, useEffect } from "react";
import './index.less';
import areaDataType from '@/constants/area.json'; // 引入公有目录下的完整地区数据

export type AreaDataType = typeof areaDataType;

export interface YkAreaPickerProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (value: { province: string; city: string; area: string }) => void;
  title?: string;
  data?: AreaDataType; // 使用本地数据类型
  currentValue?: string[];
}

const DEFAULT_VALUE = ['北京市', '北京市', '东城区'];

const YkAreaPicker: React.FC<YkAreaPickerProps> = ({
  visible,
  onClose,
  onConfirm,
  title = "选择地区",
  data = areaDataType, // 默认用本地完整数据
  currentValue
}) => {
  const [selectedValue, setSelectedValue] = useState<string[]>(currentValue || DEFAULT_VALUE);

  useEffect(() => {
    if (visible) {
      setSelectedValue(currentValue || DEFAULT_VALUE);
    }
  }, [currentValue, visible]);

  const handleConfirm = () => {
    const [province, city, area] = selectedValue;
    onConfirm({
      province,
      city,
      area
    });
    onClose();
  };

  return (
    <Popup
      visible={visible}
      close={onClose}
      className="area-picker"
    >
      <View className="picker-content">
        <View className="picker-header">
          <View className="cancel-btn" onClick={onClose}>取消</View>
          <View className="title">{title}</View>
          <View className="confirm-btn" onClick={handleConfirm}>确定</View>
        </View>
        <PickerView
          cascade={true}
          data={data}
          value={selectedValue}
          onPickerChange={(value) => setSelectedValue(value.map(String))}
          className="picker-view"
        />
      </View>
    </Popup>
  );
};

export default YkAreaPicker;
