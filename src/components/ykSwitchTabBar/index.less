@import "@arco-design/mobile-react/style/mixin.less";

.ykTabsIconPrimary [fill] {
  fill: var(--tab-bar-color);
  .use-dark-mode-query({
      fill: var(--dark-tab-bar-color);
  });
}
.ykTabsIconPrimary [stroke] {
  stroke: var(--tab-bar-color);
  .use-dark-mode-query({
      stroke: var(--dark-tab-bar-color);
  });
}

.activeTab [fill] {
  fill: var(--primary-color);
  .use-dark-mode-query({
      fill: var(--dark-primary-color);
  });
}
.activeTab [stroke] {
  stroke: var(--primary-color);
  .use-dark-mode-query({
    stroke: var(--dark-primary-color);
  });
}

.tabBarBox {
  background-color: var(--container-background-color);
  .use-dark-mode-query({
    background-color: var(--dark-container-background-color);
  });
  z-index: 99; // 确保TabBar在其他元素之上
}
  .tabBarPaddingbottom {
    padding-bottom: 34px !important;
  }