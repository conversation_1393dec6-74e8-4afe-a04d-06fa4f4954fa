import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { TabBar } from "@arco-design/mobile-react";
import React from "react";
import {
    IconApps,
    IconUser,
    IconEmail,
    IconApple,
    IconMessage
  } from "@arco-iconbox/react-yk-arco";
import Taro from "@tarojs/taro";
export default function YkSwitchTabBar({ activeTab, ...props }) {
  const tabs = [
    {
      title: "相册",
      icon: <IconApps className={activeTab == 0 ? "activeTab" : "ykTabsIconPrimary"} />,
      url: "/pages/index/index",
    },
    {
      title: "好友",
      icon: <IconEmail className={activeTab == 1 ? "activeTab" : "ykTabsIconPrimary"} />,
      url: "/pages/friends/index",
    },
    {
      title: "工作台",
      icon: <IconApple className={activeTab == 2 ? "activeTab" : "ykTabsIconPrimary"} />,
      url: "/pages/control/index",
    },
    {
      title: "消息",
      icon: <IconMessage className={activeTab == 3 ? "activeTab" : "ykTabsIconPrimary"} />,
      url: "/pages/message/index",
    },
    {
      title: "我的",
      icon: <IconUser className={activeTab == 4 ? "activeTab" : "ykTabsIconPrimary"} />,
      url: "/pages/my/index",
    },
  ];
  const [httpPlatform, setHttpPlatform] = React.useState("H5");

  useLoad(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    if (isAndroid) {
      setHttpPlatform("Android");
    } else if (isIos) {
      setHttpPlatform("IOS");
    } else {
      setHttpPlatform("H5");
    }
  });
   const onTabBarClick = (tab: any) => {
      Taro.switchTab({
        url: tab.url,
      });
    };
    
  return (
    <TabBar fixed activeIndex={activeTab} className={httpPlatform == 'H5' ? 'tabBarBox' : 'tabBarBox tabBarPaddingbottom'}>
      {tabs.map((tab, index) => (
        <TabBar.Item
          title={tab.title}
          icon={tab.icon}
          key={index}
          onClick={() => onTabBarClick(tab)}
        />
      ))}
    </TabBar>
  );
}
