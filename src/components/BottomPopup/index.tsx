import React, { ReactNode } from 'react';
import { View } from "@tarojs/components";
import { Popup } from "@arco-design/mobile-react";
import "./index.less";

interface OptionItem {
  label: string;
  value?: string | number;
  icon?: ReactNode;
  disabled?: boolean;
}

interface BottomPopupProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (index: number, item?: OptionItem | string) => void;
  options: (string | OptionItem)[];
  btnCloseText?: string;
  title?: string;
}

const BottomPopup: React.FC<BottomPopupProps> = ({
  visible,
  onClose,
  onConfirm,
  options = [],
  btnCloseText = "取消",
  title,
}) => {
  // 辅助函数：判断是否为 OptionItem 类型
  const isOptionItem = (item: string | OptionItem): item is OptionItem => {
    return typeof item === 'object' && item !== null && 'label' in item;
  };

  // 辅助函数：获取选项的显示文本
  const getOptionLabel = (item: string | OptionItem): string => {
    return isOptionItem(item) ? item.label : item;
  };

  // 辅助函数：检查选项是否禁用
  const isOptionDisabled = (item: string | OptionItem): boolean => {
    return isOptionItem(item) ? !!item.disabled : false;
  };

  // 辅助函数：获取选项的图标
  const getOptionIcon = (item: string | OptionItem): ReactNode => {
    return isOptionItem(item) ? item.icon : null;
  };

  return (
    <Popup
      close={onClose}
      contentStyle={{ borderRadius: "10px 10px 0 0" }}
      visible={visible}
      direction="bottom"
      className="bottom-popup"
    >
      <View className="popup-content">
        {title && <View className="popup-header">{title}</View>}
        <View className="popup-options">
          {options.map((item, index) => {
            const disabled = isOptionDisabled(item);
            const icon = getOptionIcon(item);
            const label = getOptionLabel(item);

            return (
              <View
                key={index}
                className={`popup-option ${disabled ? 'popup-option-disabled' : ''}`}
                onClick={() => {
                  if (!disabled && onConfirm) {
                    onClose();
                    onConfirm(index, item);
                  }
                }}
              >
                <View className="popup-option-content">
                  {icon && <View className="popup-option-icon">{icon}</View>}
                  <View className="popup-option-label">{label}</View>
                </View>
              </View>
            );
          })}
        </View>
        <View className="popup-cancel-btn" onClick={onClose}>
          {btnCloseText}
        </View>
      </View>
    </Popup>
  );
};

export default BottomPopup;
