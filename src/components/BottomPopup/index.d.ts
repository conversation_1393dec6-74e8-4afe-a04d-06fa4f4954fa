import React, { ReactNode } from 'react';

interface OptionItem {
  label: string;
  value?: string | number;
  icon?: ReactNode;
  disabled?: boolean;
}

interface BottomPopupProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (index: number, item?: OptionItem | string) => void;
  options: (string | OptionItem)[];
  btnCloseText?: string;
  title?: string;
}

declare const BottomPopup: React.FC<BottomPopupProps>;

export default BottomPopup;
export type { BottomPopupProps, OptionItem };