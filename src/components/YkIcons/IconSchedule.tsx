import { IconProps } from './types';

const IconSchedule: React.FC<IconProps> = ({
  color = 'currentColor',
  size = 20,
  className
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width={size}
    height={size}
    viewBox="0 0 20 20"
    className={className}
  >
    <defs>
      <clipPath id="master_svg0_10_034345/10_015951/10_015706">
        <rect x="0" y="0" width="20" height="20" rx="0" />
      </clipPath>
    </defs>
    <g clipPath="url(#master_svg0_10_034345/10_015951/10_015706)">
      <g>
        <path
          d="M3.3333366269302367,9.999996626930237C3.3333366269302367,6.318086626930237,6.318086626930237,3.3333366269302367,9.999996626930237,3.3333366269302367C13.681866626930237,3.3333366269302367,16.666666626930237,6.318086626930237,16.666666626930237,9.999996626930237C16.666666626930237,13.681866626930237,13.681866626930237,16.666666626930237,9.999996626930237,16.666666626930237C6.318086626930237,16.666666626930237,3.3333366269302367,13.681866626930237,3.3333366269302367,9.999996626930237C3.3333366269302367,9.999996626930237,3.3333366269302367,9.999996626930237,3.3333366269302367,9.999996626930237ZM9.999996626930237,1.6666666269302368C5.397626626930236,1.6666666269302368,1.6666666269302368,5.397626626930236,1.6666666269302368,9.999996626930237C1.6666666269302368,14.602366626930237,5.397626626930236,18.333366626930236,9.999996626930237,18.333366626930236C14.602366626930237,18.333366626930236,18.333366626930236,14.602366626930237,18.333366626930236,9.999996626930237C18.333366626930236,5.397626626930236,14.602366626930237,1.6666666269302368,9.999996626930237,1.6666666269302368C9.999996626930237,1.6666666269302368,9.999996626930237,1.6666666269302368,9.999996626930237,1.6666666269302368ZM9.166666626930237,9.910586626930236C9.166666626930237,9.910586626930236,12.097666626930236,13.067066626930236,12.097666626930236,13.067066626930236C12.097666626930236,13.067066626930236,13.318966626930237,11.932966626930236,13.318966626930237,11.932966626930236C13.318966626930237,11.932966626930236,10.833336626930237,9.256086626930237,10.833336626930237,9.256086626930237C10.833336626930237,9.256086626930237,10.833336626930237,5.416666626930237,10.833336626930237,5.416666626930237C10.833336626930237,5.416666626930237,9.166666626930237,5.416666626930237,9.166666626930237,5.416666626930237C9.166666626930237,5.416666626930237,9.166666626930237,9.910586626930236,9.166666626930237,9.910586626930236C9.166666626930237,9.910586626930236,9.166666626930237,9.910586626930236,9.166666626930237,9.910586626930236Z"
          fillRule="evenodd"
          fill={color}
          fillOpacity="1"
        />
      </g>
    </g>
  </svg>
);

export default IconSchedule;