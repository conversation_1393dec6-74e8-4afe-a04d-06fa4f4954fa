.page-animation-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  
  .taro_page,
  .virtual-page {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease-out;
    background: #fff;
  }
  
  /* 抽屉打开动画（非返回操作） */
  .hpa-drawer-open {
    transform: translateX(100%);
  }
  
  /* 抽屉关闭动画（返回操作） */
  .hpa-drawer-close {
    transform: translateX(-100%);
  }
  