import { useState, useEffect } from 'react';
import { View, Text } from '@tarojs/components';
import { ImagePicker, Loading } from '@arco-design/mobile-react';
import { IconSad } from '@arco-design/mobile-react/esm/icon';
import type { ImagePickItem } from '@arco-design/mobile-react/cjs/image-picker/type';
import './index.less';
import {
  baiduIdCardOcr,
  baiduBusinessLicenseOcr,
  baiduBankCardOcr,
  baiduBankAccountLicenseOcr
} from '@/utils/api/common/common_wechat';

interface YkImagePickerProps {
  type: 'idCardFront' | 'idCardBack' | 'businessLicense' | 'bankCard' | 'accountOpening' | 'others';
  value: ImagePickItem | null;
  onChange?: (img: ImagePickItem | null) => void;
  onOCRResult?: (result: any) => void;
  upload?: (img: ImagePickItem) => Promise<any> | void;
  label?: string;
  disabled?: boolean;
  /** 图片URL，用于触发OCR识别 */
  imageUrl?: string;
}

export default function YkImagePicker({
  type,
  value,
  onChange,
  onOCRResult,
  upload,
  label,
  disabled,
  imageUrl
}: YkImagePickerProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 当imageUrl变化时触发OCR
  useEffect(() => {
    if (imageUrl && onOCRResult && type !== 'others') {
      handleOCR(imageUrl);
    }
  }, [imageUrl, onOCRResult, type]);

  // OCR识别 - 使用后端接口
  const handleOCR = async (imageUrl: string) => {
    console.log("YkImagePicker: handleOCR with backend API...", imageUrl);
    setLoading(true);
    setError(null);
    try {
      if (!imageUrl) {
        setError('图片URL不存在');
        setLoading(false);
        return;
      }

      let result: any;
      const ocrData = { url: imageUrl };

      if (type === 'idCardFront' || type === 'idCardBack') {
        result = await baiduIdCardOcr(ocrData);
      } else if (type === 'businessLicense') {
        result = await baiduBusinessLicenseOcr(ocrData);
      } else if (type === 'bankCard') {
        result = await baiduBankCardOcr(ocrData);
      } else if (type === 'accountOpening') {
        result = await baiduBankAccountLicenseOcr(ocrData);
      }

      console.log('[YkImagePicker] OCR API返回结果:', {
        type,
        result,
        hasWordsResult: !!result?.words_result,
        hasErrorCode: !!result?.error_code,
        resultKeys: result ? Object.keys(result) : []
      });

      // 检查是否有错误
      if (result?.error_code) {
        setError(result?.error_msg || '识别失败，请重试');
        setLoading(false);
        return;
      }

      // 检查是否有识别结果并转换格式
      if (result) {
        let standardResult = result;

        // 如果后端返回的是新格式（有result字段），需要转换为前端期望的格式
        if (result.result && !result.words_result) {
          console.log('[YkImagePicker] 检测到新格式OCR结果，正在转换...');

          if (type === 'bankCard') {
            // 银行卡格式转换
            standardResult = {
              ...result,
              words_result: {
                bank_card_number: { words: result.result.bank_card_number || '' },
                bank_name: { words: result.result.bank_name || '' },
                bank_card_type: { words: result.result.bank_card_type?.toString() || '' },
                holder_name: { words: result.result.holder_name || '' }
              }
            };
          } else if (type === 'accountOpening') {
            // 开户证明格式转换（根据实际字段调整）
            standardResult = {
              ...result,
              words_result: {
                公司名称: { word: result.result.company_name || result.result.公司名称 || '' },
                账号: { word: result.result.account_number || result.result.账号 || '' },
                开户银行: { word: result.result.bank_name || result.result.开户银行 || '' }
              }
            };
          } else {
            // 其他类型的转换
            standardResult = {
              ...result,
              words_result: result.result
            };
          }

          console.log('[YkImagePicker] 格式转换完成:', standardResult);
        }

        // 检查转换后的结果
        if (standardResult.words_result || standardResult.data || standardResult.result) {
          if (onOCRResult) onOCRResult(standardResult);
        } else {
          console.warn('[YkImagePicker] OCR结果格式不符合预期:', result);
          setError('未识别到有效内容，请重试');
          setLoading(false);
          return;
        }
      } else {
        console.warn('[YkImagePicker] OCR返回空结果');
        setError('未识别到有效内容，请重试');
        setLoading(false);
        return;
      }
    } catch (e: any) {
      console.error('[YkImagePicker] OCR识别失败:', e);
      setError(e.message || '识别失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理图片变更
  const handleChange = (imgs: ImagePickItem[]) => {
    const img = imgs[0] || null;
    console.log('[YkImagePicker] handleChange:', {
      type,
      imgsLength: imgs.length,
      img,
      currentValue: value
    });
    onChange && onChange(img);
    setError(null);
    // OCR将在图片上传完成后调用，不在这里直接调用
  };

  // 处理图片上传
  const handleUpload = async (img: ImagePickItem): Promise<ImagePickItem | null> => {
    if (upload) {
      try {
        // 调用外部的 upload 方法
        const uploadResult = await Promise.resolve(upload(img));

        // 如果有OCR回调且上传成功，尝试获取图片URL并进行OCR
        if (onOCRResult && uploadResult) {
          // 从上传结果中获取图片URL
          let imageUrl = '';
          if (typeof uploadResult === 'string') {
            imageUrl = uploadResult;
          } else if (uploadResult && typeof uploadResult === 'object') {
            // 尝试从不同可能的字段获取URL
            imageUrl = (uploadResult as any).localUrl || (uploadResult as any).url || (uploadResult as any).data || '';
          }

          console.log('[YkImagePicker] 准备进行OCR识别:', {
            type,
            uploadResult,
            imageUrl,
            hasOnOCRResult: !!onOCRResult
          });

          if (imageUrl) {
            // 延迟一下再调用OCR，确保上传完全完成
            setTimeout(() => {
              handleOCR(imageUrl);
            }, 500);
          } else {
            console.warn('[YkImagePicker] 无法从上传结果中获取图片URL:', uploadResult);
          }
        }

        return uploadResult as ImagePickItem | null;
      } catch (error) {
        console.error('[YkImagePicker] 上传失败:', error);
        setError('上传失败，请重试');
        return null;
      }
    }
    // 如果没有 upload 方法，直接返回 null
    return null;
  };

//   // 处理重拍
//   const handleReupload = () => {
//     onChange && onChange(null);
//     setError(null);
//     return null;
//   };

  return (
    <View className="yk-imagepicker">
        <ImagePicker
            className="yk-imagepicker-card"
            images={value ? [value] : []}
            onChange={handleChange}
            upload={handleUpload}
            limit={1}
            columns={1}
            disabled={disabled}
            imageProps={{
                fit: 'contain',
            }}
            renderLoading={()=> (
                <View className="yk-imagepicker-card-loading">
                  <Loading type="circle"></Loading>
                </View>
            )}
            renderError={()=> (
                <View className="yk-imagepicker-card-error">
                    <IconSad/>
                </View>
            )}
        />
        {label && <View className="yk-imagepicker-label"><Text className="yk-imagepicker-label-text">{label}</Text></View>}
        {loading && (
          <View className="yk-imagepicker-ocr-status">
            <Loading type="circle" />
            <Text className="yk-imagepicker-ocr-text">正在识别...</Text>
          </View>
        )}
        {error && (
          <View className="yk-imagepicker-error-status">
            <Text className="yk-imagepicker-error-text">{error}</Text>
          </View>
        )}
    </View>
  );
}