@import '@arco-design/mobile-react/style/mixin.less';
@import '@/utils/css/variables.less';

.yk-imagepicker {
  margin: 0 !important;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  //gap: 5px;
  width: 100%;

  .yk-imagepicker-card {
    // width: 160px !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100%;
    height: 90px !important;
    // background-color: #fff;
    // overflow: hidden !important;
    // background-color: var(--background-color) !important;
    // .use-var(background-color, background-color);
    // .use-dark-mode-query({
    //   background-color: @dark-background-color !important;
    // });
    
    .arco-grid-rows {
      flex: 1;
    }
    .arco-image-picker-container {
      width: 100% !important;
      height: 100% !important;
    }
    .arco-image-picker-add {
      padding-top: 50% !important;
    }
    .arco-image-picker-image {
      flex: 1;
      padding-top: 50% !important;
    }
  
    // .arco-grid-rows-item {
    //   width: 160px !important;
    //   height: 90px !important;
    //   z-index: 100;
    //   background-color: #fff;
    //   border-radius: 6px;
    //   overflow: hidden !important;
    // }
  }

  .yk-imagepicker-card-loading {
//   position: absolute;
//   top: 0; left: 0; right: 0; bottom: 0;
//   background: rgba(255,255,255,0.7);
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   justify-content: center;
//   z-index: 10;
}

  .yk-imagepicker-card-error {
//   position: absolute;
//   top: 0; left: 0; right: 0; bottom: 0;
//   background: rgba(255,255,255,0.9);
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   justify-content: center;
//   z-index: 10;
//   color: #f53f3f;
}

  .yk-imagepicker-label {
    width: 100%;
    margin-top: 5px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 0 10px;
    &-text {
      font-size: 11px;
      color: #86909C;
    }
  }

  .yk-imagepicker-ocr-status {
    width: 100%;
    margin-top: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 10px;
    background-color: #F2F3F5;
    border-radius: 4px;
  }

  .yk-imagepicker-ocr-text {
    font-size: 12px;
    color: #4E5969;
  }

  .yk-imagepicker-error-status {
    width: 100%;
    margin-top: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 8px 10px;
    background-color: @--danger-1;
    border-radius: 4px;
  }

  .yk-imagepicker-error-text {
    font-size: 12px;
    color: @--danger-6;
  }
}