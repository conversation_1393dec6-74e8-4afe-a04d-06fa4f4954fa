@import '@arco-design/mobile-react/style/mixin.less';

.reset-password {
  // min-height: 100vh;
  display: flex;
  flex-direction: column;
//   align-items: center;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });

  .title-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .rem(margin-top, 40);
    // .rem(width, 290);
    .rem(margin-left, 30);
    .title {
      .text-medium();
      .rem(font-size, 18);
      .rem(margin-bottom, 8);
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: @dark-font-color;
      });
    }

    .subtitle {
      .rem(font-size, 15);
      .use-var(color, sub-info-font-color);
      .use-dark-mode-query({
        color: @dark-sub-info-font-color;
      });
    }
  }

  .input-container {
    // .rem(margin-top, 60);
    // .rem(padding-left, 30);
    // .rem(padding-right, 30);

    width: 100%;
    box-sizing: border-box;
    padding: 30px 14px;

    display: flex;
    flex-direction: column;
    // align-items: center;

    .input-field {
      .rem(width, 315);
    // width: calc(100% - 60px);
      .rem(height, 46);
    //   .rem(border-radius, 6);
      .rem(margin-bottom, 15);
      // background-color: #F2F4F5;
      // .use-dark-mode-query({
      //   background-color: #2B2B2B;
      // });
    }

    .verification-row {
      display: flex;
    //   .rem(width, 290);
      align-items: center;
      .rem(margin-bottom, 15);

      // .input-verification {
      //   .rem(width, 200);
      //   .rem(margin-bottom, 0);
      //   .rem(margin-right, 10);
      // }

      .get-code {
        .rem(font-size, 14);
        .use-var(color, primary-color);
        .use-dark-mode-query({
          color: @dark-primary-color;
        });
      }
    }
  }

  .submit-button {
    .rem(margin-top, 50);
    .rem(padding-left, 30);
    .rem(padding-right, 30);

    display: flex;
    align-items: center;

    .complete-button {
    //   .rem(width, 290);
      .rem(height, 46);
      .rem(border-radius, 6);
    }
  }
} 