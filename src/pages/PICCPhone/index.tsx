import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Image } from "@arco-design/mobile-react";
import React, { useEffect } from "react";
import Taro from "@tarojs/taro";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

export default function aboutUs() {
  const [userInfo, setUserInfo] = React.useState<any>(false);
  const [phone, setPhone] = React.useState<string>('');

   const formatPhoneNumber = (phoneNumber) => {
      const start = phoneNumber.substring(0, 3);
      const end = phoneNumber.substring(phoneNumber.length - 4);
      return start + "****" + end;
  };

  useLoad(() => {
    setUserInfo(Taro.getStorageSync("userInfo").userInfo);
  });

  useEffect(() => {
    if(userInfo){
      setPhone(formatPhoneNumber(userInfo.mobile));
    }
  }, [userInfo]);

  return (
    <View className="PICCPageContent">
      <YkNavBar title="" />
      <View className="PICCPageContent-title">
        <View className="PICCPageContent-title-name">「手机号」收集情况</View>
      </View>
      <View className="PICCModule">
        <View className="PICCModule-item">
          <View className="PICCModule-item-label">内容</View>
          <View className="PICCModule-item-content">{phone}</View>
        </View>
        <View className="PICCModule-item">
          <View className="PICCModule-item-label">目的</View>
          <View className="PICCModule-item-content">
            <Text className="PICCModule-item-content-text">
              用户自行完善设置
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}
