import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Cell, Image } from "@arco-design/mobile-react";
import React from "react";
import Taro from "@tarojs/taro";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

export default function aboutUs() {
  const [isIos, setIsIos] = React.useState(false);

  useLoad(() => {});

  return (
    <View className="aboutUsPageContent">
      <YkNavBar title="注销账号" />
      <View>
        <View>申请注销账号</View>
        <Text>您提交的注销申请生效前，我们将进行以下验证，以保证您的账号数据安全</Text>
      </View>
    </View>
  );
}
