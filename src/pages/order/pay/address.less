@import "../../../utils/css/variables.less";

.address-page {
  background: #f7f8fa;
  min-height: 100vh;
  padding-bottom: 80px;
}

.address-section-title {
  color: #bfc2cc;
  font-size: 14px;
  padding: 16px 16px 8px 16px;
}

.address-form {
  background: #fff;
  border-radius: 0;
  margin: 0;
  padding: 0 0 0 0;
}
.address-form-item {
  display: flex;
  align-items: center;
  height: 48px;
  padding: 0 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  &:last-child {
    border-bottom: none;
  }
  &.address-form-item-arrow {
    cursor: pointer;
    position: relative;
    &:after {
      content: '';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 16px;
      height: 16px;
      background: url('data:image/svg+xml;utf8,<svg fill="%23999" height="16" viewBox="0 0 1024 1024" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M384 192l256 320-256 320z"/></svg>') no-repeat center/contain;
    }
  }
}
.address-label {
  color: #222;
  font-size: 15px;
  width: 90px;
  flex-shrink: 0;
}
.address-input {
  flex: 1;
  background: transparent;
  border: none;
  font-size: 15px;
  color: #222;
  margin-left: 8px;
  padding: 0;
}
.address-placeholder {
  color: #bfc2cc;
}
.address-region-value {
  color: #222;
  min-height: 22px;
}
.address-bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.03);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 56px;
  z-index: 10;
}
.address-btn {
  width: 100%;
  height: 40px;
  font-size: 16px;
  border-radius: 6px;
  background: #93bfff;
  color: #fff;
  border: none;
}
