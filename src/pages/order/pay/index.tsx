import React, { useState  } from "react";
import { View, Image, Text, Input } from "@tarojs/components";
import { <PERSON><PERSON>, Stepper, Cell, Avatar, Switch, Picker } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar";
import "./index.less";
import Taro from "@tarojs/taro";
import { useDidShow } from "@tarojs/taro";
import { useEffect } from "react";

interface AddressInfo {
  name?: string;
  phone?: string;
  area?: string;
  detailedAddress?: string;
}

interface CartDetail {
  id: number;
  userId: number;
  shoppingCartId: number;
  productColorId: number | null;
  productSpecificationsId: number | null;
  productSpecificationsName: string | null;
  productColorName: string | null;
  quantity: number;
  price: number;
  checked: boolean;
}

interface CartItem {
  id: number;
  dynamicsId: number;
  dynamicsUserId: number;
  dynamicsUserName: string;
  dynamicsUserAvatar: string;
  dynamicsContent: string;
  dynamicsImage: string;
  remark: string | null;
  type: number;
  checked: boolean;
  details: CartDetail[];
}

const goodsList = [
  {
    shop: "尚品百货",
    goods: [
      {
        img: "https://img.alicdn.com/imgextra/i1/2200708363777/O1CN01Qw1k8B1wJv2lYQ6gA_!!0-item_pic.jpg",
        name: "Nk SB Dunk Low LV联名-高端定制 很帮 休闲鞋大厂纯原品质细节完美纯白新款.",
        price: 230,
        size: "36.5",
        count: 1,
      },
      {
        img: "https://img.alicdn.com/imgextra/i1/2200708363777/O1CN01Qw1k8B1wJv2lYQ6gA_!!0-item_pic.jpg",
        name: "Nk SB Dunk Low LV联名-高端定制 很帮 休闲鞋大厂纯原品质细节完美纯白新款...",
        price: 230,
        size: "38",
        count: 1,
      },
    ],
  },
];

export default function PayOrderPage() {
  const [goods, setGoods] = useState(goodsList[0].goods);
  const [remark, setRemark] = useState("");
  const [customSender, setCustomSender] = useState(false);
  const [senderName, setSenderName] = useState('');
  const [senderPhone, setSenderPhone] = useState('');
  const [address, setAddress] = useState<AddressInfo>({});
  const [selectedItems, setSelectedItems] = useState<CartItem[]>([]);
  // 数量加减
  const handleCountChange = (itemIdx: number, detailIdx: number, value: number) => {
    setSelectedItems(prev => {
      const newItems = [...prev];
      newItems[itemIdx].details[detailIdx].quantity = value;
      return newItems;
    });
  };

  const totalCount = selectedItems.reduce((sum, item) => 
    sum + item.details.reduce((itemSum, detail) => itemSum + detail.quantity, 0), 0
  );
  const totalPrice = selectedItems.reduce((sum, item) => 
    sum + item.details.reduce((itemSum, detail) => itemSum + item.price * detail.quantity, 0), 0
  );
  const freight = 10;
  const payPrice = totalPrice + freight;
  const single = React.useMemo(() => {
      return [
          [
              {label: <span className="demo-picker-color"><i  /><span>快递</span></span>, value: '快递'},
              {label: <span className="demo-picker-color"><i  /><span>顺丰到付</span></span>, value: '顺丰到付'},
              {label: <span className="demo-picker-color"><i  /><span>自提</span></span>, value: '自提'},
              {label: <span className="demo-picker-color"><i  /><span>其他</span></span>, value: '其他'},
          ]
      ];
    }, []);
    //const address = Taro.getStorageSync('address');
  useDidShow(() => {
    setAddress(Taro.getStorageSync('address') || {});
  });
  const [singleValue, setSingleValue] = React.useState(['快递']);
  useEffect(() => {
      setSelectedItems( Taro.getStorageSync('selectedItems') || []);
      console.log(Taro.getStorageSync('selectedItems'),'-------------------'); 
  }, []);
  return (
    <View className="pay-order-page">
      <YkNavBar title="确认订单" />
      <View className="pay-list-section">
        <Cell.Group bordered={false}>    
          <Cell
            label={
              address.detailedAddress ? (
               <div className="demo-cell-avatar-label">
                <span>{address.detailedAddress}</span>
               </div>
              ) : (
               <div className="demo-cell-avatar-label">
                <Avatar src="https://sf1-cdn-tos.toutiaostatic.com/obj/arco-mobile/_static_/small_image_5.jpg" />
                <span>添加收货地址</span>
                </div>
              )
            }

            {...(address?.name && address?.phone && address?.area && address?.detailedAddress ? {
              prepend: (
                <div style={{ fontSize: 12, lineHeight: '18px', marginBottom: -15, paddingTop: 16 }}>
                  {address.area}
                </div>
              ),
              append: (
                <div style={{ fontSize: 12, lineHeight: '18px', marginTop: -15, paddingBottom: 16 }}>
                  {address.name} {address.phone} 
                </div>
              )
            } : {})}
            showArrow
            onClick={() => Taro.navigateTo({ url: '/pages/order/pay/address' })}
          />

          <Picker
            title="配送方式"
            cascade={false}
            data={single}
            maskClosable={true}
            value={singleValue}
            onChange={val => setSingleValue(val as any as string[]  )}
            mountOnEnter={false}
            unmountOnExit={false}
            renderLinkedContainer={(_, data) => (
              <Cell label={<Text>配送方式</Text>} className="pay-list-cell" text={singleValue[0] as string} showArrow  />
            )}
          />

          
          <Cell label={<Text>自定义发件人</Text>} className="pay-list-cell">
            <Switch platform="android" checked={customSender} onChange={setCustomSender} />
          </Cell>
          {customSender && (
            <View className="custom-sender-panel">
              <View className="custom-sender-row">
                <Text className="custom-sender-label">发件人</Text>
                <Input
                  className="custom-sender-input"
                  placeholder="默认为商家，可修改"
                  value={senderName}
                  disabled={!customSender}
                  onInput={e => setSenderName(e.detail.value)}
                />
              </View>
              <View className="custom-sender-row">
                <Text className="custom-sender-label">手机号</Text>
                <Input
                  className="custom-sender-input"
                  placeholder="默认为商家手机号，可修改"
                  value={senderPhone}
                  disabled={!customSender}
                  onInput={e => setSenderPhone(e.detail.value)}
                />
              </View>
            </View>
          )}


        </Cell.Group>
      </View>
      <View className="pay-goods-section">
        {selectedItems.map((item, idx) => (
          <View>
                  <View className="shop-title-row">
                  <Avatar src={item.dynamicsUserAvatar} />
                  <Text className="shop-title">{item.dynamicsUserName}</Text>
                  <Text className="shop-cert" />
                </View>
                
                  <View className="goods-row" key={idx}>             
                        <View className="goods-info">
                            <Image className="goods-img" src={item.dynamicsImage} />
                            <Text className="goods-title">{item.dynamicsContent}</Text>
                            <View style={{display: 'flex', justifyContent:'flex-start', alignItems: 'center',flexDirection: 'column'}}>
                            <Text className="goods-price">￥{selectedItems[idx].price}</Text>
                            <Text className="goods-count">x{item.details.reduce((sum, detail) => sum + detail.quantity, 0)}</Text>
                            </View>
                        </View>        
                        {item.details.map((g, detailIdx) => (
                          <View className="goods-bottom-row" key={detailIdx}>
                              <Text className="goods-size">{g.productColorName || g.productSpecificationsName}</Text>
                              <Stepper
                              value={g.quantity}
                              min={1}
                              onChange={v => handleCountChange(idx, detailIdx, v || 1)}
                              />
                          </View>
                        ))}
                  </View>
               
          </View>
        ))}


        <View className="pay-summary-list">
          <View className="pay-summary-row">
            <Text>商品数量</Text>
            <Text>{totalCount}</Text>
          </View>
          <View className="pay-summary-row">
            <Text>商品总金额</Text>
            <Text>¥{totalPrice.toFixed(2)}</Text>
          </View>
          <View className="pay-summary-row">
            <Text>运费</Text>
            <Text>¥{freight.toFixed(2)}</Text>
          </View>
          <View className="pay-summary-row pay-summary-total">
            <Text>实付金额</Text>
            <Text className="pay-price">¥{payPrice.toFixed(2)}</Text>
          </View>
        </View>
        <View className="pay-remark-row">
          <Input
            placeholder="留言(选填)"
            value={remark}
            onInput={e => setRemark(e.detail.value)}
            className="pay-remark-input"
          />
        </View>
      </View>
      <View className="pay-bottom-bar">
        <Text className="pay-bottom-count">共{totalCount}件 ¥{totalPrice}</Text>
        <Button type="primary" className="pay-bottom-btn">
          立即支付 ¥{payPrice}
        </Button>
      </View>
    </View>
  );
}
