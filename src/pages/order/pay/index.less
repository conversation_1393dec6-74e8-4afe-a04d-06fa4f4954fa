.pay-order-page {
  background: #f7f8fa;
  min-height: 100vh;
}
.pay-list-section {
  margin-bottom: 12px;
}
.pay-goods-section {
  background: #fff;
  border-radius: 10px;
  margin: 0 12px 12px 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  padding: 12px 12px 0 12px;
}
.shop-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.shop-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
}
.shop-title {

  font-weight: 500;
  font-size: 15px;
  margin-left: 6px;
}
.shop-cert {
  width: 16px;
  height: 16px;
  //background: url('cert-icon.svg') no-repeat center/contain;
}
.goods-row {
  display: flex;
  //align-items: flex-start;
  flex-direction: column;
  margin-bottom: 10px;
}
.goods-img {
  width: 84px;
  height: 64px;
  border-radius: 8px;
  background: #f5f5f5;
  margin-right: 10px;
}
.goods-info {

  display: flex;
  flex: 2;
  min-width: 0;
}
.goods-title {
  width: 70%;  
  font-size: 14px;
  color: #222;
  font-weight: 500;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  line-height: 20px;
  max-height: 40px;
  
}
.goods-meta {
  display: flex;
  align-items: center;
  margin-top: 8px;
}
.goods-bottom-row {
    width: 100%;
    display: flex; 
    justify-content: space-between; 
    align-items: center;
}
.goods-size {
  font-weight: 500;
  font-size: 13px;
}
.goods-count {
  font-weight: 500;
  font-size: 15px;
  margin-top: -8px;
}
.goods-price {
  font-weight: 500;
  font-size: 15px;
  color: #f53f3f;
  margin-left: 8px;
}

.goods-stepper {
  width: 100px;
}
.pay-summary-list {
  margin: 12px 0 0 0;
  border-top: 1px solid #f0f0f0;
  padding-top: 10px;
}
.pay-summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #888;
  margin-bottom: 6px;
}
.pay-summary-total {
  color: #222;
  font-weight: 600;
  font-size: 15px;
}
.pay-price {
  color: #f53f3f;
  font-weight: 700;
  font-size: 16px;
}
.pay-remark-row {
  margin: 12px 0 0 0;
}
.pay-remark-input {
  width: 100%;
  background: #f7f8fa;
  border-radius: 6px;
  font-size: 14px;
  padding: 8px 12px;
}
.pay-bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.03);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  height: 56px;
  z-index: 10;
}
.pay-bottom-count {
  font-size: 15px;
  color: #222;
}
.pay-bottom-btn {
  margin-left: 12px;
  width: 160px;
  height: 40px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

.demo-cell-avatar-label {
    display: flex;
    align-items: center;
    .arco-avatar {
        width: 32px;
        height: 32px;
        margin-right: 8px;
    }

}

.custom-sender-panel {
  background: #f7f8fa;
  border-radius: 8px;
  margin: 8px 16px 0 16px;
  padding: 0 0 0 0;
  .custom-sender-row {
    display: flex;
    align-items: center;
    height: 44px;
    border-bottom: 1px solid #f0f0f0;
    padding: 0 12px;
    &:last-child {
      border-bottom: none;
    }
    .custom-sender-label {
      color: #222;
      font-size: 15px;
      width: 70px;
      flex-shrink: 0;
    }
    .custom-sender-input {
      flex: 1;
      background: transparent;
      border: none;
      font-size: 15px;
      color: #999;
      margin-left: 8px;
      padding: 0;
    }
  }
}





