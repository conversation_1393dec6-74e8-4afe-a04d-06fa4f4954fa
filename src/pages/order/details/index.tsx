import { View, Text } from '@tarojs/components';
import { useLoad } from '@tarojs/taro';
import './index.less';
import { NavBar, Cell, Button, Image, Avatar, Divider, Tag } from '@arco-design/mobile-react';
import { IconFile } from '@arco-design/mobile-react/esm/icon';
import React, { useState } from 'react';

export default function OrderDetails() {
  // 示例数据，实际可用 props 或接口替换
  const order = {
    orderNo: '1202310111647443130001773',
    createTime: '2023-10-11 16:35:44',
    sender: {
      name: '尚品百货',
      phone: '18011457842',
      id: '1011457842',
    },
    receiver: {
      name: '张三',
      phone: '17345621472',
      address: '福建省福州市城区宏基大厦',
    },
    delivery: '快递',
    payType: '待付款',
    freight: 10,
    remark: '货到付款货到付款货到付款货到付款货到付款...',
    goods: [
      {
        id: 1,
        name: 'Nk SB Dunk Low LV联名-高端定制 休闲鞋#仲间限定#广纯质品质量节完美纯白新款,休闲鞋#仲间限定#广纯质品质量节完美纯白新款',
        price: 150,
        count: 1,
        img: 'https://img.alicdn.com/imgextra/i1/2200704182015/O1CN01Qw1k0F1z2v7kRDehA_!!2200704182015.jpg',
        spec: '默认',
      },
    ],
    total: 150,
    actual: 160,
    images: [
      'https://img.alicdn.com/imgextra/i1/2200704182015/O1CN01Qw1k0F1z2v7kRDehA_!!2200704182015.jpg',
      'https://img.alicdn.com/imgextra/i1/2200704182015/O1CN01Qw1k0F1z2v7kRDehA_!!2200704182015.jpg',
      'https://img.alicdn.com/imgextra/i1/2200704182015/O1CN01Qw1k0F1z2v7kRDehA_!!2200704182015.jpg',
      'https://img.alicdn.com/imgextra/i1/2200704182015/O1CN01Qw1k0F1z2v7kRDehA_!!2200704182015.jpg',
      'https://img.alicdn.com/imgextra/i1/2200704182015/O1CN01Qw1k0F1z2v7kRDehA_!!2200704182015.jpg',
    ],
  };

  const [activeBtn, setActiveBtn] = useState(-1); // -1表示无高亮按钮
  return (
    <View className="order-details-box">
      <NavBar title="订单详情" />
      <View className="order-status-bar">待付款</View>
      <View className="order-info-card">
        <Cell 
        label="订单编号" 
        style={{height: '40px'}}
        text={
            <View>
                <Text style={{marginRight: '10px'}}>{order.orderNo}</Text>    
                <IconFile />
            </View>
            
        } />
        <Cell 
        label="下单时间" 
        text={order.createTime} 
        style={{height: '40px'}}
        />
      </View>

      <View className="order-user-card">
        <Cell 
          label={   <div className="demo-cell-avatar-label"
                        style={{width: '140px'}}   
                    >
                        <Avatar 
                            size="small" 
                            style={{ marginRight: 5 }} 
                            src="//sf1-cdn-tos.toutiaostatic.com/obj/arco-mobile/_static_/small_image_5.jpg" 
                            />
                        <div >张三张三</div>
                    </div>} 
            showArrow                        
         />
        <Cell label="发件人" text={`${order.sender.name}  ${order.sender.id}`} />
        <Cell label="收件人" text={`${order.receiver.name}  ${order.receiver.phone}`} />
        <Cell label="地址" text={order.receiver.address} />
        <Cell label="配送方式" text={order.delivery} />
        <Cell label="支付方式" text={order.payType} />
        <Cell label="运费" text={`¥${order.freight.toFixed(2)}`} />
      </View>
      <View className="order-remark">
        <Cell label="买家留言" text={order.remark} />
      </View>
      <View className="order-img-list">
        {order.images.map((img, idx) => (
          <Image key={idx} src={img} width={60} height={60} style={{ marginRight: 8, borderRadius: 6 }} />
        ))}
      </View>
      <View className="order-goods-card">

        <Cell 
          label={ <div className="demo-cell-avatar-label"
                        style={{width: '140px'}}   
                  >
                        <Avatar 
                            size="small" 
                            style={{ marginRight: 5 }} 
                            src="//sf1-cdn-tos.toutiaostatic.com/obj/arco-mobile/_static_/small_image_5.jpg" 
                            />
                        <div >尚品百货</div>
                  </div>} 
            showArrow                        
         />
        
        <Cell.Group>
            <Cell
                style={{ display:"flex", justifyContent: "flex-start", alignItems: "center" }}
                label={
                    <Image src={order.goods[0].img} width={60} height={60} style={{ borderRadius: 8, marginRight: 10 }} />
                }
                append={
                <div style={{ display:"flex", justifyContent: "space-between", alignItems: "center" }}>    
                    <div className='info'>{order.goods[0].name}</div>
                    <div >
                            <div className="info">¥{order.goods[0].price}</div>
                            <div className="sub-info">× {order.goods[0].count}</div>
                    </div>
                </div>    
            
            }

               
            >
                
            </Cell>
        </Cell.Group>

        <Cell.Group>
            <Cell
                label={<View style={{ width: 140 ,marginTop: -20,}}>商品总金额</View>}
                prepend={<div style={{ fontSize: 12, lineHeight: '18px',  paddingTop: 14 }}>商品数量</div>}
                append={
                    <div>
                        <div style={{ fontSize: 12, lineHeight: '18px', marginTop: -25, paddingBottom: 16 }}>运费</div>
                        <div style={{ fontSize: 12, lineHeight: '18px', marginTop: -13, paddingBottom: 16 }}>实付金额</div>
                    </div>
                
            
                }
                
                
            >
                <div className="demo-cell-info">
                    <div className="sub-info">{order.goods[0].count}</div>
                    <div className="sub-info">¥{order.total}</div>
                    <div className="sub-info">¥{order.freight}</div>
                    <div className="order-goods-actual"><Text style={{ color: '#e35848' }}>¥{order.actual}</Text></div>
                </div>
            </Cell>
        </Cell.Group>



        {/* <View className="order-goods-summary">
          <View>商品数量：{order.goods[0].count}</View>
          <View>商品总金额：¥{order.total}</View>
          <View>运费：¥{order.freight}</View>
          <View className="order-goods-actual">实付金额：<Text style={{ color: '#e35848' }}>¥{order.actual}</Text></View>
        </View> */}

      </View>


      <View className="order-footer-bar">
              {["取消订单", "转发", "联系商家","立即支付"].map((btn, i) => (
                <Button
                  className={`order-btn primary${activeBtn === i ? " btn-active" : ""}`}
                  size="small"
                  key={btn}
                  onClick={() => setActiveBtn(i)}
                >
                  {btn}
                </Button>
              ))}
      </View>    

    </View>
  );
}
