.order-page {
  background: #f7f8fa;
  min-height: 100vh;
}
.order-tabs {
  display: flex;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 12px;
  font-size: 15px;
  color: #888;
}
.tab {
  flex: 1;
  text-align: center;
  padding: 14px 0 10px 0;
  position: relative;
}
.tab.active {
  color: #222;
  font-weight: 500;
}
.tab.highlight {
  color: #165dff;
  font-weight: 600;
  border-bottom: 2px solid #165dff;
}
.order-list {
  padding: 12px;
}
.order-card {
  background: #fff;
  border-radius: 10px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  padding: 0 0 10px 0;
}
.order-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 12px 0 12px;
  font-size: 15px;
}
.shop-name {
  font-weight: 500;
}
.order-status {
  color: #165dff;
  font-size: 13px;
}
.goods-list {
  padding: 0 12px;
}
.goods-item {
  display: flex;
  align-items: center;
  margin-top: 12px;
}
.goods-img {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background: #f5f5f5;
  margin-right: 10px;
}
.goods-info {
  flex: 1;
  min-width: 0;
}
.goods-title {
  font-size: 14px;
  color: #222;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  display: block;
}
.goods-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  height: auto;
  justify-content: flex-start;
  font-size: 13px;
  color: #888;
  float: inline-end;
  text-align: right;
}
.goods-price {
  color: #f53f3f;
  font-weight: 500;
  margin-bottom: 2px;
  font-size: 13px;
}
.goods-count {
  color: #888;
}
.order-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px 0 12px;
  font-size: 13px;
  color: #888;
}
.order-summary .express {
  flex: 1;
  text-align: left;
  margin-right: 30px;
}
.order-summary .order-time {
  flex: 1;
  text-align: center;
}
.order-summary .order-total {
  flex: 3;
  text-align: right;
  color: #222;
  font-weight: 500;
}
.order-summary .order-total .freight {
  color: #888;
  font-size: 13px;

}
.order-summary .order-total .total-price {
  color: #f53f3f;
  font-weight: 600;
  margin: 0 2px;
}
.order-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 0 0;
  border-top: 1px solid #f0f0f0;
  background: #fff;
  height: 44px;
  margin-top: 10px;
}
.order-btn {
  flex: 1;
  min-width: 0;
  border-radius: 0;
  font-size: 16px;
  padding: 0;
  height: 44px;
  background: #fff;
  border: none;
  color: #222;
  border-right: 1px solid #f0f0f0;
  box-shadow: none;
  margin: 0;
}
.order-btn:last-child {
  border-right: none;
}
.order-btn.primary {
  color: #000000;
  font-weight: bold;
  background: #fff;
  border: none;
}
.order-btn.btn-active {
  color: #165dff !important;
  font-weight: bold;
}
.empty-order {
  text-align: center;
  color: #bbb;
  font-size: 15px;
  padding: 48px 0 32px 0;
}
