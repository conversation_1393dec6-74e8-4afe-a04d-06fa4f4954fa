import { View, Text } from '@tarojs/components'
import { Button, Image, Input, NavBar } from '@arco-design/mobile-react'
import './code.less'
import React, { useEffect } from 'react'
import YkNavBar from '@/components/ykNavBar'
import Taro, { useLoad, useReady } from "@tarojs/taro";
import { toast } from "@/utils/yk-common";
import { getSmsCode, smsLogin, getUserInfo } from '@/utils/api/common/common_user'
import { YiCode } from './yi-code'; // Added import for YiCode

export default function CodeLoginCode() {
  const [phone, setPhone] = React.useState('');
  const [inputCode, setInputCode] = React.useState(['', '', '', '',]);
  const [currentIndex, setCurrentIndex] = React.useState(0); // 当前激活的输入框索引
  // 验证码
  const [code, setCode] = React.useState("");
  //验证码倒计时
  const [countDown, setCountDown] = React.useState(60);
  // 是否开启倒计时
  const [autoStart, setAutoStart] = React.useState(false);
  // 是否是H5环境
  const isH5 = React.useMemo(() => Taro.getEnv() === Taro.ENV_TYPE.WEB, []);

  useLoad((e) => {
    setPhone(decodeURIComponent(e.phone))
  });

  useReady(() => {
    getCodeApi(phone)
  })

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;

    if (autoStart && countDown > 0) {
      timer = setInterval(() => {
        setCountDown((prev) => prev - 1);
      }, 1000);
    } else if (countDown === 0) {
      setAutoStart(false);
      setCountDown(60); // 重置倒计时
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [autoStart, countDown]);

  // 获取验证码
  const getCodeApi = (phoneVal) => {
    setAutoStart(true);
    let data = {
      mobile: phoneVal,
      scene: 1, //1:手机号登陆
    };

    getSmsCode(data)
      .then((res: any) => {
        if (res.code >= 0) {
          //6的时候手机号已注册
          toast("success", {
            content: "验证码发送成功",
            duration: 2000,
          });
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        toast("error", {
          content: err,
          duration: 2000,
        });
      });
  };

  const codeLoginApi = (code) => {
    let data: any = {}
    data.mobile = phone;
    data.code = code;

    smsLogin(data).then((res: any) => {

      if (res.code == 0) {

        Taro.setStorageSync('userInfo', res.data)

        getUserInfo().then((res2: any) => {
          if (res2.code == 0) {
            //合并用户信息
            Taro.showToast({
              title: '登录成功',
              icon: 'none'
            })
            Taro.setStorageSync('userInfo', {
              ...res2.data,
              ...res.data
            })
            Taro.reLaunch({ url: '/pages/index/index' })
          }
        })
      } else {
        Taro.showToast({
          title: res.msg,
          icon: 'none'
        })
      }
    })

  }

  const resendClick = () => {
    if (autoStart) {
      return
    }
    getCodeApi(phone)
  }

  const handleCodeChange = (index: number, value: string) => {
    const newCode = [...inputCode];
    newCode[index] = value;
    setInputCode(newCode);
    
    // 自动跳转到下一个输入框
    if (value && index < 3) {
      // 设置下一个输入框为当前焦点
      Taro.nextTick(() => {
        setCurrentIndex(index + 1);
      });
    }
    const codeResult = newCode.join('')
    // 验证码输入完成后自动提交
    if (codeResult.length === 4) {
      codeLoginApi(codeResult);
    }
  };

  // 渲染带有激活状态的输入框
  const renderInputs = () => {
    return inputCode.map((digit, index) => (
      <Input
        key={index}
        border='none'
        className='code-input'
        type='number'
        maxLength={1}
        value={digit}
        onInput={(_, value) => handleCodeChange(index, value)}
        onClick={() => setCurrentIndex(index)}
        autoFocus={index === currentIndex}
      />
    ));
  };

  return (
    <View className='new-login'>
      <YkNavBar title=''/>
      <View className='logo-container'>
        <Image className='logo' src={require('../../assets/images/login/logo.png')} bottomOverlap={null} />
        <Text className='app-name'>验证码已发送至</Text>
        <Text className='phone-number'>{phone}</Text>
      </View>

      <View className='code-input-container'>
      <YiCode 
          size={4} 
          onConfirm={codeLoginApi} 
          isFocus={true}
        />
      </View>

      <View className='resend-code' onClick={resendClick}>
        <Text className='resend-text'>{!autoStart ? '重新获取验证码' : '重新获取(' + countDown + 's)'}</Text>
      </View>

    </View>
  )
}