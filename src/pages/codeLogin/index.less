@import '@arco-design/mobile-react/style/mixin.less';

.new-login {
  // min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });

  .logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    .rem(margin-top, 65);
    text-align: center;

    .logo {
      .rem(width, 70);
      .rem(height, 70);
      .rem(margin-bottom, 10);
    }

    .app-name {
      .text-medium();
      .rem(font-size, 18);
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: @dark-font-color;
      });
    }
  }

  .input-container {
    .rem(margin-top, 70);
    .input-field {
      .rem(width, 290);
      // .rem(height, 46);
      .rem(border-radius, 6);
      .rem(margin-bottom, 7);
      background-color: #F2F4F5;
      .use-dark-mode-query({
        background-color: #2B2B2B;
      });
    }
  }

  .login-buttons {
    .rem(margin-top, 15);

    .code-button {
      .rem(width, 290);
      .rem(height, 46);
      .rem(border-radius, 6);
    }

    .forgot-password {
      .rem(margin-top, 10);
      .use-var(color, sub-info-font-color);
    }
  }

  .pwd-login {
    position: fixed;
    .rem(bottom, 110);
    display: flex;
    flex-direction: column;
    align-items: center;
    .pwd-logo {
      .rem(width, 53);
      .rem(height, 53);
    }

    .pwd-text {
      .rem(margin-top, 5);
      .rem(font-size, 11);
      .use-var(color, sub-font-color);
      .use-dark-mode-query({
        color: @dark-sub-font-color;
      });
    }
  }

  .agreement {
    position: fixed;
    .rem(bottom, 70);
    .rem(font-size, 10);
    .use-var(color, sub-info-font-color);
    .use-dark-mode-query({
      color: @dark-sub-info-font-color;
    });
    display: flex;
    align-items: center;

    .link {
      .use-var(color, primary-color);
    }
  }
} 


.cus-popup-content {
  // background: #1890ff;
  // .rem(background-color, );
  .rem(margin-bottom, 24);

  .rem(padding, 24);
  .popup-close {
    position: absolute;
    .rem(top, 15);
    .rem(right, 15);
    .rem(width, 10);
    .rem(height, 10);
    // .rem(margin-bottom, 10);
  }
  .popup-title {
    .text-medium();
    .rem(font-size, 16);
    .rem(margin-bottom, 9);
    text-align: center;
    .use-var(color, font-color);
    .use-dark-mode-query({
      color: @dark-font-color;
    });
  }
  
  .popup-desc {
    .rem(font-size, 13);
    .use-var(color, sub-info-font-color);
    .use-dark-mode-query({
      color: @dark-sub-info-font-color;
    });
    // .rem(line-height, 1.5);
    .rem(margin-bottom, 24);
    text-align: left;
    .link {
      .use-var(color, primary-color);
    }
  }
  
  .popup-confirm {
    width: 100%;
    height: 44px;
    .rem(border-radius, 2);
    .rem(font-size, 15);
  }
}
