.box{
    position: relative;
    // background-color: red;

    .hideInput{
        position: absolute;
        top: 20px;
        color: rgba(1, 1, 1, 0);
        // background-color: red;
        left: -100px;
        padding-right: 100px;
        z-index: 2;
        width: 100%;
        border: 0;
        height: 80px;
    }

    .showInput{
        padding: 20px;
        display: flex;

        justify-content: space-between;
        .inputBox{
            position: relative;
            // background-color: red;
            
            .input{
                height: 80px;
                width: 80px;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .active{
                position: absolute;
                bottom: 0;
                left: 0;
                width: 80px;
                animation: myfirst 600ms infinite;
                @keyframes myfirst
                {
                    0%   {opacity: 0.1}
                    100% {opacity: 1}
                }
            }
        }

.pane {
    .input{
        border: 1px solid #f0f0f0;
    }
    .active{
        border: 1px solid #777;
    }
}

        .line{
            .input{
                border-bottom: 1px solid #f0f0f0;
            }
            .active{
                border-bottom: 2px solid #777;
            }
        }

    }

}
