import {CSSProperties, FC, useEffect, useState} from "react";
import {View, Input} from "@tarojs/components";
import './index.less';
// import { Button, Image, Input, NavBar } from '@arco-design/mobile-react'

interface IProps {
	size?: number,
	isFocus?: boolean,
	inputType?: 'text' | 'number',
	type?: 'pane' | 'line',
	onConfirm: (code: string) => void,
	inputStyle?: CSSProperties,
	activeStyle?: CSSProperties
}

export const YiCode: FC<IProps> = (props) => {
	
	const [code, setCode] = useState<string[]>([]);
	
	const config = {
		size: props.size || 6,
		isFocus: props.isFocus === undefined ? true : props.isFocus,
		type: props.type || 'pane',
		inputType: props.inputType || 'number'
	}
	
	const inputChange = (val: string | number) => {
		setCode(val.toString().split(''));
	}
	
	useEffect(() => {
		if(code.length === config.size){
			props.onConfirm(code.join(''));
		}
	}, [code])
	
	let input_className = '';
	if(config.type === 'pane'){
		input_className = ['inputBox', 'pane'].join();
	}else{
		input_className = ['inputBox', 'line'].join();
	}
	
	return <View className={'box'}>
		<View className={'showInput'}>
			{Array.from(new Array(config.size).keys()).map((i) => {
				return <View className={'inputBox pane'}>
					<Input disabled={true} value={code[i] || ''} key={i} className={'input'} style={props.inputStyle}  type={'text'} maxlength={1} />
					{code.length === i && <View className={'active'} style={props.activeStyle} />}
				</View>
			})}
		</View>
		<Input className={'hideInput'} style={{ height: props.inputStyle && props.inputStyle.height ? props.inputStyle.height: '80rpx' }} type={config.inputType} onInput={(e) => inputChange(e.detail.value)} maxlength={config.size} focus={config.isFocus}/>
	</View>
}