import { View, Text } from '@tarojs/components'
import { Button, Image, Input, Checkbox, Popup } from '@arco-design/mobile-react'
import YkNavBar from '@/components/ykNavBar'
import './index.less'
import Taro from '@tarojs/taro'

import { useState } from 'react';

export default function CodeLogin() {
  const [input, setInput] = useState('');
  const [check, setCheck] = useState(false);
  const [visible, setVisible] = useState(false);


  const goCode = () => {
    if (input.length !== 13) {
      Taro.showToast({
        title: "请输入正确的手机号",
        icon: "none",
      });
      return;
    }
    Taro.navigateTo({url: '/pages/codeLogin/code?phone=' + input.replace(/ /g, '')});
  }

  return (
    <View className='new-login'>
      <YkNavBar title=''/>
      <View className='logo-container'>
        <Image className='logo' src={require('../../assets/images/login/logo.png')} bottomOverlap={null} />
        <Text className='app-name'>验证码登录</Text>
      </View>

      <View className='input-container'>
        <Input className='input-field' placeholder='请输入手机号' type='tel' border='none'             value={input}
            maxLength={13}
            onInput={(_, value) => {
                const newValue = (value || '').replace(/ /g, '');
                setInput(`${newValue.slice(0, 3)} ${newValue.slice(3,7)} ${newValue.slice(7)}`.trim());
            }} />
      </View>

      <View className='login-buttons'>
        <Button className='code-button' onClick={goCode}>获取验证码</Button>
      </View>

      <View className='pwd-login' onClick={() => {Taro.navigateBack()}}>
        <Image className='pwd-logo' src={require('../../assets/images/login/pwdlogin.png')} bottomOverlap={null} />
        <Text className='pwd-text'>密码登录</Text>
      </View>

      <View className='agreement'>
      <Checkbox value={''} checked={check} onChange={() => { setCheck(!check) }}></Checkbox>
      <Text>已阅读并同意</Text>
        <Text className='link' onClick={() => {Taro.navigateTo({url: '/pages/webView/index?id=123&name=用户协议'})}}>《用户协议》</Text>
        <Text>和</Text>
        <Text className='link' onClick={() => {Taro.navigateTo({url: '/pages/webView/index?id=126&name=隐私政策'})}}>《隐私政策》</Text>
      </View>


      <Popup visible={visible} close={() => setVisible(false)} contentStyle={{ borderRadius: '10px 10px 0 0' }} direction={'bottom'}>
        <View className='cus-popup-content'>
          <Image src={require('../../assets/images/login/close.png')} className='popup-close' onClick={() => setVisible(false)} bottomOverlap={null} />
          <View className="popup-title">用户协议及隐私保护</View>
          <View className="popup-desc">
            <Text>为了更好地保障您的合法权益，请您阅读并同意以下协议</Text>
            <Text className='link' onClick={() => { Taro.navigateTo({ url: '/pages/webView/index?id=123&name=用户协议' }); setVisible(false) }}>《用户协议》</Text>
            <Text>和</Text>
            <Text className='link' onClick={() => { Taro.navigateTo({ url: '/pages/webView/index?id=126&name=隐私政策' }); setVisible(false) }}>《隐私政策》</Text>
            <Text>的全部内容。</Text>
          </View>
          <Button className="popup-confirm" onClick={() => {
            setCheck(true);
            // handleWechatLogin();
            setVisible(false);
          }}>
            同意并继续
          </Button>
        </View>
      </Popup>
    </View>
  )
} 