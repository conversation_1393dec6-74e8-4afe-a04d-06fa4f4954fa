// import { View, Text } from "@tarojs/components";
// import { useLoad, useReachBottom } from "@tarojs/taro";
// import "./index.less";
// import {
//   Toast,
//   TabBar,
//   LoadMore,
//   Tabs,
//   Sticky,
//   Image,
//   Ellipsis,
//   Button,
// } from "@arco-design/mobile-react";
// import {
//   IconUser,
//   IconHome,
//   IconSetting,
//   IconNotice,
//   IconGift,
//   IconHeart,
//   IconDownload,
// } from "@arco-design/mobile-react/esm/icon";
// import Taro from "@tarojs/taro";
// import { getAlbumList } from "@/utils/api/common/common_user";
// import React from "react";
// import { LoadMoreStatus } from "@arco-design/mobile-react/cjs/load-more";

// export default function Home() {
//   const tabs = [
//     {
//       title: "相册",
//       icon: <IconHome />,
//       url: "/pages/index/index",
//     },
//     {
//       title: "好友",
//       icon: <IconUser />,
//       url: "/pages/friends/index",
//     },
//     {
//       title: "工作台",
//       icon: <IconNotice />,
//       url: "/pages/workbench/index",
//     },
//     {
//       title: "我的",
//       icon: <IconSetting />,
//       url: "/pages/my/index",
//     },
//   ];
//   // 点击tabbar
//   const onTabBarClick = (tab: any) => {
//     console.log(tab);
//     Taro.switchTab({
//       url: tab.url,
//     });
//   };
//   // 提示框
//   const toast = (func, options) => {
//     if (!!window.toastInstance) {
//       window.toastInstance.close();
//     }
//     window.toastInstance = Toast[func](options);
//   };

//   const [albumList, setAlbumList] = React.useState<any>([]);
//   const [albumPage, setAlbumPage] = React.useState(1);
//   const [albumLimit, setAlbumLimit] = React.useState(10);
//   const [albumType, setAlbumType] = React.useState(0); // 0:全部 1:上新
//   //是否已经加载完 ，且没有更多数据
//   const [isLoadEnd, setIsLoadEnd] = React.useState(false);
//   // 加载状态
//   const [loadStatus, setLoadStatus] = React.useState<LoadMoreStatus>("prepare"); //"before-ready" | "prepare" | "loading" | "nomore" | "retry"
//   // 是否显示省略号,把id添加进来
//   const [ellipsis, setEllipsis] = React.useState<any>([]);

//   // 展开or收起
//   const handleEllipsis = (id: string) => {
//     console.log(ellipsis,id);
//     if (ellipsis.includes(id)) {
//       setEllipsis(ellipsis.filter((item) => item !== id));
//     } else {
//       setEllipsis([...ellipsis, id]);
//     }
//   };

//   // 获取相册列表
//   const getAlbumListData = async () => {
//     let data = {
//       page: albumPage,
//       limit: albumLimit,
//       type: albumType,
//     };
//     if (isLoadEnd) {
//       setLoadStatus("nomore");
//       return;
//     }
//     const res: any = await getAlbumList(data);
//     if (res.code == 0) {
//       setAlbumList(res.data);
//       // 判断返回数据的总条数
//       if (res.data.length < albumLimit) {
//         setIsLoadEnd(true);
//         setLoadStatus("nomore");
//       } else {
//         setLoadStatus("prepare");
//         setIsLoadEnd(false);
//         setAlbumPage(albumPage + 1);
//       }
//     } else {
//       setLoadStatus("retry");
//       toast("error", {
//         content: res.msg,
//         duration: 2000,
//       });
//     }
//   };

//   // const tabData = [{ title: "全部" }, { title: "上新" }];
//   const tabData = [{ title: "全部" }];

//   const onTabsChange = (e) => {
//     console.log(e);
//   };

//   // 初始化
//   useLoad(() => {
//     console.log("Page loaded.");
//     getAlbumListData();
//   });
//   // 上拉触底
//   useReachBottom(() => {
//     setLoadStatus("loading");
//     setTimeout(() => {
//       getAlbumListData();
//     }, 2000);
//   });

//   return (
//     <View className="switchBox">
//       <Sticky topOffset={0}>
//       <View className="switchBox-title">
//         <Text className="switchBox-title-text">相册</Text>
//       </View>
//       </Sticky>
//       <Tabs
//         tabBarStyle={{ padding: "0 18px" }}
//         tabs={tabData}
//         tabBarHasDivider={false}
//         swipeable
//         tabBarArrange={"start"}
//         onChange={onTabsChange}
//         renderTabBar={(TabBar) => <Sticky topOffset={44}>{TabBar}</Sticky>}
//       >
//         <View className="showAlbumBox">
//           {albumList.map((item, index) => {
//             return (
//               <View className="showAlbumBox-item">
//                 <View className="showAlbumBox-item-top">
//                   <Image
//                     fit="cover"
//                     showLoading={true}
//                     showError={true}
//                     src={item.head_img}
//                     className="showAlbumBox-item-top-image"
//                     radius="50%"
//                   />
//                   <View className="showAlbumBox-item-top-content">
//                     <View className="showAlbumBox-item-top-content-name">
//                       {item.nickname}
//                     </View>
//                     <View className="showAlbumBox-item-top-content-date">
//                       {item.date}
//                     </View>
//                   </View>
//                 </View>
//                 <View className="showAlbumBox-item-title">
//                   <Ellipsis
//                     className="showAlbumBox-item-title-content"
//                     text={item.dynamic_title}
//                     maxLine={3}
//                     ellipsisNode={<span className="demo-link">...<span style={{color:'#355A95'}}>展开</span></span>}
//                     collapseNode={<span className="demo-link"  style={{color:'#355A95'}}>收起</span>}
//                     ellipsis={!ellipsis.includes(item.id)}
//                     onCollapseNodeClick={() => handleEllipsis(item.id)}
//                     onEllipsisNodeClick={() =>  handleEllipsis(item.id)}
//                     reflowOnResize={true}
//                   />
//                 </View>
//                 <View className="showAlbumBox-item-center">
//                   {item.dynamic_img &&
//                    item.dynamic_img.split("|").map((item2, index2) => {
//                       return (
//                         index2 < 9 && <Image
//                           fit="cover"
//                           showLoading={true}
//                           showError={true}
//                           src={item2}
//                           className="showAlbumBox-item-center-item"
//                           key={index2}
//                         />
//                       );
//                     })}
//                 </View>
//                 <View className="showAlbumBox-item-jiage">
//                   <View className="showAlbumBox-item-jiage-icon">￥</View>
//                   <View className="showAlbumBox-item-jiage-val">
//                     {item.price}
//                   </View>
//                 </View>
//                 <View className="showAlbumBox-item-playMode">
//                   <Text className="showAlbumBox-item-playMode-link">删除</Text>
//                   <Text className="showAlbumBox-item-playMode-link">下架</Text>
//                   <Text className="showAlbumBox-item-playMode-link">刷新</Text>
//                   <Text className="showAlbumBox-item-playMode-link">置顶</Text>
//                   <Text className="showAlbumBox-item-playMode-link">编辑</Text>
//                 </View>
//                 <View className="showAlbumBox-item-playBtn">
//                   <View className="showAlbumBox-item-playBtn-left">
//                     <View className="showAlbumBox-item-playBtn-left-item">
//                       <IconHeart className="showAlbumBox-item-playBtn-left-item-icon" />
//                       <Text className="showAlbumBox-item-playBtn-left-item-text">
//                         收藏
//                       </Text>
//                     </View>
//                     <View className="showAlbumBox-item-playBtn-left-item">
//                       <IconDownload className="showAlbumBox-item-playBtn-left-item-icon" />
//                       <Text className="showAlbumBox-item-playBtn-left-item-text">
//                         下载
//                       </Text>
//                     </View>
//                   </View>
//                   <Button
//                       inline
//                       shape="round"
//                       icon={<IconGift />}
//                       type="ghost"
//                     >
//                       一键分享
//                     </Button>
//                 </View>
//               </View>
//             );
//           })}

//           <LoadMore
//             style={{ paddingTop: 16, paddingBottom: 16 }}
//             status={loadStatus}
//             threshold={0}
//           />
//         </View>
//       </Tabs>
//       <TabBar fixed activeIndex={0} style={{ zIndex: 10, background: "#FFF" }}>
//         {tabs.map((tab, index) => (
//           <TabBar.Item
//             title={tab.title}
//             icon={tab.icon}
//             key={index}
//             onClick={() => onTabBarClick(tab)}
//           />
//         ))}
//       </TabBar>
//     </View>
//   );
// }
