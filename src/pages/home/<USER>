// .switchBox {
//   position: relative;
//   // width: 100vw;
//   box-sizing: border-box;
//   margin-bottom: 40px;

//   &-title{
//     width: 100%;
//     height: 44px;
//     display: flex;
//     align-items: center;
//     background-color: #FFFFFF;

//     &-text{
//         padding-left: 18px;
//         color: #000000;
//         font-size: 15px;
//         font-weight: bold;
//     }
//   }
// }

// .showAlbumBox {
//   position: relative;
//   width: 100%;
//   box-sizing: border-box;
// //   padding-top: 44px;

//   &-item {
//     position: relative;
//     padding: 15px;
//     box-sizing: border-box;
//     width: 100%;

//     &-title {
//       width: 100%;
//       margin-top: 8px;
//       box-sizing: border-box;
//       padding-left: 46px;
//     }

//     &-top {
//       width: 100%;
//       height: 40px;
//       display: flex;
//       &-image {
//         width: 40px;
//         height: 40px;
//       }

//       &-content {
//         flex: 1;
//         padding-left: 10px;
//         display: flex;
//         flex-direction: column;
//         justify-content: space-between;

//         &-name {
//           font-size: 13px;
//           color: #576b95;
//         }
//         &-date {
//           font-size: 12px;
//           color: #999999;
//         }
//       }
//     }

//     &-center {
//       width: 100%;
//       margin-top: 8px;
//       box-sizing: border-box;
//       padding-left: 46px;
//       display: grid;
//       grid-template-columns: repeat(3, 1fr);
//       /* 定义三列，每列宽度相等 */
//       gap: 4px;
//       /* 格子之间的间距 */
//       &-item {
//         width: 97px;
//         height: 97px;
//         background-color: #ccc;
//       }
//     }

//     &-jiage {
//       width: 100%;
//       height: 44px;
//       display: flex;
//       box-sizing: border-box;
//       padding-left: 46px;
//       align-items: center;
//       color: #eb483f;

//       &-icon {
//         font-size: 12px;
//       }

//       &-val {
//         font-size: 18px;
//         font-weight: bold;
//       }
//     }

//     &-playMode {
//       width: 100%;
//       padding: 10px;
//       box-sizing: border-box;
//       padding-left: 46px;
//       display: flex;
//       flex-wrap: wrap;
//       gap: 16px;

//       &-link {
//         color: #576b95;
//         font-size: 12px;

//         &:active {
//           opacity: 0.8;
//         }
//       }
//     }

//     &-playBtn {
//       width: 100%;
//       display: flex;
//       justify-content: space-between;
//       align-items: center;
//       box-sizing: border-box;
//       padding-top: 10px;
//       padding-left: 46px;

//       &-left {
//         display: flex;

//         &-item {
//           width: 40px;
//           margin-right: 10px;
//           display: flex;
//           flex-direction: column;
//           justify-content: flex-start;
//           align-items: center;

//           &:active {
//             opacity: 0.8;
//           }

//           &-icon {
//             width: 18px;
//             height: 18px;
//             color: #999999;
//           }

//           &-text {
//             font-size: 12px;
//             color: #999999;
//           }
//         }
//       }

//       &--right {
//         height: 40px;
//       }
//     }
//   }
// }
