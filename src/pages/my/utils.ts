// my页面工具函数

// 用户信息接口（基于userInfo中的字段）
export interface UserInfo {
  id?: string;
  nickname?: string;
  avatar?: string;
  isMember?: number;
  memberExpireTime?: string; // 格式: YYYY-MM-DD HH:mm:ss
}

// 会员状态枚举（保留用于调试）
export enum VipStatus {
  NON_VIP = 0,    // 非会员
  VIP = 1,        // 会员
  EXPIRED = 2     // 过期会员
}

// 会员信息接口（保留用于调试）
export interface VipInfo {
  isVip: VipStatus;
  expireDate?: string; // 格式: YYYY/MM/DD
  expireTime?: string; // 格式: YYYY-MM-DD HH:mm:ss
}

/**
 * 基于userInfo的会员状态判断和处理函数
 */

/**
 * 检查会员是否有效（基于userInfo）
 */
export const isMemberValid = (userInfo: UserInfo | null): boolean => {
  if (!userInfo || !userInfo.isMember) {
    return false;
  }

  // 如果没有过期时间，认为是有效的
  if (!userInfo.memberExpireTime) {
    return true;
  }

  // 检查是否过期
  const expireTime = new Date(userInfo.memberExpireTime);
  const now = new Date();
  return expireTime > now;
};

/**
 * 获取会员状态文本（基于userInfo）
 */
export const getMemberStatusText = (userInfo: UserInfo | null): string => {
  if (!userInfo || !userInfo.isMember) {
    return '开通会员尊享超多权益';
  }

  if (!userInfo.memberExpireTime) {
    return '会员用户';
  }

  const expireTime = new Date(userInfo.memberExpireTime);
  const now = new Date();

  if (expireTime <= now) {
    return '会员已过期';
  }

  // 格式化显示日期 YYYY/MM/DD
  const expireDate = expireTime.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).replace(/\//g, '/');

  return `会员到期日：${expireDate}`;
};

/**
 * 格式化会员过期时间用于URL参数
 */
export const formatMemberExpireTime = (userInfo: UserInfo | null): string => {
  if (!userInfo?.memberExpireTime) return '';
  // 将 YYYY-MM-DD HH:mm:ss 格式转换为 YYYY-MM-DD
  return userInfo.memberExpireTime.split(' ')[0];
};

/**
 * 原有的VIP相关函数（保留用于调试）
 */

/**
 * 获取会员状态文本
 */
export const getVipStatusText = (vipInfo: VipInfo): string => {
  if (vipInfo.isVip === VipStatus.VIP && vipInfo.expireDate) {
    return `会员有效期：${vipInfo.expireDate}`;
  } else if (vipInfo.isVip === VipStatus.EXPIRED) {
    return '会员已过期';
  } else {
    return '开通会员尊享超多权益';
  }
};

/**
 * 检查会员是否有效
 */
export const isVipValid = (vipInfo: VipInfo): boolean => {
  return vipInfo.isVip === VipStatus.VIP;
};

/**
 * 格式化到期时间用于URL参数
 */
export const formatExpireDateForUrl = (expireTime?: string): string => {
  if (!expireTime) return '';
  // 将 YYYY-MM-DD HH:mm:ss 格式转换为 YYYY-MM-DD
  return expireTime.split(' ')[0];
};

/**
 * 我的页面调试配置
 */
import { ScenarioConfig } from '@/pages/debug';

// 会员相关调试场景配置
export const myPageDebugScenarios: Record<string, ScenarioConfig> = {
  'non_vip': {
    displayName: '非会员用户',
    icon: '👤',
    description: '未开通会员的普通用户状态',
    color: 'primary'
  },
  'vip_user': {
    displayName: '会员用户',
    icon: '✅',
    description: '已开通会员的用户状态',
    color: 'success'
  },
  'vip_expiring_soon': {
    displayName: '即将过期',
    icon: '⏰',
    description: '会员即将过期的用户状态',
    color: 'warning'
  },
  'expired_vip': {
    displayName: '已过期会员',
    icon: '❌',
    description: '会员已过期的用户状态',
    color: 'danger'
  }
};

// 会员模拟数据生成器
export const myPageMockDataGenerators: Record<string, (scenario: string) => VipInfo> = {
  'non_vip': () => ({
    isVip: VipStatus.NON_VIP
  }),
  'vip_user': () => {
    const futureDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000);
    return {
      isVip: VipStatus.VIP,
      expireDate: futureDate.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).replace(/\//g, '/'),
      expireTime: futureDate.toISOString().slice(0, 19).replace('T', ' ')
    };
  },
  'vip_expiring_soon': () => {
    const soonDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
    return {
      isVip: VipStatus.VIP,
      expireDate: soonDate.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).replace(/\//g, '/'),
      expireTime: soonDate.toISOString().slice(0, 19).replace('T', ' ')
    };
  },
  'expired_vip': () => {
    const pastDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    return {
      isVip: VipStatus.EXPIRED,
      expireDate: pastDate.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).replace(/\//g, '/'),
      expireTime: pastDate.toISOString().slice(0, 19).replace('T', ' ')
    };
  }
};
