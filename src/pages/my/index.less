@import "@arco-design/mobile-react/style/mixin.less";
@import "@/utils/css/variables.less";

[id^="/pages/my/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  .switchBox {
    position: relative;
    width: 100vw;
    box-sizing: border-box;
    margin-bottom: 40px;

    &-title {
      width: 100%;
      height: 44px;
      display: flex;
      align-items: center;
      background-color: #ffffff;

      &-text {
        padding-left: 18px;
        color: #000000;
        font-size: 15px;
        font-weight: bold;
      }
    }
  }

  .userInfoBox {
    position: relative;
    width: calc(100% - 20px);
    display: flex;
    box-sizing: border-box;
    padding: 10px;
    margin: 10px 12px;
    background: var(--container-background-color);
    .use-dark-mode-query({
      background: var(--dark-container-background-color);
    });

    &-image {
      width: 56px;
      height: 56px;
    }
    &-content {
      flex: 1;
      padding-left: 20px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &-name {
        font-size: 18px;
        font-weight: bold;
        color: var(--font-color);
        .use-dark-mode-query({
            color: var(--dark-font-color);
        });
      }
      &-id {
        font-size: 14px;
        font-weight: bold;
        color: var(--sub-info-font-color);
        .use-dark-mode-query({
            color: var(--dark-sub-info-font-color);
        });
      }
    }

    &-iconBox {
      width: 18px;
      height: 56px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 15px;

      &-icon {
        font-size: 18px;

        &:active {
          opacity: 0.6;
        }
      }

      &-icon [fill] {
        fill: var(--font-color);
        .use-dark-mode-query({
            fill: var(--dark-font-color);
        });
      }
      &-icon [stroke] {
        stroke: var(--font-color);
        .use-dark-mode-query({
            stroke: var(--dark-font-color);
        });
      }
    }
  }

  // 菜单列表
  .menuList {
    width: 100%;
    box-sizing: border-box;
    padding: 0 12px;
    &-itemIcon {
      font-size: 18px;
    }
    &-itemIcon [fill] {
      fill: var(--cell-label-icon-color);
      .use-dark-mode-query({
            fill: var(--dark-cell-label-icon-color);
        });
    }
    &-itemIcon [stroke] {
      stroke: var(--cell-label-icon-color);
      .use-dark-mode-query({
          stroke: var(--dark-cell-label-icon-color);
        });
    }
  }

  .page {
    min-height: 100vh;
    overflow-y: auto;
    padding-bottom: 60px;
    // background: #f8f9fa;
  }

  .userInfo {
    margin: 20px;
    padding: 20px;
    background: var(--container-background-color);
    .use-dark-mode-query({
      background: var(--dark-container-background-color);
    });
    border-radius: 12px;
    display: flex;
    align-items: center;

    .avatar {
      width: 60px;
      height: 60px;
    }

    .info {
      flex: 1;
      margin-left: 15px;

      .nickname {
        font-size: 18px;
        font-weight: bold;
        color: var(--font-color);
        .use-dark-mode-query({
          color: var(--dark-font-color);
        });
      }

      .userId {
        font-size: 14px;
        color: var(--sub-info-font-color);
        .use-dark-mode-query({
          color: var(--dark-sub-info-font-color);
        });
        margin-top: 4px;
      }
    }

    .arrow {
      color: #999;
      font-size: 20px;
    }
  }

  .member-card {
    margin: 0px 15px 10px 15px;
    position: relative;
    background: #1a1a2e;
    border-radius: 16px;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #f5e6c8;
    font-size: 14px;
    overflow: hidden;

    &-blur1,
    &-blur2 {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.08);
      z-index: 0;
    }

    &-blur1 {
      width: 184px;
      height: 134px;
      top: -89px;
      right: 80px;
      transform: rotate(180deg);
    }

    &-blur2 {
      width: 276px;
      height: 200px;
      top: -102px;
      right: 150px;
      transform: rotate(180deg);
    }

    .vip-badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 2px 6px; // 紧凑的内边距
      background: linear-gradient(
        269deg,
        #fe9460 0%,
        #ffebc0 97%
      ); // 橙色渐变背景
      border: 0.5px solid #ffcf8b; // 金色边框
      border-radius: 6px; // 圆角
      max-width: 100%; // 防止溢出

      .vip-text {
        font-family: "PingFang SC";
        font-size: 11px; // 小字体
        font-weight: 900; // 超粗字体
        line-height: 1.2; // 紧凑行高
        color: #1d2129; // 深色文字
        .use-var(color, font-color); // 支持主题变量
      }
    }

    &-text {
      font-size: 16px;
      text-align: left;
      z-index: 1;
    }

    &-btn {
      z-index: 1;
      flex-shrink: 0;
      background: linear-gradient(135deg, #ff7f4a, #f64a4a);
      color: white;
      font-size: 14px;
      padding: 6px 16px;
      border-radius: 20px;
      border: none;
      min-width: auto;
      width: auto;

      // 移除 Arco Button 的默认阴影或边框
      box-shadow: none;
    }

    // 已开通会员状态样式
    &.member-valid {
      background: #FCC59F; // 使用中浅橙色背景
      color: #333; // 深色文字以确保可读性

      .member-card-text {
        color: #333; // 文本颜色调整为深色
      }

      .member-card-btn {
        background: linear-gradient(135deg, #ff7f4a, #f64a4a); // 保持按钮原有样式
        color: white;
      }

      // 调整模糊效果颜色以适配新背景
      .member-card-blur1,
      .member-card-blur2 {
        background: #ffffff26; // 稍微增加透明度以适配浅色背景
      }

      // VIP标记样式
      .member-card-vip {
        // position: absolute;
        // top: 12px;
        // left: 12px;
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        color: white;
        font-size: 12px;
        font-weight: bold;
        padding: 4px 8px;
        border-radius: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 6px rgba(255, 107, 53, 0.3);
        z-index: 10;

        // 添加小的光泽效果
        &::before {
          content: '';
          position: absolute;
          top: 1px;
          left: 2px;
          right: 2px;
          height: 50%;
          background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), transparent);
          border-radius: 8px 8px 0 0;
          pointer-events: none;
        }
      }
    }
  }

  .menuGrid {
    margin: 20px;
    padding: 20px;
    background: var(--container-background-color);
    .use-dark-mode-query({
      background: var(--dark-container-background-color);
    });
    border-radius: 12px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;

    .menuItem {
      display: flex;
      flex-direction: column;
      align-items: center;
      .menuItem-icon {
        width: 24px;
        height: 24px;
        overflow: visible;
      }

      .menuText {
        margin-top: 8px;
        font-size: 14px;
        color: var(--font-color);
        .use-dark-mode-query({
          color: var(--dark-font-color);
        });
      }
    }
  }

  .orderStatsBox {
    background: var(--container-background-color);
    .use-dark-mode-query({
      background: var(--dark-container-background-color);
    });
    margin: 16px;
    border-radius: 12px;
    padding: 16px;
    color: var(--font-color);
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });

    .orderStatsTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .viewAll {
        color: var(--sub-info-font-color);
        .use-dark-mode-query({
          color: var(--dark-sub-info-font-color);
        });
        font-size: 14px;
      }
    }

    .orderStatsContent {
      display: flex;
      justify-content: space-between;

      .statsItem {
        display: flex;
        flex-direction: column;
        align-items: center;

        .number {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 4px;
        }

        .label {
          font-size: 12px;
          color: var(--sub-info-font-color);
          .use-dark-mode-query({
            color: var(--dark-sub-info-font-color);
          });
        }
      }
    }
  }

  .bottomMenu {
    margin: 20px;
    // padding: 20px;
    background: var(--container-background-color);
    .use-dark-mode-query({
      background: var(--dark-container-background-color);
    });
    border-radius: 12px;
    overflow: hidden;
  }
}
