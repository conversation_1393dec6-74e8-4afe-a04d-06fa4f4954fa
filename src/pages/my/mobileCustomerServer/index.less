@import "@arco-design/mobile-react/style/mixin.less";


[id^="/pages/my/mobileCustomerServer/index"] {
  /* 在这里设置样式 */
  background-color: @background-color;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
  // background: #f5f5f5;

  .pc_customerServer_container {
    width: 100%;
    height: calc(100vh - 44px);
    display: flex;
    position: fixed;
    flex-direction: column;
    justify-content: space-between;
    // background: #f5f5f5;

    background-color: @background-color;
    .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });
  
    &_content {
      flex: 1;
      overflow: hidden;
      position: relative;
      .scroll_content {
        position: absolute;
        width: 98%;
        height: 100%;
        overflow-y: auto;
        padding-bottom: 34px;
        box-sizing: border-box;
        .chart_list {
          position: relative;
          z-index: 2;
          &_item {
            &_content {
              display: flex;
              align-items: center;
              padding: 8px;
            }
  
            &_avatar {
              // width: 33px;
              // height: 33px;
              // border-radius: 50%;
              overflow: hidden;
              margin-right: 10px;
              align-self: flex-start;
              img {
                width: 100%;
                height: 100%;
              }
            }
            &_text {
              max-width: 60%;
              word-wrap: break-word;
              background: #fff;
              padding: 9px 14px;
              font-size: 15px;
              border-radius: 6px;
              overflow: hidden;
            }
            &_img {
              max-width: 60%;
              border-radius: 6px;
              background-color: @background-color;
              .use-dark-mode-query({
                background-color: @dark-background-color !important;
              });

              img {
                width: 100%;
                height: auto;
                border-radius: 6px;
                overflow: hidden;
              }
            }
            .chart_list_item_imgOrText {
              background: #fff;
              padding: 4px 10px;
              border-radius: 8px;
              width: 226px;
              box-sizing: border-box;
              .order-wrapper {
                .img-box {
                  width: 100%;
                  img {
                    width: 100%;
                    height: auto;
                  }
                }
                .order-info {
                  .price-box {
                    color: #ff0000;
                    font-size: 18px;
                  }
                  .name {
                    font-size: 14px;
                  }
                }
              }
            }
  
            &_time {
              text-align: center;
              margin: 10px auto;
              font-size: 13px;
              color: @sub-font-color;
              .use-dark-mode-query({
                color: @dark-sub-font-color;
              });
            }
            .right-box {
              flex-direction: row-reverse;
              .chart_list_item_avatar {
                margin-left: 10px;
              }
              .chart_list_item_text {
                text-align: right;
                background: #cde0ff;
                color: #000;
              }
              .chart_list_item_img {
                text-align: right;
                // background: #fff;
                // background-color: #f5f5f5;
                background-color: @background-color;
                .use-dark-mode-query({
                  background-color: @dark-background-color !important;
                });

                img {
                  width: 100%;
                  height: auto;
                }
              }
              .chart_list_item_imgOrText {
                background: #fff;
                padding: 10px;
                border-radius: 8px;
                width: 226px;
                box-sizing: border-box;
                .order-wrapper {
                  .img-box {
                    width: 100%;
                    img {
                      width: 100%;
                      height: auto;
                    }
                  }
                  .order-info {
                    .price-box {
                      color: #ff0000;
                      font-size: 18px;
                    }
                    .name {
                      font-size: 14px;
                    }
                  }
                }
              }
            }
            &_text {
            }
          }
        }
      }
    }
  
    .footer_customerServer_container {
      flex-shrink: 0;
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      height: 56px;
      transition: bottom 0.2s ease-out;
      z-index: 100;
      background-color: @background-color;
      .use-dark-mode-query({
        background-color: @dark-background-color;
      });
    }
    .mobel_customerServer_container_footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-top: 1px solid #ececec;
      .use-dark-mode-query({
        border-top: 1px solid #3e3e3e;
      });

      padding: 8px 15px;
      box-sizing: border-box;
      position: relative;

      .iconfont {
        font-size: 24px;
      }
      .mobel_customerServer_container_footer_uploag_image {
        font-size: 24px;
        display: flex;
        align-items: center;
        position: relative;
        color: @font-color;
        .use-dark-mode-query({
          background-color: @dark-font-color;
        });
      }
      .file_input {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
      }
      &_input {
        flex: 1;
        margin: 0 9px;
        // background: #fff;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        // line-height: 28px;
  
        &_con {
          flex: 1;
          position: relative;
          min-height: 32px;
          display: flex;
          align-items: center;
          .font {
            // font-size: 14px;
            padding: 4px 8px;
          }
          Textarea {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            resize: none;
            outline: none;
            // background: none;
            // color: rgba(0, 0, 0, 0.7);
            border: none;
            width: 100%;
            height: 100%;
            font-size: 15px;
          }
          p {
            width: 100%;
            display: block;
            min-height: 20px;
            opacity: 0;
          }
        }
  
        &_send {
          align-self: flex-start;
          padding-right: 8px;
        }
      }
  
      &_emoji {
        display: flex;
        align-items: center;
        font-size: 24px;
        color: @font-color;
        .use-dark-mode-query({
          background-color: @dark-font-color;
        });
        // color: @dark-background-color;
        // .use-dark-mode-query({
        //   background-color: @background-color;
        // });
      }
      .sendMessage {
        background: #ccc;
        padding: 4px 12px;
        border-radius: 4px;
        margin-left: 8px;
        color: #fff;
        font-size: 14px;
        white-space: nowrap;  // 添加这行确保文本不换行

      }
      .sendMessage-primary {
        background: #3875ea;
      }
      &_emojiList {
        width: 100%;
        height: 0;
        // padding: 10px;
        display: grid;
        grid-template-columns: repeat(9, 1fr);
        grid-row-gap: 20px;
        overflow-y: auto;
        background: #fff;
        transition: 0.3s;
      }
    }
  }
  .canSelectemoji {
    height: 165px !important;
    padding: 10px;
  }
  
  .demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }
  
  .primary_color {
    color: #3875ea;
  }

  .pt140 {
    padding-bottom: 140px !important;
  }
  @keyframes ani-demo-spin {
    from {
      transform: rotate(0deg);
    }
  
    50% {
      transform: rotate(180deg);
    }
  
    to {
      transform: rotate(360deg);
    }
  }
  :global(.happy-scroll-content) {
    width: 100%;
    box-sizing: border-box;
  }

  .emoji-picker-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1px solid #eee;
    padding: 10px;
    height: 200px;
    z-index: 99;
    box-sizing: border-box;
    
    .emoji-list {
      display: grid;
      grid-template-columns: repeat(8, 1fr);
      gap: 10px;
      height: 100%;
      overflow-y: auto;

      .emoji-item {
        text-align: center;
        cursor: pointer;
        font-size: 20px;
        
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
}
