import Taro from '@tarojs/taro';
// import { EventEmitter } from 'events';
class SimpleEventEmitter {
    constructor() {
        this.events = {};
    }

    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }

    emit(event, data) {
        if (this.events[event]) {
            this.events[event].forEach(callback => callback(data));
        }
    }

    removeAllListeners() {
        this.events = {};
    }
}

class WebSocketClient extends SimpleEventEmitter {
    constructor(opt) {
        super();
        this.ws = null;
        this.opt = opt || {};
        this.networkStatus = true;
        this.socketStatus = false;
        this.reconneMax = 100;
        this.connectLing = false;
        this.reconneTimer = null;
        this.reconneCount = 0;
        this.timer = null;

        this.init(opt);
        this.watchNetwork();
    }

    watchNetwork() {
        Taro.onNetworkStatusChange((res) => {
            this.networkStatus = res.isConnected;
            if (!res.isConnected) {
                this.socketStatus = false;
                this.timer && clearInterval(this.timer);
                this.timer = null;
                this.ws?.close();
                console.log('网络已断开');
            } else {
                console.log('网络已连接');
            }
        });
    }

    reconnect() {
        if (this.reconneCount > this.reconneMax) {
            this.reconneTimer && clearInterval(this.reconneTimer);
            return;
        }
        if (this.reconneTimer || this.socketStatus) {
            return;
        }
        this.reconneTimer = setInterval(() => {
            if (this.socketStatus) return;
            if (!this.connectLing) {
                console.log('正在重新连接');
                this.init(this.opt);
                this.reconneCount++;
            }
        }, 2000);
    }

    init(opt) {
        if (this.socketStatus) return;

        let wsUrl = '';
        const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
        const hostUrl = `${protocol}pipakf.fjpipixia.com/ws`;

        if (opt.type === 'user') {
            wsUrl = `${hostUrl}?type=user&form=${opt.form}&token=${opt.token}`;
            if (opt.tourist_uid) {
                wsUrl += `&tourist_uid=${opt.tourist_uid}`;
            }
        }

        if (wsUrl) {
            this.connectLing = true;
            Taro.connectSocket({
                url: wsUrl,
                success: () => {
                    console.log('WebSocket连接成功');
                },
                fail: (err) => {
                    console.error('WebSocket连接失败', err);
                    this.onError(err);
                }
            }).then(task => {
                this.ws = task;

                task.onOpen(() => {
                    this.onOpen();
                });

                task.onError((err) => {
                    this.onError(err);
                });

                task.onMessage((msg) => {
                    this.onMessage(msg);
                });

                task.onClose(() => {
                    this.onClose();
                });
            }).catch(err => {
                console.error('WebSocket连接错误:', err);
                this.onError(err);
            });
        }
    }

    onOpen() {
        this.reconneTimer && clearInterval(this.reconneTimer);
        this.reconneTimer = null;
        this.connectLing = false;
        this.reconneCount = 0;
        this.socketStatus = true;
        this.startPing();
        this.emit('open');
    }

    startPing() {
        this.timer = setInterval(() => {
            this.send({ type: 'ping' });
        }, 10000);
    }

    send(data) {
        if (!this.socketStatus || !this.networkStatus) {
            this.reconnect();
            return Promise.reject({ status: false, socketStatus: this.socketStatus, networkStatus: this.networkStatus });
        }

        return new Promise((resolve, reject) => {
            try {
                this.ws.send({
                    data: JSON.stringify(data),
                    success: () => {
                        resolve({ status: true });
                    },
                    fail: (err) => {
                        console.error(err);
                        reject({ status: false, socketStatus: this.socketStatus, networkStatus: this.networkStatus });
                    }
                });
            } catch (e) {
                console.error(e);
                reject({ status: false, socketStatus: this.socketStatus, networkStatus: this.networkStatus });
            }
        });
    }

    onMessage(event) {
        try {
            const { type, data = {} } = JSON.parse(event.data);
            this.emit(type, data);
        } catch (e) {
            console.error('WebSocket消息解析错误:', e);
        }
    }

    onClose() {
        this.connectLing = false;
        this.timer && clearInterval(this.timer);
        this.timer = null;
        this.socketStatus = false;
        this.emit('close');
        this.reconnect();
    }

    onError(e) {
        this.connectLing = false;
        this.timer && clearInterval(this.timer);
        this.timer = null;
        this.socketStatus = false;
        this.emit('error', e);
        this.reconnect();
    }

    close() {
        this.ws?.close();
        this.removeAllListeners();
        this.timer && clearInterval(this.timer);
        this.reconneTimer && clearInterval(this.reconneTimer);
    }
}

const socketInstances = {};

export const mobileScoket = (flag, token, form, tourist_uid) => {
    return new Promise((resolve) => {
        const key = 'mobile';
        if (flag) {
            socketInstances[key]?.close();
            socketInstances[key] = null;
        }

        if (!socketInstances[key]) {
            socketInstances[key] = new WebSocketClient({
                type: 'user',
                token,
                form,
                tourist_uid
            });
        }

        resolve(socketInstances[key]);
    });
};