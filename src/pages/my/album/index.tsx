/*修改文件：src/pages/my/album/index.tsx
目的：修复 ReferenceError，将 getFilteredDataByTab 函数定义提前。
*/
import { View, Text, Image, Button, Input, ScrollView, CoverImage } from "@tarojs/components";
import React, { useState, useEffect, useRef  } from "react";
import Taro, { useDidShow, useReady, Clipboard, showToast } from "@tarojs/taro";
import { Dialog, SearchBar, ActionSheet, Popup, Cell } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import { getMyAlbumList,getUserHomeTopData, deleteDynamic, updateDynamic, getTagList, getTreeCatalog } from "@/utils/api/common/common_user";
import "./index.less";
import { count } from "console";
import { content } from "html2canvas/dist/types/css/property-descriptors/content";
import { blob } from "stream/consumers";


// 定义 dynamic 类型
interface DynamicItem {
  id: string | number;
  content: string;
  price: number;
  time: string;
  pictures: string;  // API returns pictures as comma-separated string
  coverImage: string[];  // Transformed pictures into array
  skus?: string[];
  color?: string[];
  digitalWatermark?: string;
  isTop?: number;  // Add isTop property to the interface
}

interface GroupedItem {
  group: string;
  items: DynamicItem[];
}

interface DynamicData {
  items: GroupedItem[];
  totalCount?: number;
  hasMore?: boolean;
  allItems?: DynamicItem[]; // 添加 allItems 字段存储所有商品数据
}

interface UserHomeTopData {
  avatar?: string;
  nickname?: string;
  total?: number;
}

// 定义 tab 类型
type TabType = 'all' | 'new' | 'video' | 'image';

export default function selectMaterials123() {
  // 修正 scrollViewRef 的类型为 Taro.ScrollView
  const [search, setSearch] = useState("");

  const [expandedId, setExpandedId] = useState<string | number | null>(null);
  const [currentTab, setCurrentTab] = useState<TabType>('all');  // 添加当前 tab 状态
  const [dynamic, setDynamic] = useState<DynamicData>({
    items: [],
    hasMore: true,
    allItems: []
  });
  //const [value, setValue] = React.useState([2, '22328490198975000']); // 这个状态没有使用，可以考虑移除
  const [newFilteredData, setNewFilteredData]= useState<any[]>([]); // 这个状态只在 renderNewEditPage 中使用，且数据源是 dynamic.items 分组后，可以考虑直接在 render 函数中处理
  const [userHomeTop, setUserHomeTop] = useState<UserHomeTopData>({});
  const [pageNo, setPageNo] = useState(1);
  const [pageSize] = useState(10);
  const [sortType, setSortType] = useState(1);
  const [visible, setVisible] = React.useState(false);
  const [isLoading, setIsLoading] = useState(false); // 添加加载状态
  const [catalog, setCatalog]= useState<any[]>([]); 
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [selectedTagId, setSelectedTagId] = useState<any[]>([]);
  const [selectedTagName, setSelectedTagName] = useState<any[]>([]);
  // 获取所有商品对象
  const allItems = dynamic?.items || [];

  useEffect(() => {
    getTagListData();
  }, []); // 组件挂载时执行一次

  useDidShow(() => {
    setSelectedTagId([]);
    setSelectedTagName([]);
  })

  // 根据 tab 类型过滤数据 - 将定义提前
  const getFilteredDataByTab = (items: DynamicItem[]) => {
    const allItems = dynamic.allItems || items;
    switch (currentTab) {
      case 'new':
        // 上新 tab 显示所有商品，并按日期分组
        return allItems; // 注意：上新 tab 的分组逻辑在 renderNewEditPage 中处理
      case 'video':
        // 假设视频类型的商品标题包含"视频"或"小视频"
        return allItems; 
      case 'image':
        // 假设图集类型的商品有多张图片
        return allItems.filter(item => item.coverImage && item.coverImage.length > 1);
      default: // 'all' tab
        return allItems;
    }
  };


  // 获取用户信息
  const getUserHomeTop = async () => {
    try {
       const res: any = await getUserHomeTopData({userId: 299});
       if(res.code === 0 && res.data){
        setUserHomeTop(res.data)
        console.log('getUserInfo', res);
       }
       
    } catch (error) {
      console.error('getUserInfo failed:', error);
    }
  };


  const getDynamicListData = async (content=' ') => {
    // if (isLoading || !dynamic.hasMore) return;

    setIsLoading(true);
    Taro.showLoading({
      title: '加载中...',
      mask: true
    });
    console.log('search',  search);
    const data = {
      pageNo: pageNo,
      pageSize: pageSize,
      userId: 299,
      sort: sortType,
      content: content==''? content :search
    } as any;

    try {
      const res: any = await getMyAlbumList(data);
      Taro.hideLoading();
      
      if (res.code === 0 && res.data) {
        const itemsWithSkus = res.data.list?.map(item => {
          const pictures = item.pictures || '';
          // 分割图片字符串，并取第一张（过滤空值）
          const pictureArray = pictures.split(',').filter(Boolean);
          const coverImage = pictureArray[0] || ''; // 如果没有图片则设为空字符串;

          return {
            ...item,
            pictures,
            coverImage: coverImage,
            color: ['红色', '蓝色', '绿色', '黄色', '紫色', '粉色'],
          };
        }) || [];

        // 更新状态，累加数据
        setDynamic(prevDynamic => {
          // 合并所有商品数据
          const allItems = pageNo === 1 ? itemsWithSkus : [...(prevDynamic.allItems || []), ...itemsWithSkus];
          
          // 处理分组数据
          const grouped = {};
          allItems.forEach(item => {
            const time = item.time;
            if (!grouped[time]) {
              grouped[time] = [];
            }
            grouped[time].push(item);
          });

          const newFilteredData_s = Object.keys(grouped).map(time => ({
            group: time,
            items: grouped[time].map(item => ({
              ...item,
              digitalWatermark: item.digitalWatermark,
            }))
          }));

          const hasMore = res.data.list?.length === pageSize;
          
          return {
            totalCount: res.data.total || 0,
            items: newFilteredData_s,
            hasMore,
            allItems // 保存所有商品数据
          };
        });

      } else {
        Taro.showToast({
          title: res.msg || '加载失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      Taro.hideLoading();
      Taro.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });
      console.error('获取动态列表失败:', error);
    } finally {
      setIsLoading(false);
    }
  };



  useEffect(() => {
    if (pageNo === 1) {
      // 重置数据
      setDynamic({
        items: [],
        hasMore: true,
        allItems: []
      });
    }
    getUserHomeTop();
    getDynamicListData();

  }, [pageNo, sortType]); // 添加 sortType 作为依赖



  // 处理 tab 切换
  const handleTabChange = (tab: TabType) => {
    setCurrentTab(tab);
    // 切换 tab 时不清空选中状态，只改变展示的数据。
    // 如果需要清空，可以在这里取消注释setSelectedMap(new Map());
  };


  const handleSelectcatalog = (index) => {
    setSelectedIndex(index);
    // 这里可以添加其他选中后的逻辑
  };

  const del = (id) => {
      Dialog.confirm({
      platform: 'ios',
      className: 'dialog-input-demo',
      title: '温馨提示',
      contentAlign: 'left',
      children: (<>
          <div className="dialog-input-demo-hint">删除动态后不可恢复，确认删除？</div>
      </>),

      onOk: async () => {
        Taro.showLoading({
          title: '删除中...',
          mask: true
        });
        
        try {
          const res: any = await deleteDynamic({id: id});
          if(res.code === 0 ){
            // 更新 dynamic.items，过滤掉被删除的项
            setDynamic(prevDynamic => {
              // 对每个分组进行处理
              const updatedItems = prevDynamic.items
                .map(group => ({
                  ...group,
                  // 过滤掉当前分组中被删除的项
                  items: group.items.filter(item => item.id !== id)
                }))
                // 移除空分组
                .filter(group => group.items.length > 0);

              return {
                ...prevDynamic,
                items: updatedItems
              };
            });

            setTimeout(() => {
              Taro.hideLoading();
              Taro.showToast({
                title: '删除成功',
                icon: 'none',
                duration: 2000
              });
            }, 1000);
          }
          
       } catch (error) {
         Taro.hideLoading();
         Taro.showToast({
           title: '删除失败，请重试',
           icon: 'none',
           duration: 2000
         });
         console.error('删除失败:', error);
       }
      },
      onCancel: () => {
        // 用户点击取消时执行
        console.log('用户取消了操作');
      },
    });
  };


  const batch = () => {
     ActionSheet.open({
      items: [
          { content: '批量编辑'   , onClick: () => handleSelect('编辑')  },
          { content: '批量上下架' , onClick: () => upAndDownShelves()  },
          { content: '批量删除'   , onClick: () => batchDelete(), status: 'danger'  },
      ],
      cancelText: '取消',
    });
    // setDynamic(prevDynamic => ({
    //   ...prevDynamic,
    //   items: prevDynamic.items.sort((a, b) => new Date(b.group).getTime() - new Date(a.group).getTime())
    // }));
  }


  //联系ta
  const contact = () => {
    ActionSheet.open({
      className: 'actionSheet',
      items: [
        { content: (<View style={{padding: '32px 0px 22px 0px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>
          {/* 二维码 */}
          <Image
            src="https://img2.baidu.com/it/u=839922839,306118709&fm=253&fmt=auto&app=138&f=GIF?w=280&h=280"
            style={{ width: 150, height: 150, background: '#fff', borderRadius: 8 }}
          />
          {/* 按钮区域 */}
          <View
            style={{
              width: '116px',
              height: '36px',
              /* 自动布局 */
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '0px',
              zIndex: 1,
              borderRadius: '50px',
              background: '#F7F8FA',


            }}
          >
            {/* 下载按钮 */}
            <View
              style={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',

              }}
              onClick={() => { /* 下载逻辑 */ }}
            >
            <Image
              src="data:image/png;base64,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"
              style={{ width: 20, height: 20 }}
              onClick={() => { 
                  alert('下载微信')  
              }}
            />
            </View>
            {/* 分割线 */}
            <View style={{ width: 1, height: 24, background: '#eee' }} />
            {/* 加好友按钮 */}
            <View
              style={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                
              }}
              onClick={() => { /* 加好友逻辑 */ }}
            >
              <Image 
              src="data:image/png;base64,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"
              style={{ width: 20, height: 20 }}
              />
            </View>
          </View>
        </View>)    },

        { content: (<View style={{
                display: 'flex',
                flex: 2,
                justifyContent: 'space-between',
                alignItems: 'center',
                backgroundColor: '#fff',
                borderRadius: 8,
                padding: '0 16px',
        }}>
          <View >
            <View>
              <Text style={{ color: '#bcbcbc', fontSize: 12}}>微信号</Text>
            </View>
            <View>
              <Text style={{ color: '#222', fontSize: 13, fontWeight: 'bold' }}>17012345678</Text>
            </View>
          </View>
          <View  onClick={() => {
              Taro.setClipboardData({
                data: 'wx1234664', // 这里填你要复制的内容
                success: function () {
                  Taro.showToast({ title: '微信号已复制', icon: 'none' });
                }
              });
          }}>
            <Image
              src="data:image/png;base64,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"
              style={{ width: 20, height: 20 }}
            />
          </View>
        </View>)   },


        { content: (<View style={{
                display: 'flex',
                flex: 2,
                justifyContent: 'space-between',
                alignItems: 'center',
                backgroundColor: '#fff',
                borderRadius: 8,
                padding: '0 16px',
        }}>
          <View >
            <View>
              <Text style={{ color: '#bcbcbc', fontSize: 12 }}>手机号</Text>
            </View>
            <View>
              <Text style={{ color: '#222', fontSize: 13, fontWeight: 'bold' }}>17012345678</Text>
            </View>
          </View>
          <View >
            <Image   src="data:image/png;base64,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"    
              style={{ width: 20, height: 20 }}  
              onClick={() => {               
                Taro.makePhoneCall({
                  phoneNumber: '18012345678' // 这里填写你要拨打的手机号
                }); 
              }} 
            />
            <Image   src="data:image/png;base64,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"    
              style={{ width: 20, height: 20, marginLeft: 10 }}  
              onClick={() => {               
                Taro.setClipboardData({
                  data: '13995541244', // 这里填你要复制的内容
                  success: function () {
                    Taro.showToast({ title: '手机号已复制', icon: 'none' });
                  }
                });
              }}  
            />
          </View>
        </View>)   },
      ],
      cancelText: '取消',
    });
  };

  //获取目录列表
  const getTagListData = async (type=1) => {
    const data = {
      userId: 299,
    }
    const tags: any = await getTreeCatalog(data);
    if (tags.code == 0) {
      if (tags.data.length > 0) {
        setCatalog(tags.data);   
      } else {
        
      }
    }
  }

  //商品分类
  const goodsclass = () => {
    setVisible(true);
    //getTagListData();

  };

  const sort = () => {
    ActionSheet.open({
     items: [
         { content: '按发布时间排序' , onClick: () => handleSelect('发布')  },
         { content: '按更新时间排序' , onClick: () => handleSelect('更新')  },
     ],
     cancelText: '取消',
   });
 }

  const handleSelect = (type) => {
    Dialog.confirm({
      platform: 'ios',
      className: 'dialog-input-demo',
      title: '确认修改商品排序',
      contentAlign: 'left',
      children: (<>
          <div className="dialog-input-demo-hint" style={{textAlign: 'center'}}>确认后,客户在全部中看到的所有商品将按{type}时间排序。</div>
      </>),

      onOk: async () => {
        if(type == '发布'){
          setSortType(1)
          getDynamicListData()    
        }else{
          setSortType(2)
          getDynamicListData()    
        }
      },
      onCancel: () => {
        // 用户点击取消时执行
      },
    });
  }

  // 批量删除
  const batchDelete = () => {
    Taro.navigateTo({
      url: `/pages/my/album/batchDelete/index`
    });
  }

  // 上下架
  const upAndDownShelves = () => {
    Taro.navigateTo({
      url: `/pages/my/album/upAndDownShelves/index`
    });  
  }
  //跳转到发布
  const edit = (item) => {
    console.log('edit', item);
    Taro.setStorageSync('releaseDynamicList', item);
    Taro.navigateTo({
      url: `/pages/releaseDynamic/index?type=2`
    });
  };

  //刷新
  const refresh = async (id) => {
    const res: any = await updateDynamic({id: id, isListed: 1});
    if(res.code === 0){
      Taro.showToast({
        title: '刷新成功',
        icon: 'none',
        duration: 2000
      });
    }
  }

    // 跳转到详情页
    const goToDetailPage = (item) => {
      Taro.navigateTo({
        url: `/pages/detail/index?dynamicId=${item.id}&dynamicUserId=${item.userId}`
      });
    };

  const download = (item) => {
  
    Taro.showLoading({
      title: '下载中...',
      mask: true
    });
    Taro.downloadFile({
      url: item.pictures,
      success: res => {
        Taro.saveFile({
          tempFilePath: res.tempFilePath,
          success: res => {
            Taro.hideLoading();
            Taro.showToast({
              title: '下载成功',
              icon: 'none',
              duration: 2000
            });
          },
          fail: res => {
            Taro.hideLoading();
            Taro.showToast({
              title: '下载失败',
              icon: 'none',
              duration: 2000
            });
          }
        });
      },
      fail: res => {
        Taro.hideLoading();
        Taro.showToast({
          title: '下载失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }


  const create = () => {
    Taro.navigateTo({
      url: '/pages/releaseDynamic/index'
    });
  }
  const canceltop = async (id, isTop) => {
    const newTopStatus = isTop == 1 ? 0 : 1;
    const res: any = await updateDynamic({id: id, isTop: newTopStatus});
    if(res.code === 0){
      // Update the local state to reflect the change immediately
      setDynamic(prevDynamic => ({
        ...prevDynamic,
        items: prevDynamic.items.map(group => ({
          ...group,
          items: group.items.map(item => 
            item.id === id ? { ...item, isTop: newTopStatus } : item
          )
        }))
      }));

      Taro.showToast({
        title: newTopStatus === 1 ? '置顶成功' : '取消置顶成功',
        icon: 'none',
        duration: 2000
      });
    }
  }

  const loadingData = () => {
    if (!isLoading && dynamic.hasMore) {
      setPageNo(prevPageNo => prevPageNo + 1);
    }
  };

  const addTagId=(tagId,tagName)=>{
    if(selectedTagId.length==3){
      Taro.showToast({
        title: '最多只能支出3个标签',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    setSelectedTagId([...selectedTagId,tagId])
    setSelectedTagName([...selectedTagName,tagName])

  }

  // 渲染商品列表 (除上新外的其他 Tab)
  const renderProductList = () => {
    // 使用 currentTabFilteredItems 替代 filteredData

    return (
      
      <View className="move-list-my" >
              {
                (0==2)?(
                  <Popup
                    visible={visible}
                    close={() => setVisible(false)}
                    contentStyle={{ borderRadius: '10px 10px 0 0' }}
                    //animation="slide-up"
                    //maskClosable
                  >
                <View style={{ 
                  display: 'flex',
                  flexDirection: 'column',
                  padding: '0',
                  alignSelf: 'stretch',
                  zIndex: 0,
                }}>
                  <View className="popup-demo-title">多选</View>
                  <View className="popup-demo-main-content">
                      <View className="popup-demo-content">         
                        <Image className="popup-demo-img" src="data:image/png;base64,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" />
                        <Text className="popup-demo-text">私密(仅自己可见)</Text>
                      </View>
                    {catalog && catalog.map((item, index) => (
                      <View className="popup-demo-content">         
                        <Image className="popup-demo-img" src="data:image/png;base64,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" />
                        <Text className="popup-demo-text" style={{ fontWeight: 'bold'}}>{item.name}</Text>
                      </View>
                    ))}
                      <View className="popup-demo-content" 
                      onClick={() => {            
                        setVisible(false)
                        Taro.navigateTo({ url: `/pages/albumManage/tagManage/tag/createTag` })
                      
                      }} >         
                          <Image className="popup-demo-img" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAVAAAAFQCAYAAADp6CbZAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAUWSURBVHic7doxT1NRAIbhWw7taDAVWRyM//83GQcXxCZukJILrgyQ1DfS2ybPs57hftPbnPSs7h/2zxMA/+xi6QEA50pAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIDocukB8NI8z9Pv3Z9Xzz5tr6YxxpEXwdsElJMyz0/T7d3u1bOPVx8ElJPiCg8QCShAJKAAkYACRAIKEAkoQCSgAJGAAkQCChAJKEAkoACRgAJEAgoQCShAJKAAkYACRAIKEAkoQCSgAJGAAkQCChAJKEAkoACRgAJEl0sP4DzM8zzN89O7f2f/+JjO/qcxLqYxxlG+xXlb3T/sn5cewem7/bWbbu92S884ipvr7XTzebv0DM6AKzxAJKAAkYACRAIKEAkoQORfeA5yzGdM33/8fPXs29cv02a9fvcNnjFxKO9AOcgYY/GobNbrabN5/4DCoVzhASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgOhy6QHw0hgX08319s0zOCWr+4f989IjAM6Rn3SASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgEhAASIBBYgEFCASUIBIQAEiAQWIBBQgElCASEABIgEFiAQUIBJQgOgvcYY8mjJJE+cAAAAASUVORK5CYII=" />
                          <Text className="popup-demo-text">新建标签</Text>
                      </View>

                    </View>
                </View>
              </Popup>
                ):( 
                  <Popup visible={visible} close={() => setVisible(false)} >
                    
                        <View className="popup-demo-title">多选</View>
                        <ScrollView 
                          className="content-scroll-view"
                          style={{height: '320px'}}
                          scrollY
                        >
                            <View className="popup-demo-catalog">
                              <View className="popup-demo-catalog-content">
                              <Text className={`popup-demo-text ${selectedIndex === -1 ? 'active' : ''}`}
                                onClick={() => {handleSelectcatalog(-1);}}
                                key='-1'
                                >全部</Text>
                                {catalog && catalog.map((item, index) => (
                                  <Text className={`popup-demo-text ${selectedIndex === item.id ? 'active' : ''}`}
                                  onClick={() => {handleSelectcatalog(item.id);}}
                                  key={item.id}
                                  >{item.name}</Text>
                                ))}  
                              </View>
                              <Cell.Group  bordered={false}>
                                                                  
                                {catalog && catalog.map((item, index) => (
                                    <React.Fragment key={index}>
                                      {/* 外层 item 渲染 */}
                                      <Cell 
                                        label={item.name} 
                                        style={{ fontWeight: 'bold'}}
                                        showArrow 
                                        onClick={() => setVisible(true)}                                    
                                      />
                                
                                      {/* 内层 list 渲染 */}

                                      {item.children.length >0 ? (item.children.map((val, subIndex) => (
                                        <View className="popup-demo-content" key={val.id} onClick={()=>addTagId(val.id,val.name)}>
                                          <Image className="popup-demo-img" src={val.coverImage	}/>
                                          <Text className="popup-demo-text">{val.name}</Text>
                                        </View>
                                      ))):(<View ></View>)}
                                    </React.Fragment>
                                  ))}
                              
                              </Cell.Group>                 
                            </View>
                        </ScrollView>  
                      <Button  className='move-footer-btn-popup' 
                      onClick={() => {
                        setVisible(false)
                        console.log(selectedTagId,'selectedTagId')
                        Taro.navigateTo({
                            url: '/pages/my/album/classDynamic/index?tagId='+selectedTagId.join(',')+'&tagName='+(selectedTagName.join(','))
                        });
                      }}>确定</Button>   
                  </Popup>
                )
              }
              
        {dynamic.items.map(group => (
          <View  key={group.group}  style={{marginTop: '10px'}}>
                <Text className="move-group-title">{group.group}</Text>
                {group.items.map(item => {
                    return (
                    // 只有当 itemId 不为 null 时才渲染该列表项
                        <View key={item.id}>
                            <View className="move-item-my" >
                                {item.coverImage  && (
                                    <Image className="move-img" src={item.coverImage} />
                                )}
                                <View className="move-info-my">
                                    <Text className="move-title-text-my" onClick={() => goToDetailPage(item)}>{item.content}</Text>
                                    <Text className="move-price">¥{item.price}</Text>
                                    <View className="move-actions">
                                      
                                        <Text className="move-action-btn" onClick={() => del(item.id)}>删除</Text>
                                        <Text className="move-action-btn" onClick={() => download(item)}>下载</Text>
                                        <Text className="move-action-btn" onClick={() => refresh(item.id)}>刷新</Text>
                                        <Text className="move-action-btn" onClick={() => edit(item)}>编辑</Text>
                                        <Text className="move-action-btn" onClick={() => canceltop(item.id,item.isTop)}>{item.isTop == 1 ? '取顶' : '置顶'}</Text>
                                        <Text className="move-action-share">分享</Text>
                                    </View>
                                </View>                                   
                            </View>
                        {/* 商品属性区域，保留原逻辑 */}
                        <View className="move-attrs-wrap-my">
                            {((item.skus && item.skus.length > 0) || (item.color && item.color.length > 0)) && (
                            <>
                                <Text
                                className="move-attrs-my"
                                onClick={() =>
                                    setExpandedId(item.id === undefined
                                    ? null
                                    : (expandedId === item.id ? null : item.id)
                                    )
                                }
                                >
                                商品属性
                                {expandedId === item.id ?
                                    <Text className="move-attrs-arrow">▼</Text> :
                                    <Text className="move-attrs-arrow">▶</Text>
                                }
                                </Text>
                                {expandedId === item.id && (
                                <View className="attr-content card-attr-content">
                                        {item.skus && item.skus.length ?
                                        (<View className="attr-row" key="规格">
                                          <Text className="attr-label">规格：</Text>
                                          <View className="attr-values">
                                            {item.skus.map((val, index) => (
                                            <Text
                                                className="attr-value" // 使用 .selected 样式
                                                key={index} // 使用 index 作为 key
                                            >
                                                {val}
                                            </Text>
                                            ))}
                                          </View>
                                        </View>):(<></>)
                                        }

                                        {item.color && item.color.length ?
                                        (<View className="attr-row" key="颜色">
                                            <Text className="attr-label">颜色：</Text>
                                            <View className="attr-values">
                                                {item.color.map((val, index) => (
                                                <Text
                                                    className="attr-value" // 使用 .selected 样式
                                                    key={index} // 使用 index 作为 key
                                                >
                                                    {val}
                                                </Text>
                                                ))}
                                            </View>
                                            </View>):(<></>)
                                        }
                                </View>
                                )}
                            </>
                            )}
                        </View>
                    </View>
                    );
                })}
         </View>      
         ))} 
      </View>
    );
  };

  return (
    <ScrollView  className="move-list-scroll"
        style={{height: '100%'}}
        scrollY
        onScrollToLower={loadingData}     
    >
    <View className="select-materials-page"> {/* 添加页面整体容器 */}
      {/* 顶部导航栏 */}
      <YkNavBar title="我的相册"        
      />
      {/* 页面主要内容区域，使用 Flexbox 使 ScrollView 填充剩余空间 */}
      <View className="move-content-page" >
        {/* 用户头部信息区域，固定在顶部 */}
        <View className="user-header">
          {/* 头像昵称简介 */}
          <View className="user-header-info">

            <View className="user-header-avatar-wrap">
              {/* 如果 dynamic.merchantAvatar 不为空才渲染 Image */}
              {userHomeTop.avatar && <Image className="user-header-avatar" src={userHomeTop.avatar} />}
              {/* 二维码图标 */}
              <View className="user-header-qrcode" />
            </View>
            <Text className="user-header-nick">{userHomeTop.nickname}</Text>
            <Text className="user-header-desc">
               {/* 根据图片，这里显示的是描述文字 */}
               莆田优质潮鞋货源批发,一线品牌代工,免费代理。大单量可优惠,全部包邮!大单量可优惠,全部包邮!大单量可优惠,全...
            </Text>
            {/* 统计数据区域 */}
            <View className="user-header-stats">
              <View className="user-header-stat">
                <Text className="user-header-stat-num">{userHomeTop.newNumbers}</Text>
                <Text className="user-header-stat-label">上新</Text>
              </View>
              <View className="user-header-stat">
                <Text className="user-header-stat-num">{userHomeTop.total}</Text>
                <Text className="user-header-stat-label">总数</Text>
              </View>
            </View>
          </View>
          {/* tab栏 */}
          <View className="user-header-tabs">
            <View
              className={`user-header-tab ${currentTab === 'all' ? 'active' : ''}`}
              onClick={() => handleTabChange('all')}
            >
              全部 () {/* 显示当前tab下的商品数量 */}
            </View>
            <View
              className={`user-header-tab ${currentTab === 'new' ? 'active' : ''}`}
              //onClick={() => handleTabChange('new')}
            >
              内容1 ({newFilteredData.reduce((sum, group) => sum + group.items.length, 0)}) {/* 显示上新商品数量 */}
            </View>
            <View
              className={`user-header-tab ${currentTab === 'video' ? 'active' : ''}`}
              onClick={() => handleTabChange('video')}
            >
              内容2 () {/* 显示当前tab下的商品数量 */}
            </View>
            <View
              className={`user-header-tab ${currentTab === 'image' ? 'active' : ''}`}
              onClick={() => handleTabChange('image')}
            >
              内容3 () {/* 显示当前tab下的商品数量 */}
            </View>
          </View>


          {/* 搜索栏 - 根据图片，只在非上新页面显示 */}
          {currentTab !== 'new' && (
            <View className="user-header-searchbar-wrap"> {/* 添加搜索栏容器 */}
              <SearchBar
                actionButton={<span className="user-header-filter" onClick={() => {setPageNo(1);getDynamicListData()}}>搜索</span>}
                placeholder="请输入要搜索的内容"
                onChange={(e, value) => setSearch(value)}  // Handle both event and value
                value={search}
                className="user-header-searchbar"
                clearable
                onClear={() => {setSearch('');setPageNo(1);getDynamicListData('')}}
              />
               
            </View>
          )}
            {/* 添加按更新时间排序 */}
             {currentTab !== 'new' && (
                <View className="user-header-sort">
                       <View className="move-img-add-img" onClick={create} ></View>                     
                      <View>
                        <Text className="user-header-sort-text" onClick={sort}>按更新时间排序</Text>
                        <Text className="user-header-sort-icon">▼</Text> {/* 使用 ▼ 图标 */}
                      </View>
                 
                </View>
             )}
        </View>


        {/* 商品列表滚动区域 */}
         {/* 根据图片判断，如果 totalCount 为 0，显示空状态 */}


            {/* {(dynamic.totalCount ?? 0) > 0 ? (
                currentTab === 'new' ? (
                  <View className="empty-state">
                    <Text className="empty-text">上新页面开发中...</Text>
                  </View>
                ) : renderProductList()
            ) : (
                // 数据为空时显示空状态
                <View className="empty-state">
                    <Image
                    className="empty-image"
                    src="https://img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"
                    />
                    <Text className="empty-text">暂无数据</Text>
                </View>
            )} */}
        

              {renderProductList()}
              {isLoading && (
                <View className="loading-more">
                  <Text className="loading-text">加载中...</Text>
                </View>
              )}
              {!dynamic.hasMore && dynamic.items.length > 0 && (
                <View className="no-more">
                  <Text className="no-more-text">没有更多了</Text>
                </View>
              )}


   


         {/* 底部tab栏 - 商品分类 联系Ta 批量操作 分享 */}
         <View className="bottom-tabs">
            <View className="bottom-tab-item">
                <Text className="bottom-tab-text" onClick={goodsclass}>商品分类</Text>
            </View>
             <View className="bottom-tab-item">
                <Text className="bottom-tab-text" onClick={contact}>联系Ta</Text>
            </View>
             <View className="bottom-tab-item" >
                <Text className="bottom-tab-text" onClick={batch}>批量操作</Text>
            </View>
             <View className="bottom-tab-item">
                <Text className="bottom-tab-text">分享</Text>
            </View>
         </View>
      </View>
    </View>
    </ScrollView>
  );
}

