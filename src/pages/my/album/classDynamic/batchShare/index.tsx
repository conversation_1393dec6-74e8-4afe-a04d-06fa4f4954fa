/*
修改文件：src/pages/my/album/index.tsx
目的：修复 ReferenceError，将 getFilteredDataByTab 函数定义提前。
*/
import { View, Text, Image,  Input, ScrollView, CoverImage } from "@tarojs/components";
import React, { useState, useEffect, useRef  } from "react";
import Taro, { useDidShow, useReady } from "@tarojs/taro";
import { Dialog, SearchBar, ActionSheet, Checkbox, Button, Popup, Cell, Tag,  DatePicker } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import { getMyAlbumList,getUserHomeTopData, deleteDynamic, updateDynamic } from "@/utils/api/common/common_user";
import "./index.less";
import { count } from "console";
import { content } from "html2canvas/dist/types/css/property-descriptors/content";


// 定义 dynamic 类型
interface DynamicItem {
  id: string | number;
  content: string;
  price: number;
  time: string;
  pictures: string;  // API returns pictures as comma-separated string
  coverImage: string[];  // Transformed pictures into array
  skus?: string[];
  color?: string[];
  digitalWatermark?: string;
  isTop?: number;  // Add isTop property to the interface
}

interface GroupedItem {
  group: string;
  items: DynamicItem[];
}

interface DynamicData {
  items: GroupedItem[];
  totalCount?: number;
  hasMore?: boolean;
  allItems?: DynamicItem[]; // 添加 allItems 字段存储所有商品数据
}

interface UserHomeTopData {
  avatar?: string;
  nickname?: string;
  total?: number;
}

// 定义 tab 类型
type TabType = 'all' | 'new' | 'video' | 'image';

export default function BatchDeletePage() {
  const [dynamic, setDynamic] = useState<DynamicData>({ items: [], hasMore: true, allItems: [] });
  const [selectedIds, setSelectedIds] = useState<(string|number)[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize] = useState(20);
  const [sortType, setSortType] = useState(1);
  const [visible, setVisible] = React.useState(false);
  const [picker2Visible, setPicker2Visible] = React.useState(false);
  const [picker2Value, setPicker2Value] =  React.useState(''); 
  const [isSelectPicker, setIsSelectPicker] =  React.useState(1); 
  const [isSelectedShare, setIsSelectedShare] =  React.useState(1); 
  const [picker3Visible, setPicker3Visible] = React.useState(false);
  const [picker3Value, setPicker3Value] =  React.useState('');
  const [dateRange, setDateRange] =  React.useState(''); 
  const [selectedTagIndexes, setSelectedTagIndexes] = useState<number[]>([]); // 支持多选
  const [selectedTagIndexesSearch, setSelectedTagIndexesSearch] = useState<number[]>([]); // 支持多选
  const [selectedShareIndexes, setSelectedShareIndexes] = React.useState(-1); // 支持多选
  const [selectedSourceIndexes, setSelectedSourceIndexes] = useState<number[]>([]); // 支持多选
  const [selectedSourceIndexesSearch, setSelectedSourceIndexesSearch] = useState<number[]>([]); // 支持多选
  const tagList = [
    '自己',
    '班级',
    '班级全群发送',
    '精品合辑',
    '家课精品合辑批次',
    '测数表单31382321',
  ];
  const share = [
    '未分享',
    '已分享',
  ];
  const sourceList = [
    '微信',
    'QQ',
    '朋友圈',
    'QQ空间',
  ];
  const [selectedTagIndexesNew, setSelectedTagIndexesNew] = useState<number[]>([]); // 支持多选
  const [selectedSourceIndexesNew, setSelectedSourceIndexesNew] = useState<number[]>([]); // 支持多选
  const [selectedShareIndexesNew, setSelectedShareIndexesNew] = useState(-1); // 支持多选

  const getDynamicListData = async (content=' ') => {
    // if (isLoading || !dynamic.hasMore) return;

    setIsLoading(true);
    Taro.showLoading({
      title: '加载中...',
      mask: true
    });

    const data = {
      pageNo: pageNo,
      pageSize: pageSize,
      userId: 299,
      sort: sortType,
    } as any;

    try {
      const res: any = await getMyAlbumList(data);
      Taro.hideLoading();
      
      if (res.code === 0 && res.data) {
        const itemsWithSkus = res.data.list?.map(item => {
          const pictures = item.pictures || '';
          // 分割图片字符串，并取第一张（过滤空值）
          const pictureArray = pictures.split(',').filter(Boolean);
          const coverImage = pictureArray[0] || ''; // 如果没有图片则设为空字符串;

          return {
            ...item,
            pictures,
            coverImage: coverImage,
            pictureArray: pictureArray,
          };
        }) || [];
  
        // 更新状态，累加数据
        setDynamic(prevDynamic => {
          // 合并所有商品数据
          const allItems = pageNo === 1 ? itemsWithSkus : [...(prevDynamic.allItems || []), ...itemsWithSkus];
          const hasMore = res.data.list?.length === pageSize;
          
          return {
            items: allItems,
            hasMore
          };
        });

      } else {
        Taro.showToast({
          title: res.msg || '加载失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      Taro.hideLoading();
      Taro.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });
      console.error('获取动态列表失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 获取数据逻辑同主页面
  useEffect(() => {
    getDynamicListData();
  }, []);

  // 全选/反选
  const handleSelectAll = () => {
    if (isAllSelected) {
      setSelectedIds([]);
      setIsAllSelected(false);
    } else {
      const all = dynamic.items.flatMap(group => group.items.map(item => item.id));
      setSelectedIds(all);
      setIsAllSelected(true);
    }
  };

  // 单选
  const handleSelect = (id) => {
    let newSelected;
    if (selectedIds.includes(id)) {
      newSelected = selectedIds.filter(_id => _id !== id);
    } else {
      newSelected = [...selectedIds, id];
    }
    
    setSelectedIds(newSelected);
    setIsAllSelected(newSelected.length === dynamic.items.flatMap(g=>g.items).length);
  };

  const reset=()=>{
    setSelectedTagIndexes([]);
    setPicker2Value('');
    setPicker3Value('');
  }

  const finish=()=>{
    setVisible(false);
    setSelectedTagIndexesNew(selectedTagIndexes);
    setSelectedShareIndexesNew(selectedShareIndexes);
    setSelectedSourceIndexesNew(selectedSourceIndexes);
    const date=picker2Value=='' ? (picker3Value=='' ? '' : '2015-01-01--'+picker3Value ) : (picker3Value=='' ? picker2Value+'--'+new Date().toISOString().slice(0, 10)  : picker2Value+'--'+picker3Value )
    
    //setPicker2ValueNew(picker2Value);
    setDateRange(date)
    getDynamicListData()
    console.log('date--------', date);
    console.log('selectedTagIndexes', selectedTagIndexes);
    console.log('selectedSourceIndexes', selectedSourceIndexes);
    console.log('picker2Value', picker2Value);
    console.log('picker3Value', picker3Value);
  }


  const filter=()=>{
    return (
      
        <Popup    
        contentStyle={{width: '250px'}}  //{{width: '250px', height: 'calc(100% - 50px)',marginTop: '50px'}}
        direction = 'right'
        visible={visible} 
        close={() => setVisible(false)} >      
          <YkNavBar title="筛选" />
          <View className="filter-container">
            <View className="filter-title" >时间区间</View>
            <View className="filter-title">
            <Cell.Group bordered={false}>
              <Cell className="date-picker-cell"  text={picker2Value} label="起始时间" showArrow onClick={() => {setPicker2Visible(true)}} />
              <Cell className="date-picker-cell"  text={picker3Value} label="结束时间" showArrow onClick={() => {setPicker3Visible(true)}} />
            </Cell.Group>
            </View>
            <View className="source-section">
              <View className="filter-title">
                分享 
              </View>
              <View className="tag-group">
                {share.map((tag, idx) => (
                  <Tag
                    key={tag}
                    filleted
                    type="solid"
                    color={selectedShareIndexes==idx ? '#165DFF' : '#4E5969'}
                    bgColor={selectedShareIndexes==idx  ? '#E8F3FF' : '#F2F3F5'}
                    className={selectedShareIndexes==idx  ? 'active' : ''}
                    onClick={() => {
                      selectedShareIndexes==idx ? setSelectedShareIndexes(-1) : setSelectedShareIndexes(idx)    
                    }}
                  >
                    {tag}
                  </Tag>
                ))}
              </View>
            </View>
            <View className="source-section">
              <View className="filter-title">
                标签 
              </View>
              <View className="tag-group">
                {tagList.map((tag, idx) => (
                  <Tag
                    key={tag}
                    filleted
                    type="solid"
                    color={selectedTagIndexes.includes(idx) ? '#165DFF' : '#4E5969'}
                    bgColor={selectedTagIndexes.includes(idx) ? '#E8F3FF' : '#F2F3F5'}
                    className={selectedTagIndexes.includes(idx) ? 'active' : ''}
                    onClick={() => {
                      setSelectedTagIndexes(prev =>
                        prev.includes(idx)
                          ? prev.filter(i => i !== idx)
                          : [...prev, idx]
                      );
                    }}
                  >
                    {tag}
                  </Tag>
                ))}
              </View>
            </View>

            <View className="source-section">
              <View className="filter-title">
                来源 
              </View>
              <View className="tag-group">
                {sourceList.map((tag, idx) => (
                  <Tag
                    key={tag}
                    filleted
                    type="solid"
                    color={selectedSourceIndexes.includes(idx) ? '#165DFF' : '#4E5969'}
                    bgColor={selectedSourceIndexes.includes(idx) ? '#E8F3FF' : '#F2F3F5'}
                    className={selectedSourceIndexes.includes(idx) ? 'active' : ''}
                    onClick={() => {
                      setSelectedSourceIndexes(prev =>
                        prev.includes(idx)
                          ? prev.filter(i => i !== idx)
                          : [...prev, idx]
                      );
                    }}
                  >
                    {tag}
                  </Tag>
                ))}
              </View>
            </View>    

            <View className="button-group">
              <Button  className='move-footer-btn reset-btn' onClick={reset}> 重置</Button>
              <Button  className='move-footer-btn' style={{marginTop: 0}} onClick={finish}> 确定 </Button>
            </View>
          </View>
        </Popup>
    )
  }

  const startDate = () => {
    return (
      <DatePicker
                title="起始日期"
                visible={picker2Visible}
                maskClosable
                disabled={false}
                currentTs={new Date(picker2Value).getTime()}
                mode="date"
                onHide={() => {
                    setPicker2Visible(false);
                }}
                onChange={(timestamp, obj) => {
                    console.info('---demo on change', timestamp);
                    //setPicker2Value(timestamp);
                }}
                onOk={(timestamp, obj) => {
                    console.info('----- time onok demo date', obj, timestamp);
                    setPicker2Value(new Date(timestamp).toISOString().split('T')[0]);
                }}
                formatter={(value, type) => {
                    if (type === 'year') {
                        return `${value}年`;
                    } else if (type === 'month') {
                        return `${value}月`;
                    } else if (type === 'date') {
                        return `${value}日`;
                    }
                }}
            />
    )
  }

  const endDate = () => {
    return (
      <DatePicker
                title="结束日期"
                visible={picker3Visible}
                maskClosable
                disabled={false}
                currentTs={new Date(picker3Value).getTime()}
                mode="date"
                onHide={() => {
                    setPicker3Visible(false);
                }}
                onChange={(timestamp, obj) => {
                    console.info('---demo on change', timestamp);
                    //setPicker3Value(timestamp);
                }}
                onOk={(timestamp, obj) => {
                    console.info('----- time onok demo date', obj, timestamp);
                    setPicker3Value(new Date(timestamp).toISOString().split('T')[0]);
                }}
                formatter={(value, type) => {
                    if (type === 'year') {
                        return `${value}年`;
                    } else if (type === 'month') {
                        return `${value}月`;
                    } else if (type === 'date') {
                        return `${value}日`;
                    }
                }}
            />
    )
  }
  // 列表渲染
  const renderList = () => (
    <View className="move-list-my">
      {dynamic.items.map(item => (
        <View key={item.id} className="move-item-my">
          <Checkbox
            className="move-checkbox"
            checked={selectedIds.includes(item.id)}
            onChange={() => handleSelect(item.id)}
          />
          <View >
            <View >
              <Text className="move-title-text-my">{item.content}</Text>
            </View>
            <View className="move-content-my">
              {item.pictureArray && item.pictureArray.slice(0, 6).map((pic, idx) => (
                  <Image className="move-img" src={pic} />
              ))}
            </View>
          </View>
        </View>
      ))}
    </View>
  );

  return (
    <View className="select-materials-page">
      <YkNavBar title="批量分享" />
      <View className="user-header-searchbar-wrap"> {/* 添加搜索栏容器 */}
                  <SearchBar
                    actionButton={<span className="user-header-filter" onClick={() => {setVisible(true)}}>搜索</span>}
                    placeholder="请输入要搜索的内容"
                    //onChange={(e, value) => setSearch(value)}  // Handle both event and value
                    //value={search}
                    className="user-header-searchbar"
                    clearable
                    //onClear={() => {setSearch('');setPageNo(1);getDynamicListData('')}}
                  />                               
                  
      </View>
      {filter()}
      {startDate()}
      {endDate()}

      <View className="tag-group-selected">
        {selectedShareIndexesNew >= 0 && (
                <View>  
                  <Tag
                    key={selectedShareIndexesNew}
                    filleted
                    type="solid"
                    color={isSelectedShare ?   '#165DFF' : '#4E5969' }
                    bgColor={isSelectedShare ? '#E8F3FF' : '#F2F3F5' }
                    className={isSelectedShare ? '' : 'active' }
                    onClick={() => {
                      isSelectedShare==0 ? setIsSelectedShare(1) :  setIsSelectedShare(0)
                    }}
                  >
                    {share[selectedShareIndexesNew]}  
                  </Tag>   
                </View>
          )}
          {selectedTagIndexesNew.length > 0 && (          
                <View>  
                      {selectedTagIndexesNew.map((tag, idx) => (
                      <Tag
                        key={tag}
                        filleted
                        type="solid"
                        color={selectedTagIndexesSearch.includes(idx) ? '#165DFF' : '#4E5969'}
                        bgColor={selectedTagIndexesSearch.includes(idx) ? '#E8F3FF' : '#F2F3F5'}
                        className={selectedTagIndexesSearch.includes(idx) ? 'active' : ''}
                        onClick={() => {
                          setSelectedTagIndexesSearch(prev =>
                            prev.includes(idx)
                              ? prev.filter(i => i !== idx)
                              : [...prev, idx]
                          );
                        }}
                      >
                        {tagList[tag]}
                      </Tag>
                    ))}
                </View>         
          )}
          {dateRange != '' && (
                <View>  
                  <Tag
                    key={dateRange}
                    filleted
                    type="solid"
                    color={isSelectPicker ?   '#165DFF' : '#4E5969' }
                    bgColor={isSelectPicker ? '#E8F3FF' : '#F2F3F5' }
                    className={isSelectPicker ? '' : 'active' }
                    onClick={() => {
                      isSelectPicker==0 ? setIsSelectPicker(1) :  setIsSelectPicker(0)
    
                    }}
                  >
                    {dateRange}  
                  </Tag>   
                </View>
          )}
          {selectedSourceIndexesNew.length > 0 && (          
                <View>  
                      {selectedSourceIndexesNew.map((tag, idx) => (
                        <Tag
                          key={tag}
                          filleted
                          type="solid"
                          color={selectedSourceIndexesSearch.includes(idx) ? '#165DFF' : '#4E5969'}
                          bgColor={selectedSourceIndexesSearch.includes(idx) ? '#E8F3FF' : '#F2F3F5'}
                          className={selectedSourceIndexesSearch.includes(idx) ? 'active' : ''}
                          onClick={() => {
                            setSelectedSourceIndexesSearch(prev =>
                              prev.includes(idx)
                                ? prev.filter(i => i !== idx)
                                : [...prev, idx]
                            );
                          }}
                        >
                          {sourceList[tag]}
                        </Tag>
                      ))}
                </View>         
          )}
      </View>

      <ScrollView className="move-list-scroll" scrollY>
        {renderList()}
      </ScrollView>
      <View className="move-footer">
        {/* <Checkbox className="move-footer-checkbox" checked={isAllSelected} onChange={handleSelectAll}>
          全选
        </Checkbox> */}
        <Text style={{ marginLeft: 8 }}>选中{selectedIds.length}条</Text>
        <Button
          className={`move-footer-btn${selectedIds.length === 0 ? ' disabled' : ''}`}
          disabled={selectedIds.length === 0}
          loading={isLoading}
        >
          批量分享
        </Button>
      </View>
    </View>
  );
}
