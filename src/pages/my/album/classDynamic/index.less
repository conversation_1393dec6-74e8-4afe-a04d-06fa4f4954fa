/*
修改文件：src/pages/my/album/index.less
目的：根据图片调整页面整体、header、tab栏、搜索栏、列表项、底部操作栏、底部tab栏的样式。
*/
@import '@arco-design/mobile-react/style/mixin.less';

// 页面整体容器，使用 Flexbox 布局
.select-materials-page {
  display: flex;
  flex-direction: column; // 垂直布局
  min-height: 100vh; // 改为最小高度
  position: relative; // 添加相对定位
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });


  

  .move-content-page {
    flex: 1; // 使内容区域占据剩余空间
    display: flex;
    flex-direction: column; // 内容区域内部也使用垂直布局
    position: relative; // 添加相对定位
  }

  .user-header {
    padding-top: 10px; // 顶部留白
    background-color: #fff; // 背景色
    .use-dark-mode-query({
        background-color: @dark-background-color;     //黑色背景
    });

    .user-header-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 8px;
      padding: 0 16px; // 左右内边距

      .user-header-avatar-wrap {
        position: relative;
        margin-bottom: 8px; // 头像下方间距

        .user-header-avatar {
          width: 64px;
          height: 64px;
          border-radius: 50%;
          object-fit: cover;
          background: #f7f8fa;
        }
        .user-header-qrcode {
          position: absolute;
          right: 0; // 根据图片调整二维码位置
          bottom: 0;
          width: 20px;
          height: 20px;
          // background: url('二维码icon地址') no-repeat center/cover; // 根据实际二维码图标设置
           background-color: #ccc; // 占位符
        }
      }
      .user-header-nick {
        font-size: 15px;
        font-weight: bold; // 根据图片调整字体粗细
        color: #222;
        margin-top: 0px; // 调整与头像的间距
        max-width: 200px;
        text-align: center;
        .use-dark-mode-query({
            color: var(--dark-font-color);    //白色字体
        });
      }
      .user-header-desc {
        font-size: 13px; // 根据图片调整字体大小
        color: #8c8c8c;
        margin-top: 4px;
        max-width: 320px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .user-header-stats {
        display: flex;
        justify-content: center;
        margin-top: 12px;
        width: 100%; // 使统计区域宽度占满
        .user-header-stat {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 32px; // 调整统计项间距
          .user-header-stat-num {
            font-size: 18px; // 根据图片调整字体大小
            color: #222;
            font-weight: bold; // 根据图片调整字体粗细
            .use-dark-mode-query({
                color: var(--dark-font-color);    //白色字体
            });
          }
          .user-header-stat-label {
            font-size: 12px; // 根据图片调整字体大小
            color: #8c8c8c;
            margin-top: 2px;
          }
        }
      }
    }
    .user-header-tabs {
      display: flex;
      justify-content: space-around; // 调整 Tab 项分布方式
      align-items: center;
      margin-top: 20px; // 调整与统计区域的间距
      padding: 0 16px; // 左右内边距
      border-bottom: 1px solid #f0f0f0; // Tab 下方细线
      .use-dark-mode-query({
         border-bottom: 1px solid #333;
      });

      .user-header-tab {
        flex: 1; // 使 Tab 项平均分配宽度
        text-align: center; // 文字居中
        font-size: 15px; // 根据图片调整字体大小
        color: #222;
        padding: 12px 0; // 调整 Tab 项内边距
        position: relative;
        cursor: pointer;
        .use-dark-mode-query({
            color: var(--dark-font-color);    //白色字体
        });
        &.active {
          color: #165DFF;
          font-weight: bold; // 选中 Tab 字体加粗
          &::after {
            content: '';
            display: block;
            height: 2px;
            background: #165DFF;
            border-radius: 1px; // 根据图片调整圆角
            position: absolute;
            left: 20%; // 根据图片调整下划线宽度和位置
            right: 20%;
            bottom: 0px; // 根据图片调整下划线位置
          }
        }
      }
    }
    .user-header-searchbar-wrap { // 搜索栏外层容器
        display: flex;
        align-items: center;
        background-color: #fff; // 背景色
         .use-dark-mode-query({
            background-color: @dark-background-color;     //黑色背景
        });
        .user-header-searchbar { // SearchBar 组件样式
            flex: 1; // 搜索栏占据剩余空间
        }
        .user-header-filter { // 筛选按钮样式
            color: #165DFF;
            font-size: 15px;
            margin: 0px; // 调整与搜索栏间距
            cursor: pointer;
            margin-left: 10px; // 调整与搜索栏间距
        }
    }
    .user-header-sort { // 排序区域样式
        margin-bottom: 10px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content:space-between ; // 靠右对齐
        padding: 4px 16px 8px 16px; // 内边距
        background-color: #fff; // 背景色
        font-size: 13px; // 字体大小
        color: #8c8c8c; // 字体颜色
         .use-dark-mode-query({
            background-color: @dark-background-color;     //黑色背景
            color: #ccc;
        });
        .user-header-sort-icon {
            margin-left: 4px; // 图标与文字间距
        }
    }
  }

  .move-list-my {
    flex: 1; // 使列表区域占据剩余空间
    background: #fff;
    padding: 0 16px; // 左右内边距
    height: 420px;
    .use-dark-mode-query({
        background-color: @dark-background-color;    //黑色背景
    });
  }


  .move-group-title {
    font-size: 16px;
    color: #222;
    font-weight: bold;
    margin: 10px 0 8px 0px; // 调整分组标题间距
    .use-dark-mode-query({
        color: var(--dark-font-color);    //白色字体
    });
  }
  .move-item-my {
    display: flex;
    align-items: flex-start;
    margin-top: 20px; // 列表项之间顶部间距
    // border-bottom: 1px solid #f0f0f0; // 列表项之间不需要底部边线
    background: #fff;
    .use-dark-mode-query({
        background-color: @dark-background-color;   //黑色背景
    });
  }
  .move-checkbox {
    margin-right: 10px; // 调整复选框与图片间距
    margin-top: 8px; // 调整复选框顶部对齐
  }
  .move-img {
    width: 85px; // 根据图片调整图片尺寸
    height: 85px;
    border-radius: 6px;
    object-fit: cover;
    background: #f7f8fa;
    margin-right: 12px; // 调整图片与信息区域间距
  }

  .move-img-add-img {
    width: 80px;
    height: 80px;
    border-radius: 6px;
    background: #f7f8fa;
    cursor: pointer;

 
  }

  .move-info-my {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }
  .move-title-text-my {
    // width: 200px; // 移除固定宽度
    padding-bottom: 3px;
    height: 42px;
    font-size: 14px; // 根据图片调整字体大小
    color: #222;
    display: -webkit-box; // 多行文本溢出省略
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; // 限制最多显示 2 行
    overflow: hidden;
    text-overflow: ellipsis;
    .use-dark-mode-query({
        color: var(--dark-font-color);    //白色字体
    });

  }
  .move-price {
    padding-bottom: 3px;
    height: 21px;
    color: #F53F3F;
    font-size: 14px; // 根据图片调整字体大小
  }
  .move-attrs-wrap-my{
    
    padding: 0;
    margin-top: -20px;
    .use-dark-mode-query({
       border-bottom: 1px solid #333;
    });
  }

   .move-attrs-my {
    font-size: 13px;
    color: #8c8c8c;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    .move-attrs-arrow {
      font-size: 12px;
      margin-left: 2px;
      color: #c0c0c0;
    }
  }

  .card-attr-content {
    background: #f7f8fa;
    border-radius: 8px; // 根据图片调整圆角
    padding: 12px; // 调整内边距
    margin-top: 8px; // 调整与属性文字间距
    margin-right: 0px; // 移除右侧外边距
    box-shadow: none; // 移除阴影
    .use-dark-mode-query({
      background: @dark-container-background-color;
      box-shadow: none;
    });
  }
  .card-attr-content .attr-row {
      align-items: center; // 使属性 label 和 values 垂直居中
  }
  .card-attr-content .attr-row + .attr-row {
    border-top: 1px dashed #eee; // 使用虚线作为分隔线
    padding-top: 8px; // 调整上方内边距
    margin-top: 8px; // 调整上方外边距
    .use-dark-mode-query({
      border-top: 1px dashed #333;
    });
  }
  .card-attr-content .attr-label {
    font-size: 14px; // 根据图片调整字体大小
    color: #222;
    font-weight: bold; // 字体加粗
    min-width: 40px; // 调整最小宽度
    margin-top: 0px; // 移除顶部外边距
    .use-dark-mode-query({ color: var(--dark-font-color); });
  }
  .card-attr-content .attr-values {
    display: flex;
    flex-wrap: wrap;
    gap: 8px 16px; // 调整属性值之间的间距
  }
  .card-attr-content .attr-value {
    font-size: 13px; // 根据图片调整字体大小
    color: #444;
    background: none;
    border: none;
    padding: 0; // 移除内边距
    margin-bottom: 0px; // 移除底部外边距
    position: relative;
    cursor: pointer;
    transition: none;
    .use-dark-mode-query({ color: var(--dark-font-color); });

    &.selected {
      color: #165DFF; // 选中状态字体颜色
      font-weight: bold; // 选中状态字体加粗
    }

    // 属性值之间的分隔线，根据图片样式调整
    &:not(:last-child)::after {
      content: '|'; // 使用竖线作为分隔符
      position: static; // 移除绝对定位
      transform: none; // 移除transform
      margin-left: 16px; // 调整与属性值间距
      height: auto; // 移除固定高度
      background: none; // 移除背景
      color: #e5e6eb; // 分隔符颜色
       .use-dark-mode-query({ color: #333; });
    }
     &:last-child::after { // 移除最后一个属性值后面的分隔符
         display: none;
     }

  }

  .move-actions { // 删除 下载 刷新 编辑 取顶 分享 按钮区域
    //   display: flex;
    //   justify-content: flex-end; // 靠右对齐
      padding: 0px 0 12px 0; // 调整内边距
      font-size: 13px; // 字体大小
      color: #8c8c8c; // 字体颜色
      .move-action-btn {
          margin-left: 12px; // 按钮之间间距
          cursor: pointer;
          &:first-child {
              margin-left: 0; // 第一个按钮左侧无间距
          }
      }
      .move-action-share { // 分享按钮特殊处理
         margin-left: 12px; // 与前一个按钮间距
         border: 1px solid #165DFF; // 蓝色边框
         color: #165DFF; // 蓝色字体
         border-radius: 4px; // 圆角
         padding: 2px 8px; // 内边距
      }
  }


  .content-scroll-view{
     flex: 1; // 填充剩余空间
     overflow-y: auto; // 垂直方向滚动
  }

  .move-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px; // 调整内边距
    background: #fff;
    height: 50px; // 调整高度
    .use-dark-mode-query({
        background-color: @dark-background-color;     //黑色背景
    });
    border-top: 1px solid #f0f0f0; // 顶部细线
     .use-dark-mode-query({
       border-top: 1px solid #333;
    });
    // margin-top: 20px; // 移除顶部外边距
  }
  .move-footer-checkbox {
    font-size: 15px;
    color: #222;
    width: 80px; // 调整宽度
     .use-dark-mode-query({
        color: var(--dark-font-color);
    });
  }




  // 底部tab栏样式
  .bottom-tabs {
    position: fixed; // 固定定位
    bottom: 0; // 固定在底部
    left: 0; // 左对齐
    right: 0; // 右对齐
    z-index: 100; // 确保在其他内容之上
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 50px; // 高度
    background-color: #fff; // 背景色
    border-top: 1px solid #f0f0f0; // 顶部细线
    .use-dark-mode-query({
      background-color: @dark-background-color;
      border-top: 1px solid #333;
    });
    .bottom-tab-item {
      flex: 1;
      text-align: center;
      cursor: pointer;
      .bottom-tab-text {
        font-size: 13px; // 字体大小
        color: #222; // 字体颜色
        .use-dark-mode-query({
          color: var(--dark-font-color);
        });
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 0;
    background: #fff;
    .use-dark-mode-query({
      background: @dark-background-color;
    });
  }

  .empty-image {
    width: 83px;
    height: 82px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 14px;
    color: #86909C;
  }


/* 清除采集记录弹窗样式 - 保持原样，因为图片中没有这个弹窗 */
.confirm-dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7); /* Semi-transparent dark background */
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-dialog {
  background: #fff; /* White background for the dialog */
  border-radius: 12px; /* Rounded corners */
  width: 75%; /* Adjust width as needed */
  max-width: 300px; /* Max width to keep it centered and readable */
  padding: 24px 0px 0px 0px; /* Padding inside the dialog */
  display: flex;
  flex-direction: column;
  align-items: center;
  .use-dark-mode-query({
    background: @dark-container-background-color; /* Dark mode background */
  });
}

.confirm-dialog-title {
  font-size: 18px;
  font-weight: bold;
  color: #222; /* Dark text for title */
  margin-bottom: 8px;
  .use-dark-mode-query({
    color: var(--dark-font-color); /* White text for title in dark mode */
  });
}

.confirm-dialog-content {
  font-size: 14px;
  color: #666; /* Slightly lighter text for content */
  text-align: center;
  padding: 0 24px 0 24px;
  margin-bottom: 24px;
  line-height: 1.6;
  .use-dark-mode-query({
    color: #ccc; /* Lighter gray text for content in dark mode */
  });
}

.confirm-dialog-buttons {
   display: flex;
   width: 100%;
  // background:#fff;
   border-top: 1px solid #eee; /* Separator line above buttons */
   .use-dark-mode-query({
     border-top: 1px solid #333; /* Dark mode separator */
   });
}

.confirm-dialog-button {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 16px;
  cursor: pointer;

  &.cancel {
    color: #165DFF; /* Blue color for cancel button */
    border-right: 1px solid #eee; /* Separator between buttons */
    .use-dark-mode-query({
      border-right: 1px solid #333; /* Dark mode separator */
    });
  }

  &.clear {
    color: #165DFF; /* Blue color for clear button */
  }
}

.yk-navbar-back{
  cursor: pointer;
}

.dialog-input-demo-input {
  margin-top: 0.16rem;
  padding: 0.16rem 0.24rem;
  font-size: 0.28rem;
  line-height: 0.4rem;
  background-color: #f7f8fa;
  width: 100%;
  caret-color: #165dff;
}
.dialog-input-demo-hint {
  color: #86909c;
  margin-top: 0.16rem;
  font-size: 0.26rem;
  line-height: 0.36rem;
}
.dialog-input-demo .arco-dialog-footer .dialog-footer-button.cancel {
  color: #1d2129;
}

.move-list-scroll {
  flex: 1;
  //height: calc(100vh - 50px); // 减去底部标签栏的高度
  height:200px;
  background: #fff;
  overflow-y: auto; // 确保可以垂直滚动
  -webkit-overflow-scrolling: touch; // 增加 iOS 滚动支持
  .use-dark-mode-query({
    background: @dark-background-color;
  });
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  background: #fff;
  .use-dark-mode-query({
    background: @dark-background-color;
  });
  
  .loading-text {
    font-size: 14px;
    color: #86909C;
    display: flex;
    align-items: center;
    
    &::before {
      content: '';
      width: 16px;
      height: 16px;
      margin-right: 8px;
      border: 2px solid #E5E6EB;
      border-top-color: #165DFF;
      border-radius: 50%;
      animation: loading-spin 0.8s linear infinite;
    }
  }
}

@keyframes loading-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  background: #fff;
  .use-dark-mode-query({
    background: @dark-background-color;
  });
  
  .no-more-text {
    font-size: 14px;
    color: #86909C;
    position: relative;
    padding: 0 16px;
    
    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 60px;
      height: 1px;
      background: #E5E6EB;
      .use-dark-mode-query({
        background: #333;
      });
    }
    
    &::before {
      right: 100%;
      margin-right: 16px;
    }
    
    &::after {
      left: 100%;
      margin-left: 16px;
    }
  }
}

}


.actionSheet .arco-action-sheet-item  {
  
  &:nth-child(1) {
    height: 250px;
    border-bottom: none; // 移除第二个元素的底部边框
  }

  &:nth-child(2) {
    height: 50px;
    border-bottom: none; // 移除第二个元素的底部边框
    background-color: #fff; // 第二个元素的背景色
  }
  &:nth-child(3) {
    height: 80px;
    border-bottom: none; // 移除第二个元素的底部边框
    background-color: #fff; // 第二个元素的背景色
  }

  &:hover {
    background-color: #f7f8fa;
  }
}

.popup-demo-title{

  height: 44px;
  /* 自动布局 */
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 16px 8px 16px;
  gap: 4px;
  align-self: stretch;
  z-index: 0;
}

.popup-demo-main-content{
  /* 自动布局子元素 */

    /* 自动布局 */

}

.popup-demo-content{
  /* 自动布局子元素 */
  width: 93.75px;
  height: 114.75px;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0px 10px;
  gap: 7px;
  flex-grow: 1;
  z-index: 0;
  float: left;
  margin-top: 5px;
  margin-bottom: 5px;


}
.popup-demo-img{
/* 自动布局子元素 */
  width: 73.75px;
  height: 73.75px;
  /* 自动布局 */
  display: flex;
  justify-content: flex-end;
  padding: 0px;
  align-self: stretch;
  z-index: 0;
  border-radius: 8px;
}

.popup-demo-text{
  /* 自动布局子元素 */
  width: 71px;
  height: 40px;
  z-index: 1;
  font-family: PingFang SC;
  font-size: 12px;
  text-align: center;
  letter-spacing: 0.04em;
  color: #333333;
  display: -webkit-box;
  -webkit-line-clamp: 2;    /* 限制显示行数 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  &.active {
    color: #165DFF;
    font-weight: bold; // 选中 Tab 字体加粗
    &::after {
      content: '';
      display: block;
      height: 2px;
      background: #165DFF;
      border-radius: 1px; // 根据图片调整圆角
      position: absolute;
      left: 20%; // 根据图片调整下划线宽度和位置
      right: 20%;
      bottom: 0px; // 根据图片调整下划线位置
    }
  }


}

.popup-demo-catalog{

  display: flex;
  justify-content:flex-start;
  align-items: flex-start;
  
}

.popup-demo-catalog-content
{
  margin-top: 20px;
  margin-right: 20px;
}




.move-footer-btn-popup{
  height: 35px; // 调整高度
  line-height: 35px; // 调整行高
  text-align: center;
  background: #165DFF;
  color: #fff; // 字体颜色白色
  border-radius: 3px;
  font-size: 13px; // 字体大小

  border: none;
  padding: 0;

}

.move-footer-btn{
  height: 30px; // 调整高度
  line-height: 30px; // 调整行高
  text-align: center;
  background: #165DFF;
  color: #fff; // 字体颜色白色
  border-radius: 3px;
  font-size: 13px; // 字体大小

  border: none;
  padding: 0;

}

.filter-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  margin-top: 30px;

  .filter-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
    .use-var(color, font-color);
    .use-dark-mode-query({
      color: @dark-font-color;
    });
  }

  .source-section {
    margin-top: 24px;

    .source-title {
      margin-bottom: 16px;
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: @dark-font-color;
      });
    }

    .source-subtitle {
      font-size: 12px;
      .use-var(color, sub-info-font-color);
      .use-dark-mode-query({
        color: @dark-sub-info-font-color;
      });
    }
  }

  .tag-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }


  .button-group {
    margin-top: auto;
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
    padding: 16px 0;
    align-items:flex-start;
    .reset-btn {
      background-color: #8c8c8c;
    }
  }
}

.date-picker-cell{
  margin: 0;
  padding: 0;
  font-size: 18px;
  font-weight: bold;
}


.tag-group-selected{
  padding: 0 0.8rem;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  background-color: #fff;
  color: #fff;
}