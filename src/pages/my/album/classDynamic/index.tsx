/*修改文件：src/pages/my/album/index.tsx
目的：修复 ReferenceError，将 getFilteredDataByTab 函数定义提前。
*/
import { View, Text, Image, Button, Input, ScrollView, CoverImage } from "@tarojs/components";
import React, { useState, useEffect, useRef  } from "react";
import Taro, { useDidShow, useReady, Clipboard, showToast } from "@tarojs/taro";
import { Dialog, SearchBar, ActionSheet, Popup, Cell, Tag, DatePicker } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import { getMyAlbumList, getShopHomeDynamicList, deleteDynamic, updateDynamic, getTagByDynamicList } from "@/utils/api/common/common_user";
import "./index.less";
import { count } from "console";
import { content } from "html2canvas/dist/types/css/property-descriptors/content";
import { start } from "repl";


// 定义 dynamic 类型
interface DynamicItem {
  id: string | number;
  content: string;
  price: number;
  time: string;
  pictures: string;  // API returns pictures as comma-separated string
  coverImage: string[];  // Transformed pictures into array
  skus?: string[];
  color?: string[];
  digitalWatermark?: string;
  isTop?: number;  // Add isTop property to the interface
}

interface GroupedItem {
  group: string;
  items: DynamicItem[];
}

interface DynamicData {
  items: GroupedItem[];
  totalCount?: number;
  hasMore?: boolean;
  allItems?: DynamicItem[]; // 添加 allItems 字段存储所有商品数据
}

interface UserHomeTopData {
  avatar?: string;
  nickname?: string;
  total?: number;
}

// 定义 tab 类型
type TabType = 'all' | 'new' | 'video' | 'image';

export default function selectMaterials123() {
  // 修正 scrollViewRef 的类型为 Taro.ScrollView
  const [search, setSearch] = useState("");

  const [expandedId, setExpandedId] = useState<string | number | null>(null);
  const [currentTab, setCurrentTab] = useState<TabType>('all');  // 添加当前 tab 状态
  const [dynamic, setDynamic] = useState<DynamicData>({
    items: [],
    hasMore: true,
    allItems: []
  });
  //const [value, setValue] = React.useState([2, '22328490198975000']); // 这个状态没有使用，可以考虑移除
  const [newFilteredData, setNewFilteredData]= useState<any[]>([]); // 这个状态只在 renderNewEditPage 中使用，且数据源是 dynamic.items 分组后，可以考虑直接在 render 函数中处理
  const [userHomeTop, setUserHomeTop] = useState<UserHomeTopData>({});
  const [pageNo, setPageNo] = useState(1);
  const [pageSize] = useState(10);
  const [sortType, setSortType] = useState(1);
  const [visible, setVisible] = React.useState(false);
  const [isLoading, setIsLoading] = useState(false); // 添加加载状态
  const [catalog, setCatalog]= useState<any[]>([]); 
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [picker2Visible, setPicker2Visible] = React.useState(false);
  const [picker2Value, setPicker2Value] =  React.useState(''); 
  const [isSelectPicker, setIsSelectPicker] =  React.useState(1); 
  const [picker3Visible, setPicker3Visible] = React.useState(false);
  const [picker3Value, setPicker3Value] =  React.useState('');
  const [dateRange, setDateRange] =  React.useState(''); 
  const [selectedTagIndexes, setSelectedTagIndexes] = useState<number[]>([]); // 支持多选
  const [selectedTagIndexesSearch, setSelectedTagIndexesSearch] = useState<number[]>([]); // 支持多选
  const tagList = [
    '自己',
    '班级',
    '班级全群发送',
    '精品合辑',
    '家课精品合辑批次',
    '测数表单31382321',
  ];
  const [selectedTagIndexesNew, setSelectedTagIndexesNew] = useState<number[]>([]); // 支持多选
  // 获取所有商品对象
  // 获取所有商品对象



  // 根据 tab 类型过滤数据 - 将定义提前
  // const getFilteredDataByTab = (items: DynamicItem[]) => {
  //   const allItems = dynamic.allItems || items;
  //   switch (currentTab) {
  //     case 'new':
  //       // 上新 tab 显示所有商品，并按日期分组
  //       return allItems; // 注意：上新 tab 的分组逻辑在 renderNewEditPage 中处理
  //     case 'video':
  //       // 假设视频类型的商品标题包含"视频"或"小视频"
  //       return allItems; 
  //     case 'image':
  //       // 假设图集类型的商品有多张图片
  //       return allItems.filter(item => item.coverImage && item.coverImage.length > 1);
  //     default: // 'all' tab
  //       return allItems;
  //   }
  // };


  const getDynamicListData = async (content=' ') => {
    // if (isLoading || !dynamic.hasMore) return;

    setIsLoading(true);
    Taro.showLoading({
      title: '加载中...',
      mask: true
    });
    const data = {
      pageNo: pageNo,
      pageSize: pageSize,
      userId: 299,
      startDate:isSelectPicker==1 ? picker2Value : '',
      endDate:isSelectPicker==1 ? picker3Value : '',
      labelAndCatalogueIds:Taro.getCurrentInstance().router?.params.tagId,

    } as any;

    try {
      const res: any = await getTagByDynamicList(data);
      console.log('res', res);
      Taro.hideLoading();
      
      if (res.code === 0 && res.data) {
        const itemsWithSkus = res.data.list?.map(item => {
          const pictures = item.pictures || '';
          // 分割图片字符串，并取第一张（过滤空值）
          const pictureArray = pictures.split(',').filter(Boolean);
          const coverImage = pictureArray[0] || ''; // 如果没有图片则设为空字符串;

          return {
            ...item,
            pictures,
            coverImage: coverImage,
            color: ['红色', '蓝色', '绿色', '黄色', '紫色', '粉色'],
          };
        }) || [];

        // 更新状态，累加数据
        setDynamic(prevDynamic => {
          // 合并所有商品数据
          const allItems = pageNo === 1 ? itemsWithSkus : [...(prevDynamic.allItems || []), ...itemsWithSkus];
          
          // 处理分组数据
          const grouped = {};
          allItems.forEach(item => {
            const time = item.time;
            if (!grouped[time]) {
              grouped[time] = [];
            }
            grouped[time].push(item);
          });

          const newFilteredData_s = Object.keys(grouped).map(time => ({
            group: time,
            items: grouped[time].map(item => ({
              ...item,
              digitalWatermark: item.digitalWatermark,
            }))
          }));

          const hasMore = res.data.list?.length === pageSize;
          
          return {
            totalCount: res.data.total || 0,
            items: newFilteredData_s,
            hasMore,
            allItems // 保存所有商品数据
          };
        });

      } else {
        Taro.showToast({
          title: res.msg || '加载失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      Taro.hideLoading();
      Taro.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });
      console.error('获取动态列表失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  
  useEffect(() => {
    if (pageNo === 1) {
      // 重置数据
      setDynamic({
        items: [],
        hasMore: true,
        allItems: []
      });
    }
    getDynamicListData();

  }, [pageNo, sortType, isSelectPicker]); // 添加 sortType 作为依赖



  // 处理 tab 切换
  const handleTabChange = (tab: TabType) => {
    setCurrentTab(tab);
    // 切换 tab 时不清空选中状态，只改变展示的数据。
    // 如果需要清空，可以在这里取消注释setSelectedMap(new Map());
  };


  const startDate = () => {
    return (
      <DatePicker
                title="选择日期"
                visible={picker2Visible}
                maskClosable
                disabled={false}
                currentTs={new Date(picker2Value).getTime()}
                mode="date"
                onHide={() => {
                    setPicker2Visible(false);
                }}
                onChange={(timestamp, obj) => {
                    console.info('---demo on change', timestamp);
                    //setPicker2Value(timestamp);
                }}
                onOk={(timestamp, obj) => {
                    console.info('----- time onok demo date', obj, timestamp);
                    setPicker2Value(new Date(timestamp).toISOString().split('T')[0]);
                }}
                formatter={(value, type) => {
                    if (type === 'year') {
                        return `${value}年`;
                    } else if (type === 'month') {
                        return `${value}月`;
                    } else if (type === 'date') {
                        return `${value}日`;
                    }
                }}
            />
    )
  }

  const endDate = () => {
    return (
      <DatePicker
                title="选择日期"
                visible={picker3Visible}
                maskClosable
                disabled={false}
                currentTs={new Date(picker3Value).getTime()}
                mode="date"
                onHide={() => {
                    setPicker3Visible(false);
                }}
                onChange={(timestamp, obj) => {
                    console.info('---demo on change', timestamp);
                    //setPicker3Value(timestamp);
                }}
                onOk={(timestamp, obj) => {
                    console.info('----- time onok demo date', obj, timestamp);
                    setPicker3Value(new Date(timestamp).toISOString().split('T')[0]);
                }}
                formatter={(value, type) => {
                    if (type === 'year') {
                        return `${value}年`;
                    } else if (type === 'month') {
                        return `${value}月`;
                    } else if (type === 'date') {
                        return `${value}日`;
                    }
                }}
            />
    )
  }

  const reset=()=>{
    setSelectedTagIndexes([]);
    setPicker2Value('');
    setPicker3Value('');
  }

  const finish=()=>{
    setVisible(false);
    setSelectedTagIndexesNew(selectedTagIndexes);
    
    const date=picker2Value=='' ? (picker3Value=='' ? '' : '2015-01-01--'+picker3Value ) : (picker3Value=='' ? picker2Value+'--'+new Date().toISOString().slice(0, 10)  : picker2Value+'--'+picker3Value )
    
    //setPicker2ValueNew(picker2Value);
    setDateRange(date)
    getDynamicListData()
    console.log('date--------', date);
    console.log('selectedTagIndexes', selectedTagIndexes);
    console.log('picker2Value', picker2Value);
    console.log('picker3Value', picker3Value);
  }

  const filter=()=>{
    return (
      <View className="setPageContent" >
        <Popup    
        contentStyle={{width: '250px'}}  //{{width: '250px', height: 'calc(100% - 50px)',marginTop: '50px'}}
        direction = 'right'
        visible={visible} 
        close={() => setVisible(false)} >      

          <View className="filter-container">
            <View className="filter-title" >时间区间</View>
            <View className="filter-title">
            <Cell.Group bordered={false}>
              <Cell className="date-picker-cell"  text={picker2Value} label="起始时间" showArrow onClick={() => {setPicker2Visible(true)}} />
              <Cell className="date-picker-cell"  text={picker3Value} label="结束时间" showArrow onClick={() => {setPicker3Visible(true)}} />
            </Cell.Group>
            </View>
            <View className="source-section">
              <View className="filter-title">
                来源 <Text className="source-subtitle">(仅自己可见)</Text>
              </View>
              <View className="tag-group">
                {tagList.map((tag, idx) => (
                  <Tag
                    key={tag}
                    filleted
                    type="solid"
                    color={selectedTagIndexes.includes(idx) ? '#165DFF' : '#4E5969'}
                    bgColor={selectedTagIndexes.includes(idx) ? '#E8F3FF' : '#F2F3F5'}
                    className={selectedTagIndexes.includes(idx) ? 'active' : ''}
                    onClick={() => {
                      setSelectedTagIndexes(prev =>
                        prev.includes(idx)
                          ? prev.filter(i => i !== idx)
                          : [...prev, idx]
                      );
                    }}
                  >
                    {tag}
                  </Tag>
                ))}
              </View>
            </View>
            <View className="button-group">
              <Button  className='move-footer-btn reset-btn' onClick={reset}> 重置</Button>
              <Button  className='move-footer-btn' style={{marginTop: 0}} onClick={finish}> 确定 </Button>
            </View>
          </View>

        </Popup>
      </View>
    )
  }

  const del = (id) => {
      Dialog.confirm({
      platform: 'ios',
      className: 'dialog-input-demo',
      title: '温馨提示',
      contentAlign: 'left',
      children: (<>
          <div className="dialog-input-demo-hint">删除动态后不可恢复，确认删除？</div>
      </>),

      onOk: async () => {
        Taro.showLoading({
          title: '删除中...',
          mask: true
        });
        
        try {
          const res: any = await deleteDynamic({id: id});
          if(res.code === 0 ){
            // 更新 dynamic.items，过滤掉被删除的项
            setDynamic(prevDynamic => {
              // 对每个分组进行处理
              const updatedItems = prevDynamic.items
                .map(group => ({
                  ...group,
                  // 过滤掉当前分组中被删除的项
                  items: group.items.filter(item => item.id !== id)
                }))
                // 移除空分组
                .filter(group => group.items.length > 0);

              return {
                ...prevDynamic,
                items: updatedItems
              };
            });

            setTimeout(() => {
              Taro.hideLoading();
              Taro.showToast({
                title: '删除成功',
                icon: 'none',
                duration: 2000
              });
            }, 1000);
          }
          
       } catch (error) {
         Taro.hideLoading();
         Taro.showToast({
           title: '删除失败，请重试',
           icon: 'none',
           duration: 2000
         });
         console.error('删除失败:', error);
       }
      },
      onCancel: () => {
        // 用户点击取消时执行
        console.log('用户取消了操作');
      },
    });
  };

  //跳转到发布
  const edit = (item) => {
    console.log('edit', item);
    Taro.setStorageSync('releaseDynamicList', item);
    Taro.navigateTo({
      url: `/pages/releaseDynamic/index?type=2`
    });
  };

  //刷新
  const refresh = async (id) => {
    const res: any = await updateDynamic({id: id, isListed: 1});
    if(res.code === 0){
      Taro.showToast({
        title: '刷新成功',
        icon: 'none',
        duration: 2000
      });
    }
  }

    // 跳转到详情页
    const goToDetailPage = (item) => {
      Taro.navigateTo({
        url: `/pages/detail/index?dynamicId=${item.id}&dynamicUserId=${item.userId}`
      });
    };

  const download = (item) => {
  
    Taro.showLoading({
      title: '下载中...',
      mask: true
    });
    Taro.downloadFile({
      url: item.pictures,
      success: res => {
        Taro.saveFile({
          tempFilePath: res.tempFilePath,
          success: res => {
            Taro.hideLoading();
            Taro.showToast({
              title: '下载成功',
              icon: 'none',
              duration: 2000
            });
          },
          fail: res => {
            Taro.hideLoading();
            Taro.showToast({
              title: '下载失败',
              icon: 'none',
              duration: 2000
            });
          }
        });
      },
      fail: res => {
        Taro.hideLoading();
        Taro.showToast({
          title: '下载失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }


  const canceltop = async (id, isTop) => {
    const newTopStatus = isTop == 1 ? 0 : 1;
    const res: any = await updateDynamic({id: id, isTop: newTopStatus});
    if(res.code === 0){
      // Update the local state to reflect the change immediately
      setDynamic(prevDynamic => ({
        ...prevDynamic,
        items: prevDynamic.items.map(group => ({
          ...group,
          items: group.items.map(item => 
            item.id === id ? { ...item, isTop: newTopStatus } : item
          )
        }))
      }));

      Taro.showToast({
        title: newTopStatus === 1 ? '置顶成功' : '取消置顶成功',
        icon: 'none',
        duration: 2000
      });
    }
  }

  const loadingData = () => {
    if (!isLoading && dynamic.hasMore) {
      setPageNo(prevPageNo => prevPageNo + 1);
    }
  };

  const batch=()=>{
    ActionSheet.open({
      items: [
          { content: '批量分享' , onClick: () => batchShare()  },
          { content: '批量编辑' },
      ],
      cancelText: '取消',
    });

  }

  const batchShare=()=>{
    Taro.navigateTo({
      url: '/pages/my/album/classDynamic/batchShare/index'
    });
  }

  // 渲染商品列表 (除上新外的其他 Tab)
  const renderProductList = () => {
    // 使用 currentTabFilteredItems 替代 filteredData
    return (  
      <View className="move-list-my" >
        {dynamic.items.map(group => (
          <View  key={group.group}  style={{marginTop: '10px'}}>
                <Text className="move-group-title">{group.group}</Text>
                {group.items.map(item => {
                    return (
                    // 只有当 itemId 不为 null 时才渲染该列表项
                        <View key={item.id}>
                            <View className="move-item-my" >
                                {item.coverImage  && (
                                    <Image className="move-img" src={item.coverImage} />
                                )}
                                <View className="move-info-my">
                                    <Text className="move-title-text-my" onClick={() => goToDetailPage(item)}>{item.content}</Text>
                                    <Text className="move-price">¥{item.price}</Text>
                                    <View className="move-actions">
                                      
                                        <Text className="move-action-btn" onClick={() => del(item.id)}>删除</Text>
                                        <Text className="move-action-btn" onClick={() => download(item)}>下载</Text>
                                        <Text className="move-action-btn" onClick={() => refresh(item.id)}>刷新</Text>
                                        <Text className="move-action-btn" onClick={() => edit(item)}>编辑</Text>
                                        <Text className="move-action-btn" onClick={() => canceltop(item.id,item.isTop)}>{item.isTop == 1 ? '取顶' : '置顶'}</Text>
                                        <Text className="move-action-share">分享</Text>
                                    </View>
                                </View>                                   
                            </View>
                        {/* 商品属性区域，保留原逻辑 */}
                        <View className="move-attrs-wrap-my">
                            {((item.skus && item.skus.length > 0) || (item.color && item.color.length > 0)) && (
                            <>
                                <Text
                                className="move-attrs-my"
                                onClick={() =>
                                    setExpandedId(item.id === undefined
                                    ? null
                                    : (expandedId === item.id ? null : item.id)
                                    )
                                }
                                >
                                商品属性
                                {expandedId === item.id ?
                                    <Text className="move-attrs-arrow">▼</Text> :
                                    <Text className="move-attrs-arrow">▶</Text>
                                }
                                </Text>
                                {expandedId === item.id && (
                                <View className="attr-content card-attr-content">
                                        {item.skus && item.skus.length ?
                                        (<View className="attr-row" key="规格">
                                          <Text className="attr-label">规格：</Text>
                                          <View className="attr-values">
                                            {item.skus.map((val, index) => (
                                            <Text
                                                className="attr-value" // 使用 .selected 样式
                                                key={index} // 使用 index 作为 key
                                            >
                                                {val}
                                            </Text>
                                            ))}
                                          </View>
                                        </View>):(<></>)
                                        }

                                        {item.color && item.color.length ?
                                        (<View className="attr-row" key="颜色">
                                            <Text className="attr-label">颜色：</Text>
                                            <View className="attr-values">
                                                {item.color.map((val, index) => (
                                                <Text
                                                    className="attr-value" // 使用 .selected 样式
                                                    key={index} // 使用 index 作为 key
                                                >
                                                    {val}
                                                </Text>
                                                ))}
                                            </View>
                                            </View>):(<></>)
                                        }
                                </View>
                                )}
                            </>
                            )}
                        </View>
                    </View>
                    );
                })}
         </View>      
         ))} 
      </View>
    );
  };

  return (
    <View >

        <View className="select-materials-page" style={{height: '200px'}}> {/* 添加页面整体容器 */}
          {/* 顶部导航栏 */}
          <YkNavBar title={decodeURI(Taro.getCurrentInstance().router?.params.tagName)}        
          />
          {/* 页面主要内容区域，使用 Flexbox 使 ScrollView 填充剩余空间 */}
          <View className="move-content-page" >
            {/* 用户头部信息区域，固定在顶部 */}
            <View className="user-header">
              {/* 头像昵称简介 */}

              {/* tab栏 */}
              <View className="user-header-tabs">
                <View
                  className={`user-header-tab ${currentTab === 'all' ? 'active' : ''}`}
                  onClick={() => handleTabChange('all')}
                >
                  全部 () {/* 显示当前tab下的商品数量 */}
                </View>
                <View
                  className={`user-header-tab ${currentTab === 'new' ? 'active' : ''}`}
                  //onClick={() => handleTabChange('new')}
                >
                  内容1 ({newFilteredData.reduce((sum, group) => sum + group.items.length, 0)}) {/* 显示上新商品数量 */}
                </View>
                <View
                  className={`user-header-tab ${currentTab === 'video' ? 'active' : ''}`}
                  onClick={() => handleTabChange('video')}
                >
                  内容2 () {/* 显示当前tab下的商品数量 */}
                </View>
                <View
                  className={`user-header-tab ${currentTab === 'image' ? 'active' : ''}`}
                  onClick={() => handleTabChange('image')}
                >
                  内容3 () {/* 显示当前tab下的商品数量 */}
                </View>
              </View>


              {/* 搜索栏 - 根据图片，只在非上新页面显示 */}
              {currentTab !== 'new' && (
                <View className="user-header-searchbar-wrap"> {/* 添加搜索栏容器 */}
                  <SearchBar
                    actionButton={<span className="user-header-filter" onClick={() => {setVisible(true)}}>搜索</span>}
                    placeholder="请输入要搜索的内容"
                    onChange={(e, value) => setSearch(value)}  // Handle both event and value
                    value={search}
                    className="user-header-searchbar"
                    clearable
                    onClear={() => {setSearch('');setPageNo(1);getDynamicListData('')}}
                  />                               
                  {filter()}
                  {startDate()}
                  {endDate()}
                </View>
              )}
            </View>
            

            <View className="tag-group-selected">
                {selectedTagIndexesNew.length > 0 && (          
                      <View>  
                            {selectedTagIndexesNew.map((tag, idx) => (
                            <Tag
                              key={tag}
                              filleted
                              type="solid"
                              color={selectedTagIndexesSearch.includes(idx) ? '#165DFF' : '#4E5969'}
                              bgColor={selectedTagIndexesSearch.includes(idx) ? '#E8F3FF' : '#F2F3F5'}
                              className={selectedTagIndexesSearch.includes(idx) ? 'active' : ''}
                              onClick={() => {
                                setSelectedTagIndexesSearch(prev =>
                                  prev.includes(idx)
                                    ? prev.filter(i => i !== idx)
                                    : [...prev, idx]
                                );
                              }}
                            >
                              {tagList[tag]}
                            </Tag>
                          ))}
                      </View>         
                )}
                {dateRange != '' && (
                      <View>  
                        <Tag
                          key={dateRange}
                          filleted
                          type="solid"
                          color={isSelectPicker ?   '#165DFF' : '#4E5969' }
                          bgColor={isSelectPicker ? '#E8F3FF' : '#F2F3F5' }
                          className={isSelectPicker ? '' : 'active' }
                          onClick={() => {
                            isSelectPicker==0 ? setIsSelectPicker(1) :  setIsSelectPicker(0)
          
                          }}
                        >
                          {dateRange}  
                        </Tag>   
                      </View>
                )}

            </View>

            <ScrollView  className="move-list-scroll"    
              scrollY
              onScrollToLower={loadingData}     
            >
              {renderProductList()}
            </ScrollView>
            {isLoading && (
            <View className="loading-more">
                <Text className="loading-text">加载中...</Text>
            </View>
            )}
            {!dynamic.hasMore && dynamic.items.length > 0 && (
            <View className="no-more">
                <Text className="no-more-text">没有更多了</Text>
            </View>
            )}
          </View>
          <Button  className='move-footer-btn' onClick={batch}> 批量分享/编辑 </Button>
        </View>
        
        
    </View>

  );
}

