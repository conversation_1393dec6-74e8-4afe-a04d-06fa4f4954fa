/*
修改文件：src/pages/my/album/index.tsx
目的：修复 ReferenceError，将 getFilteredDataByTab 函数定义提前。
*/
import { View, Text, Image, Button, Input, ScrollView, CoverImage } from "@tarojs/components";
import React, { useState, useEffect, useRef  } from "react";
import Taro, { useDidShow, useReady } from "@tarojs/taro";
import { Dialog, SearchBar, ActionSheet, Checkbox } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import { getMyAlbumList, upAndDownShelvesDynamic } from "@/utils/api/common/common_user";
import "./index.less";
import { count } from "console";
import { content } from "html2canvas/dist/types/css/property-descriptors/content";
import { ids } from "webpack";


// 定义 dynamic 类型
interface DynamicItem {
  id: string | number;
  content: string;
  price: number;
  time: string;
  pictures: string;  // API returns pictures as comma-separated string
  coverImage: string[];  // Transformed pictures into array
  skus?: string[];
  color?: string[];
  digitalWatermark?: string;
  isTop?: number;  // Add isTop property to the interface
}

interface GroupedItem {
  group: string;
  items: DynamicItem[];
}

interface DynamicData {
  items: GroupedItem[];
  totalCount?: number;
  hasMore?: boolean;
  allItems?: DynamicItem[]; // 添加 allItems 字段存储所有商品数据
}

interface UserHomeTopData {
  avatar?: string;
  nickname?: string;
  total?: number;
}

// 定义 tab 类型
type TabType = 'up' | 'down' ;

export default function BatchDeletePage() {
  const [dynamic, setDynamic] = useState<DynamicData>({ items: [], hasMore: true, allItems: [] });
  const [selectedIds, setSelectedIds] = useState<(string|number)[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize] = useState(20);
  const [sortType, setSortType] = useState(1);
  const [isListed,setIsListed] = useState(1);
  const [currentTab, setCurrentTab] = useState<TabType>('all');  // 添加当前 tab 状态

  const getDynamicListData = async (isListed=1) => {
    // if (isLoading || !dynamic.hasMore) return;

    setIsLoading(true);
    Taro.showLoading({
      title: '加载中...',
      mask: true
    });

    const data = {
      pageNo: pageNo,
      pageSize: pageSize,
      userId: 299,
      sort: sortType,
      isListed: isListed
    } as any;

    try {
      const res: any = await getMyAlbumList(data);
      Taro.hideLoading();
      
      if (res.code === 0 && res.data) {
        setSelectedIds([])
        const itemsWithSkus = res.data.list?.map(item => {
          const pictures = item.pictures || '';
          // 分割图片字符串，并取第一张（过滤空值）
          const pictureArray = pictures.split(',').filter(Boolean);
          const coverImage = pictureArray[0] || ''; // 如果没有图片则设为空字符串;

          return {
            ...item,
            pictures,
            coverImage: coverImage,
            pictureArray: pictureArray,
          };
        }) || [];
  
        // 更新状态，累加数据
        setDynamic(prevDynamic => {
          // 合并所有商品数据
          const allItems = pageNo === 1 ? itemsWithSkus : [...(prevDynamic.allItems || []), ...itemsWithSkus];
          const hasMore = res.data.list?.length === pageSize;
          
          return {
            items: allItems,
            hasMore
          };
        });

      } else {
        Taro.showToast({
          title: res.msg || '加载失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      Taro.hideLoading();
      Taro.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });
      console.error('获取动态列表失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 获取数据逻辑同主页面
  useEffect(() => {
    getDynamicListData();
  }, []);

  // 全选/反选
  const handleSelectAll = () => {
    if (isAllSelected) {
      setSelectedIds([]);
      setIsAllSelected(false);
    } else {
      const all = dynamic.items.flatMap(group => group.items.map(item => item.id));
      setSelectedIds(all);
      setIsAllSelected(true);
    }
  };

  // 单选
  const handleSelect = (id) => {
    let newSelected;
    if (selectedIds.includes(id)) {
      newSelected = selectedIds.filter(_id => _id !== id);
    } else {
      newSelected = [...selectedIds, id];
    }
    
    setSelectedIds(newSelected);
    setIsAllSelected(newSelected.length === dynamic.items.flatMap(g=>g.items).length);
  };

  // 批量上下架
  const handleBatchUpAndDownShelves= () => {
    if (selectedIds.length === 0) return;
    Dialog.confirm({
      platform: 'ios',
      className: 'dialog-input-demo',
      title: '温馨提示',
      contentAlign: 'left',
      children: (<>
          <div className="dialog-input-demo-hint" style={{ textAlign: 'center' }}> 确定要{isListed === 1 ? '下架' : '上架'}<span style={{ color: '#f50' }}>{selectedIds.length}</span>件商品吗？</div>
      </>),

      onOk: async () => {
        setIsLoading(true);
        await upAndDownShelvesDynamic({ ids: selectedIds.join(','), status: isListed==1? 2 : 1 });
        setDynamic(prev => ({
          ...prev,
          items: prev.items.filter(item => !selectedIds.includes(item.id))
        }));
        setSelectedIds([]);
        setIsAllSelected(false);
        setIsLoading(false);
        Taro.showToast({ title: isListed === 1 ? '下架成功' : '上架成功', icon: 'none' });
      }
    });
  };

  const handleTabChange = (tab: TabType) => {
    if(tab === 'up'){
        setIsListed(1)
        getDynamicListData(1);
    }else{
        setIsListed(2)
        getDynamicListData(2);
    }
    
    setCurrentTab(tab);

    // 切换 tab 时清空选中状态
    //setSelectedMap(new Map());
  };

  // 列表渲染
  const renderList = () => (
    <View className="move-list-my-shelves">
      {dynamic.items.map(item => (
        <View key={item.id} className="move-item-my-shelves">
          <Checkbox
            className="move-checkbox"
            checked={selectedIds.includes(item.id)}
            onChange={() => handleSelect(item.id)}
          />
          <View >
            <View >
              <Text className="move-title-text-my">{item.content}</Text>
            </View>
            <View className="move-content-my">
              {item.pictureArray && item.pictureArray.slice(0, 6).map((pic, idx) => (
                  <Image className="move-img" src={pic} />
              ))}
            </View>
          </View>
        </View>
      ))}
    </View>
  );

  return (
    <View className="select-materials-page-wrap">
      <YkNavBar title="批量上下架" />
      <View className="user-header-tabs-wrap">
            <View 
              className={`user-header-tab ${currentTab === 'up' ? 'active' : ''}`}
              onClick={() => handleTabChange('up')}
            >
              上架
            </View>
            <View 
              className={`user-header-tab ${currentTab === 'down' ? 'active' : ''}`}
              onClick={() => handleTabChange('down')}
            >
              下架
            </View>

          </View>  

      <ScrollView className="move-list-scroll" scrollY>
        {renderList()}
      </ScrollView>
      <View className="move-footer">
        {/* <Checkbox className="move-footer-checkbox" checked={isAllSelected} onChange={handleSelectAll}>
          全选
        </Checkbox> */}
        <Text style={{ marginLeft: 8 }}>选中{selectedIds.length}条</Text>
        <Button
          className={`move-footer-btn${selectedIds.length === 0 ? ' disabled' : ''}`}
          disabled={selectedIds.length === 0}
          onClick={handleBatchUpAndDownShelves}
          loading={isLoading}
        >
          {isListed === 1 ? '下架' : '上架'}
        </Button>
      </View>
    </View>
  );
}
