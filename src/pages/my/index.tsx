import { View, Text } from "@tarojs/components";
import { useLoad, useDidShow } from "@tarojs/taro";
import "./index.less";
import { Cell, Image, Button } from "@arco-design/mobile-react";
import { cls } from '@arco-design/mobile-utils';

import {
  IconRight
} from "@arco-iconbox/react-yk-arco";
import React from "react";
import Taro from "@tarojs/taro";
import { navigateToVue } from "@/utils/yk-common";
import { navigateToOnlinePayment } from "@/pages/onlinePayment/utils/navigation";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
import YkSwitchTabBar from "@/components/ykSwitchTabBar/index";
// 调试器
import {
  DebugPanel,
  createDebugger,
  createDebugConfig,
  DebugScenario
} from "@/pages/debug";
// 本地类型和工具
import {
  UserInfo,
  VipInfo,
  VipStatus,
  getMemberStatusText,
  isMemberValid,
  getVipStatusText,
  myPageDebugScenarios,
  myPageMockDataGenerators
} from "./utils";
// import initCustomerServer from "./customerServer";

export default function My() {
  // 用户资料（包含会员信息）
  const [userInfo, setUserInfo] = React.useState<UserInfo | null>(null);
  // 钱包金额
  const [walletAmount, setWalletAmount] = React.useState<string>("0");
  // 会员信息（保留用于调试）
  const [vipInfo, setVipInfo] = React.useState<VipInfo>({
    isVip: VipStatus.NON_VIP
  });

  // 创建调试器
  const myDebugger = createDebugger(
    createDebugConfig(
      '我的页面',
      {
        scenarios: myPageDebugScenarios,
        mockDataGenerators: myPageMockDataGenerators
      }
    )
  );

  useLoad(() => {
    setUserInfo(Taro.getStorageSync("userInfo"));
    console.log(Taro.getStorageSync("userInfo"))
    // TODO: 获取钱包金额

  });


  useDidShow(() => {
    setUserInfo(Taro.getStorageSync("userInfo"));
    console.log(Taro.getStorageSync("userInfo"))
  
  });
  

  // 跳转到会员中心
  const goToVipCenter = () => {
    myDebugger.debugLog('[basic] 跳转到会员中心', {
      userInfo,
      isMember: userInfo?.isMember,
      memberExpireTime: userInfo?.memberExpireTime
    });

    Taro.navigateTo({ url: "/pages/vipCenter/index" })
  };

  const handleCustomerService = () => {
    // const option = {
    //   openUrl: "https://pipakf.fjpipixia.com",
    //   token: "f99daaddc7c3a45adb0384ed34acf489",
    //   kefuid: '',
    //   isShowTip: false,
    //   windowStyle: 'center',
    //   domId: 'customerServerTip',
    //   insertDomNode: 'body',
    //   sendUserData: {
    //     uid: userInfo?.id,
    //     nickName: userInfo?.nickname,
    //     sex: '1',
    //     avatar: userInfo?.head_img,
    //     phone: '',
    //     openid: ''
    //   },
    //   deviceType: "Mobile"
    // };
    // console.log(option)

    // // 初始化客服系统
    // const customerServer = new initCustomerServer(option);
    // customerServer.init();
    // customerServer.getCustomeServer();
    Taro.navigateTo({ url: "/pages/my/mobileCustomerServer/index" })
    // navigateToVue('/pages/pagesA/customerService/customerService')
  };

  // 跳转到在线收款（智能跳转）
  const handleOnlinePayment = () => {
    console.log('[我的页面] 点击在线收款，使用智能导航');
    // 使用工具函数处理状态检查和智能跳转
    navigateToOnlinePayment();
  };

  // 菜单配置
  const menuItems = [
    {
      // icon: <IconUserGroup />,
      icon: <Image src={require('@/assets/images/my/my_fensi.png')} className="menuItem-icon" bottomOverlap={null} fit="contain"/>,
      text: "我的粉丝",
      onClick: () => navigateToVue('/pages/pagesA/fans/index')
    },
    {
      // icon: <IconUserGroup />,
      icon: <Image src={require('@/assets/images/my/my_xiangce.png')} className="menuItem-icon" bottomOverlap={null} fit="contain"/>,
      text: "我的相册",
      onClick: () => Taro.navigateTo({ url: "/pages/my/album/index" })
    },
    {
      // icon: <IconQrcode />,
      icon: <Image src={require('@/assets/images/my/my_erweima.png')} className="menuItem-icon" bottomOverlap={null} fit="contain"/>,
      text: "二维码",
      onClick: () => Taro.navigateTo({ url: "/pages/my/qrcode/index" })
    },
    {
      // icon: <IconNotification />, 
      icon: <Image src={require('@/assets/images/my/my_xiaochengxuma.png')} className="menuItem-icon" bottomOverlap={null} fit="contain"/>,
      text: "小程序码",
      onClick: () => navigateToVue('/pages/pagesA/realNameAuthentication/realNameAuthenticationFinish')
    },
    {
      // icon: <IconNotification />,
      icon: <Image src={require('@/assets/images/my/my_zaixianshoukuan.png')} className="menuItem-icon" bottomOverlap={null} fit="contain"/>,
      text: "在线收款",
      onClick: handleOnlinePayment
      // onClick: () => navigateToVue('/pages/pagesA/realNameAuthentication/realNameAuthenticationFinish')
    },
    {
      // icon: <IconNotification />, 
      icon: <Image src={require('@/assets/images/my/my_shimingrenzheng.png')} className="menuItem-icon" bottomOverlap={null} fit="contain"/>,
      text: "实名认证",
      onClick: () => navigateToVue('/pages/pagesA/realNameAuthentication/realNameAuthenticationFinish')
    },
    {
      // icon: <IconNotification />, 
      icon: <Image src={require('@/assets/images/my/my_shouhou.png')} className="menuItem-icon" bottomOverlap={null} fit="contain"/>,
      text: "售后",
      onClick: () => navigateToVue('/pages/pagesA/afterSales/index')
    }
  ];

  const bottomMenus = [
    {
      text: "客服",
      onClick: handleCustomerService
    },
    {
      text: "我的钱包",
      extra: `￥${walletAmount}`,
      onClick: () => Taro.navigateTo({ url: "pages/wallet/index" })
    },
    {
      text: "我的收藏",
      onClick: () => navigateToVue('/pages/my/myCollect')
    },
    {
      text: "设置",
      onClick: () => Taro.navigateTo({ url: "/pages/settings/index" })
    }
  ];

      // 添加订单统计数据
      const orderStats = [
        { number: 8, label: '待付款' },
        { number: 6, label: '已付款' },
        { number: 3, label: '已发货' },
        { number: 2, label: '退款' },
      ];

  // 加载模拟数据
  const loadMockData = async (scenario: DebugScenario) => {
    myDebugger.debugLog(`[mock] 开始加载模拟数据，场景: ${scenario}`);

    const mockVip = myDebugger.generateMockData<VipInfo>(scenario);
    if (mockVip) {
      setVipInfo(mockVip);
      myDebugger.debugLog(`[mock] 模拟数据加载完成`, mockVip);
    }
  };

  return (
    <View className="page">
      <YkNavBar switchTab title="我的" />
      
      {/* 用户信息 */}
      <View className="userInfo" onClick={() => Taro.navigateTo({ url: "/pages/EditUserInfo/index" })}>
        <Image
          src={userInfo?.avatar || ""}
          className="avatar"
          radius="50%"
        />
        <View className="info">
          <View className="nickname">{userInfo?.nickname}</View>
          <View className="userId">ID: {userInfo?.id}</View>
        </View>
        <IconRight className="arrow" />
      </View>

    <View className={cls("member-card", {'member-valid': isMemberValid(userInfo)})}>
      <View className="member-card-blur1"></View>
      <View className="member-card-blur2"></View>
      {isMemberValid(userInfo) && 
        <View className="vip-badge">
          <Text className="vip-text">VIP</Text>
        </View>
      }
      <View className="member-card-text">{getMemberStatusText(userInfo)}</View>
      <Button className="member-card-btn" onClick={goToVipCenter}>
        {isMemberValid(userInfo) ? '去续费' : '去开通'}
      </Button>
    </View>

      {/* 功能菜单 */}
      <View className="menuGrid">
        {menuItems.map((item, index) => (
          <View key={index} className="menuItem" onClick={item.onClick}>
            {item.icon}
            <Text className="menuText">{item.text}</Text>
          </View>
        ))}
      </View>

      <View className="orderStatsBox">
          <View className="orderStatsTitle">
            <Text>我买的</Text>
            <Text className="viewAll" onClick={() => {
              Taro.navigateTo({ url: "/pages/order/index" })
              
            }}>全部</Text>
          </View>
          <View className="orderStatsContent">
            {orderStats.map((item, index) => (
              <View key={index} className="statsItem" onClick={() => {
                navigateToVue(`/pages/pagesA/order/buyOrder?current=${index + 1}`)
              }}>
                <Text className="number">{item.number}</Text>
                <Text className="label">{item.label}</Text>
              </View>
            ))}
          </View>
        </View>

      {/* 底部菜单列表 */}
      <Cell.Group className="bottomMenu">
        {bottomMenus.map((item, index) => (
          <Cell
            key={index}
            label={item.text}
            text={item.extra}
            showArrow
            onClick={item.onClick}
          />
        ))}
      </Cell.Group>

      <YkSwitchTabBar activeTab={4} />

      {/* 调试组件 - 独立的调试功能，不影响实际页面代码 */}
      {myDebugger.isDebugMode() && (
        <DebugPanel
          scenario={myDebugger.getDebugScenario()}
          config={myDebugger.config}
          onLoadMockData={loadMockData}
          statusInfo={{
            '用户ID': userInfo?.id || '未登录',
            '会员状态': userInfo?.isMember ? '✅ 会员' : '❌ 非会员',
            '到期时间': userInfo?.memberExpireTime || '无',
            '显示文本': getMemberStatusText(userInfo),
            '调试VIP状态': vipInfo.isVip === VipStatus.VIP ? '✅ 会员' : '❌ 非会员',
            '调试显示文本': getVipStatusText(vipInfo)
          }}
        />
      )}
    </View>
  );
}