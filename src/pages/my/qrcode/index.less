@import '@arco-design/mobile-react/style/mixin.less';

.qrcode-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background-color: #ffffff;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

.qrcode-page-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #ffffff;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

.qrcode-container {
  width: 100%;
  max-width: 320px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  &:active {
    opacity: 0.9;
  }
}

.qrcode-header {
  margin: 0;
  text-align: center;
}

.qrcode-logo {
  width: 60px;
  height: 60px;
  margin: 0 auto;
  display: block;
}

.qrcode-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  .use-var(color, font-color);
  .use-dark-mode-query({
    color: @dark-font-color;
  });
  line-height: 26px;
  margin-bottom: 4px;
  white-space: nowrap;
}

.qrcode-subtitle {
  font-size: 14px;
  color: #999;
  .use-var(color, sub-info-font-color);
  .use-dark-mode-query({
    color: @dark-sub-info-font-color;
  });
  line-height: 22px;
  margin-bottom: 16px;
}

.qrcode-content {
  width: 100%;
  display: flex;
  justify-content: center;
  background-color: #ffffff;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

.qrcode-card {
  width: 100%;
  max-width: 320px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  background-color: #ffffff;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-top: 8px;
}

.user-name {
  font-size: 16px;
  color: #333;
  .use-var(color, font-color);
  .use-dark-mode-query({
    color: @dark-font-color;
  });
  line-height: 24px;
  font-weight: 500;
}

.qrcode-box {
  width: 180px;
  height: 180px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 12px 0;
  border: 1px solid #f0f0f0;
  .use-var(border-color, line-color);
  .use-dark-mode-query({
    border-color: @dark-line-color;
  });
  padding: 0;
  background-color: #ffffff;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

// .qrcode-svg-container {
  
// }

.qrcode-canvas {
  width: 160px;
  height: 160px;
  display: block;
  margin: auto;
}

.qrcode-tip {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
  font-size: 14px;
  color: #999;
  white-space: nowrap;
}

.wechat-icon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
  flex-shrink: 0;
}

.tip-text {
  font-size: 14px;
  color: #999;
  .use-var(color, sub-info-font-color);
  .use-dark-mode-query({
    color: @dark-sub-info-font-color;
  });
  white-space: nowrap;
}

.footer-button {
  position: absolute;
  bottom: 34px;
  left: 0;
  right: 0;
  width: 100%;
  padding: 16px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  
  .share-button {
    width: 164px;
    height: 32px;
    padding: 16px 16px;
    background-color: #1677ff;
    .use-var(background-color, primary-color);
    .use-dark-mode-query({
      background-color: @dark-primary-color;
    });
    border-radius: 22px;
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 添加图片预览相关样式
.image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 30px 0px;
  background-color: rgba(0, 0, 0, 0.8);
  .use-var(background-color, mask-background);
  .use-dark-mode-query({
    background-color: @mask-background;
  });
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.close-preview-button {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  z-index: 1001;
} 