import React, { useState, useEffect, useRef } from 'react';
import { View, Text, Image, Canvas } from '@tarojs/components';
import { Button } from '@arco-design/mobile-react';
import YkNavBar from '@/components/ykNavBar';
import Taro from '@tarojs/taro';
import { QRCodeSVG } from 'qrcode.react';
import html2canvas from 'html2canvas';

import {URL_BASE} from '@/utils/api/urls';
import logoImg from '@/assets/images/common/logo.png';
import defaultHeadImg from '@/assets/images/common/default_head.png';
import wechatIcon from '@/assets/images/common/wx_icon.png';
import BottomPopup from '@/components/BottomPopup';
import './index.less';

// 声明全局常量
declare const APP_NAME_CN: string;

const QrcodePage: React.FC = () => {
  const userInfo = Taro.getStorageSync('userInfo');
  const [qrcodeUrl, setQrcodeUrl] = useState<string>('');
  const canvasRef = useRef<any>(null);
  const [qrcodeImage, setQrcodeImage] = useState<string>('');
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const [isActionPopupVisible, setIsActionPopupVisible] = useState<boolean>(false);

  // 初始化数据
  useEffect(() => {
    setTimeout(() => {
      generateQrcode();
    }, 300);
  }, []);

  // 生成二维码
  const generateQrcode = () => {
    try {
      Taro.showLoading({ title: '正在生成二维码' });
      setTimeout(() => {
        // 构建分享链接
        const baseUrl = Taro.getStorageSync('baseUrl') || URL_BASE;
        const shareText = `${baseUrl}/#/pages/followUser/index?uid=${userInfo.id}&redirect=wx`;
        setQrcodeUrl(shareText);
        Taro.hideLoading();
      }, 1000);
    } catch (error) {
      console.error('生成二维码失败', error);
      Taro.hideLoading();
    }
  };

  // 将二维码元素转换为图片
  const convertToImage = () => {
    Taro.showLoading({ title: '生成图片中...' });
    
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      const element = document.getElementById('qrcode-page-content');
      if (!element) {
        console.error('找不到二维码容器元素');
        Taro.hideLoading();
        return;
      }
      
      html2canvas(element, {
        useCORS: true,
        scale: 2,
        logging: false
      }).then(canvas => {
        const base64Image = canvas.toDataURL('image/png');
        setQrcodeImage(base64Image);
        Taro.hideLoading();
        setShowPreview(true);
      }).catch(error => {
        console.error('转换图片失败', error);
        Taro.hideLoading();
        Taro.showToast({
          title: '生成图片失败',
          icon: 'none'
        });
      });
    } else {
      Taro.hideLoading();
      Taro.showToast({
        title: '当前环境不支持此功能',
        icon: 'none'
      });
    }
  };

  // 点击二维码区域
  const handleQrcodeClick = () => {
    convertToImage();
  };

  // 关闭预览
  const handleClosePreview = () => {
    setShowPreview(false);
  };

  // 返回上一页
  const handleBack = () => {
    Taro.navigateBack();
  };

  // 分享
  const handleShare = () => {
    convertToImage();
    setTimeout(() => {
      if (qrcodeImage) {
        setIsActionPopupVisible(true);
      }
    }, 1000);
  };

  // 处理底部弹出菜单操作
  const handleActionConfirm = (index: number) => {
    switch (index) {
      case 0: // 转发到微信好友
        Taro.showShareMenu({
          withShareTicket: true
        });
        Taro.showToast({
          title: '请点击右上角分享',
          icon: 'none',
          duration: 2000
        });
        break;
      case 1: // 保存图片
        if (Taro.getEnv() === Taro.ENV_TYPE.WEB) {
          // H5环境下尝试直接保存
          saveImageToAlbum();
          // 额外提示，引导用户长按保存
          setTimeout(() => {
            Taro.showModal({
              title: '提示',
              content: '如果自动下载失败，请长按图片后选择"保存图片"',
              showCancel: false,
              confirmText: '知道了'
            });
          }, 1500);
        } else {
          saveImageToAlbum();
        }
        break;
      default:
        break;
    }
  };

  // 保存图片到相册
  const saveImageToAlbum = () => {
    if (!qrcodeImage) {
      Taro.showToast({
        title: '图片未生成',
        icon: 'none'
      });
      return;
    }

    Taro.showLoading({ title: '正在保存...' });

    // 区分环境：H5环境和小程序环境
    if (Taro.getEnv() === Taro.ENV_TYPE.WEB) {
      // H5环境下使用a标签下载
      try {
        const link = document.createElement('a');
        link.download = `qrcode_${Date.now()}.png`;
        link.href = qrcodeImage;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        Taro.hideLoading();
        Taro.showToast({
          title: '图片已准备，请确认下载',
          icon: 'none',
          duration: 2000
        });
      } catch (error) {
        console.error('保存图片失败', error);
        Taro.hideLoading();
        Taro.showToast({
          title: '保存失败，请长按图片手动保存',
          icon: 'none',
          duration: 2000
        });
      }
    } else {
      // 小程序环境
      try {
        const base64Data = qrcodeImage.split(',')[1];
        const fsm = Taro.getFileSystemManager();
        const filePath = `${Taro.env.USER_DATA_PATH}/qrcode_${Date.now()}.png`;
        
        fsm.writeFile({
          filePath,
          data: base64Data,
          encoding: 'base64',
          success: () => {
            Taro.saveImageToPhotosAlbum({
              filePath: filePath,
              success: () => {
                Taro.hideLoading();
                Taro.showToast({
                  title: '保存成功',
                  icon: 'success'
                });
              },
              fail: (err) => {
                console.error('保存到相册失败', err);
                Taro.hideLoading();
                if (err.errMsg.indexOf('auth deny') >= 0) {
                  Taro.showModal({
                    title: '提示',
                    content: '保存图片需要您授权相册权限',
                    confirmText: '去授权',
                    success: (res) => {
                      if (res.confirm) {
                        Taro.openSetting();
                      }
                    }
                  });
                } else {
                  Taro.showToast({
                    title: '保存失败',
                    icon: 'none'
                  });
                }
              }
            });
          },
          fail: (err) => {
            console.error('写入文件失败', err);
            Taro.hideLoading();
            Taro.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        console.error('写入文件失败', error);
        Taro.hideLoading();
        Taro.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    }
  };

  return (
    <View className="qrcode-page">
      {/* 导航栏 */}
      <YkNavBar title="二维码" backArrow onClickLeft={handleBack} />
      
      <View className="qrcode-page-content" id="qrcode-page-content">
        <View className="qrcode-container">
          {/* 标题 */}
          <View className="qrcode-header">
            <Image className="qrcode-logo" src={logoImg} />
            <View className="qrcode-title">我的相册共享二维码</View>
            <View className="qrcode-subtitle">扫码可免费一键转图</View>
          </View>

          {/* 二维码区域 */}
          <View className="qrcode-content">
            <View className="qrcode-card">
              <View className="user-info">
                <Image 
                  className="user-avatar" 
                  src={userInfo.avatar || defaultHeadImg} 
                />
                <Text className="user-name">{userInfo.nickname || APP_NAME_CN}</Text>
              </View>
              
              <View className="qrcode-box" onClick={handleQrcodeClick}>
                {qrcodeUrl ? (
                  <View className="qrcode-svg-container">
                    <QRCodeSVG
                      value={qrcodeUrl}
                      size={160}
                      bgColor="#ffffff"
                      fgColor="#000000"
                      level="H"
                    />
                  </View>
                ) : (
                  <Canvas 
                    className="qrcode-canvas" 
                    canvasId="qrcode-canvas" 
                    ref={canvasRef}
                  />
                )}
              </View>

              <View className="qrcode-tip">
                <Image className="wechat-icon" src={wechatIcon} />
                <Text className="tip-text">扫码共享我的相册</Text>
              </View>
            </View>
          </View>
        </View>
      </View>

      {/* 底部分享按钮 */}
      <View className="footer-button">
        <Button type="primary" className="share-button" onClick={handleShare}>
          分享
        </Button>
      </View>

      {/* 图片预览 */}
      {showPreview && qrcodeImage && (
        <View className="image-preview-overlay" onClick={handleClosePreview}>
          <Image 
            className="image-preview" 
            src={qrcodeImage} 
            showMenuByLongpress={true}
            onClick={(e) => {
              e.stopPropagation();
            }}
            onLongPress={(e) => {
              e.stopPropagation();
              if (Taro.getEnv() === Taro.ENV_TYPE.WEB) {
                // H5环境下长按图片时提示保存方法
                Taro.showActionSheet({
                  itemList: ['保存图片'],
                  success: function (res) {
                    if (res.tapIndex === 0) {
                      saveImageToAlbum();
                    }
                  }
                });
              } else {
                setIsActionPopupVisible(true);
              }
            }}
          />
          {/* 关闭按钮 */}
          <View 
            className="close-preview-button"
            onClick={handleClosePreview}
          >
            ×
          </View>
        </View>
      )}

      {/* 底部操作弹窗 */}
      <BottomPopup
        options={["转发到微信好友", "保存图片"]}
        btnCloseText="取消"
        onConfirm={handleActionConfirm}
        onClose={() => setIsActionPopupVisible(false)}
        visible={isActionPopupVisible}
      />
    </View>
  );
};

export default QrcodePage; 