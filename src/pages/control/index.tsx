import { View, Text } from "@tarojs/components";
import { useLoad, useReachBottom } from "@tarojs/taro";
import "./index.less";
import { Toast, TabBar, Grid, Image } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import { getOrderNum } from "@/utils/api/common/common_user";


import {
    IconApps,
    IconUser,
    IconInteraction,
    IconSettings,
    IconCommand,
    IconNotification,
    IconCamera,
    IconFile,
    IconQrcode,
    IconArrowRight
} from "@arco-iconbox/react-yk-arco";
import { navigateToVue } from "@/utils/yk-common";

// 组件
import YkNavBar from "@/components/ykNavBar/index";
import YkSwitchTabBar from "@/components/ykSwitchTabBar/index";

import React from "react";
  export default function Control() {
        
    // 添加顶部功能区数据
    const topGridData = [
      {
        // img: <IconCamera className="gridModeBoxContent-grid-icon" />,
        img: <Image src={require('@/assets/images/common/fabudongtai.png')} className="gridModeBoxContent-grid-icon" bottomOverlap={null}/>,
        title: <Text className="gridModeBoxContent-grid-text">发布动态</Text>,
        onClick: () => {
          Taro.navigateTo({
            url: `/pages/releaseDynamic/index`,
          })
            // if (this.userInfo.is_vip == 1) {
            // navigateToVue('/pages/pagesA/releaseDynamic/index?type=1')
            // } else {
            //     // toast('warning', {
            //     //     content: '请先开通会员',
            //     //     duration: 2000
            //     // })
            //     navigateToVue('/pages/pagesA/vipCenter/index')
            // }
        },
        className: "gridModeBoxContent-grid-item",
      },
      {
        // img: <IconFile className="gridModeBoxContent-grid-icon" />,
        img: <Image src={require('@/assets/images/common/sucaibanjia.png')} className="gridModeBoxContent-grid-icon" bottomOverlap={null}/>,
        title: <Text className="gridModeBoxContent-grid-text">素材搬家</Text>,
        onClick: () => {
          //navigateToVue('/pages/pagesA/moveMaterials/link')
          Taro.navigateTo({
            // url: '/pages/moveMaterials/selectMaterials/index'
            url: '/pages/moveMaterials/collectLink/index'
          })

        },
        className: "gridModeBoxContent-grid-item",
      },
      {
        // img: <IconQrcode className="gridModeBoxContent-grid-icon" />,
        img: <Image src={require('@/assets/images/common/erweima.png')} className="gridModeBoxContent-grid-icon" bottomOverlap={null}/>,
        title: <Text className="gridModeBoxContent-grid-text">二维码</Text>,
        onClick: () => {
            navigateToVue(`/pages/pagesA/qrcode/index?user_id=${userInfo.user_id}`)
        },
        className: "gridModeBoxContent-grid-item",
      },
      {
        // img: <IconSettings className="gridModeBoxContent-grid-icon" />,
        img: <Image src={require('@/assets/images/common/shezhi.png')} className="gridModeBoxContent-grid-icon" bottomOverlap={null}/>,
        title: <Text className="gridModeBoxContent-grid-text">设置</Text>,
        onClick: () => {
          Taro.navigateTo({ url: "/pages/orderSetting/index" })
        },
        className: "gridModeBoxContent-grid-item",
      },
    ];
  
// 修改订单统计数据，改为从接口获取
const [orderStats, setOrderStats] = React.useState([
  { number: 0, label: '待收款' },
  { number: 0, label: '待发货' },
  { number: 0, label: '已发货' },
  { number: 0, label: '已完成' },
  { number: 0, label: '退款' },
]);

const toast = (func, options) => {
  let _window: any = window;
  if (!!_window.toastInstance) {
    _window.toastInstance.close();
  }
  _window.toastInstance = Toast[func](options);
};

// 获取订单数据
const fetchOrderNumApi = async () => {
  try {
    const res: any = await getOrderNum();
    console.log('[工作台] 获取订单数量返回:', res);
    
    // 确保res不为null且有code属性
    if (res && res.code === 0 && res.data) {
      setOrderStats([
        { number: res.data.wait_pay_count || 0, label: '待收款' },
        { number: res.data.wait_send_goods_count || 0, label: '待发货' },
        { number: res.data.send_goods_count || 0, label: '已发货' },
        { number: res.data.complete_count || 0, label: '已完成' },
        { number: res.data.refund_count || 0, label: '退款' },
      ]);
    } else {
      console.error('[工作台] 获取订单数量失败:', res?.msg || '未知错误');
      toast("error", {
        content: res?.msg || '获取订单数据失败',
        duration: 2000,
      });
    }
  } catch (error) {
    console.error('[工作台] 获取订单数量出错:', error);
    toast("error", {
      content: '网络异常，请重试!',
      duration: 2000,
    });
  }
};
    // 用户资料
  const [userInfo, setUserInfo] = React.useState<any>(false);

  useLoad(() => {
    setUserInfo(Taro.getStorageSync("userInfo").userInfo);
    fetchOrderNumApi();
  });
    // 添加相册功能区数据
    const albumGridData = [
      {
        // img: <IconFile className="albumBox-grid-icon" />,
        img: <Image src={require('@/assets/images/common/work_bqgl.png')} className="albumBox-grid-icon" bottomOverlap={null}/>,
        title: <Text className="albumBox-grid-text">标签管理</Text>,
        onClick: () => {
          Taro.navigateTo({ url: "/pages/albumManage/tagManage/index" });
        },
        className: "albumBox-grid-item",
      },
      {
        // img: <IconFile className="albumBox-grid-icon" />,
        img: <Image src={require('@/assets/images/common/work_plbj.png')} className="albumBox-grid-icon" bottomOverlap={null}/>,
        title: <Text className="albumBox-grid-text">批量编辑</Text>,
        onClick: () => {
            navigateToVue(`/pages/pagesA/albumManage/batchEdit?type=2&user_id=${userInfo.user_id}`)
        },
        className: "albumBox-grid-item",
      },
      {
        // img: <IconFile className="albumBox-grid-icon" />,
        img: <Image src={require('@/assets/images/common/work_sxjtw.png')} className="albumBox-grid-icon" bottomOverlap={null}/>,
        title: <Text className="albumBox-grid-text">上下架图文</Text>,
        onClick: () => {
            //判断vip
            Taro.navigateTo({
              url: '/pages/albumManage/index'
            })
            // navigateToVue('/pages/pagesA/albumManage/batchOnOffShelf')
        },
        className: "albumBox-grid-item",
      },
      {
        // img: <IconFile className="albumBox-grid-icon" />,
        img: <Image src={require('@/assets/images/common/work_sxjtw.png')} className="albumBox-grid-icon" bottomOverlap={null}/>,
        title: <Text className="albumBox-grid-text">价格管理</Text>,
        onClick: () => {
            //判断vip
            Taro.navigateTo({
              url: '/pages/albumManage/index'
            })
            // navigateToVue('/pages/pagesA/albumManage/batchOnOffShelf')
        },
        className: "albumBox-grid-item",
      },
      // ... 其他相册功能按钮
    ];
  
    return (
      <View className="indexBox">
        <YkNavBar switchTab title="工作台" />
        
        {/* 顶部功能区 */}
        <View className="gridModeBoxContent">
          <Grid
            data={topGridData}
            columns={4}
            gutter={{ x: 0, y: 16 }}
            className="gridModeBoxContent-grid"
          />
        </View>
  
        {/* 订单统计区域 */}
        <View className="orderStatsBox">
          <View className="orderStatsTitle">
            <View className="orderStatsTitleItem">
            <Text>我卖的</Text>
            <View className="orderStatsTitleItemRight" onClick={() => {
              navigateToVue('/pages/pagesA/order/saleOrder?current=0')
            }} >
              <Text>交易明细 &gt;</Text>
            </View>
            </View>

            <Text className="viewAll" onClick={() => {
              Taro.navigateTo({ url: "/pages/sellOrder/index" })
            }}>全部订单</Text>
          </View>
          <View className="orderStatsContent">
            {orderStats.map((item, index) => (
              <View key={index} className="statsItem" onClick={() => {
                navigateToVue(`/pages/pagesA/order/saleOrder?current=${index + 1}`)
              }}>
                <Text className="number">{item.number}</Text>
                <Text className="label">{item.label}</Text>
              </View>
            ))}
          </View>
        </View>
  
        {/* 相册功能区 */}
        <View className="albumBox">
          <View className="albumBoxTitle">相册</View>
          <View className="">
            <Grid
              data={albumGridData}
              columns={4}
              className="albumBox-grid"
            />
          </View>
        </View>

  
        <YkSwitchTabBar activeTab={2} />
      </View>
    );
  }