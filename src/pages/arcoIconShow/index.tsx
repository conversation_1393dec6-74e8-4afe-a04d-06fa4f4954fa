// @arco-iconbox/react-yk-arco
import { View } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Toast, SearchBar } from "@arco-design/mobile-react";
import React from "react";
import Taro from "@tarojs/taro";

import * as Icons from "@arco-iconbox/react-yk-arco";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

export default function arcoIcon() {
  const [IconsList, setIconsList] = React.useState([]);
  const [allIconsList, setAllIconsList] = React.useState([]);

  const toast = (func, options) => {
    let _window: any = window;
    if (!!_window.toastInstance) {
      _window.toastInstance.close();
    }
    _window.toastInstance = Toast[func](options);
  };

  // 获取图标列表
  const getIconsList = () => {
    let list = Object.keys(Icons);
    let nextList: any = [];
    for (let i = 0; i < list.length; i++) {
      let arr = {
        title: list[i],
        htmlName: Icons[list[i]],
      };
      nextList.push(arr);
    }
    console.log("nextList", nextList);
    setIconsList(nextList);
    setAllIconsList(nextList);
  };

  useLoad(() => {
    getIconsList();
  });

  // 点击复制title
  const handleCopyTitle = (title: string) => {
    Taro.setClipboardData({
      data: `${title}`,
      success: function (res) {
        Taro.showToast({
          title: `已复制 ${title}`,
          icon: "none",
          duration: 2000,
        });
      },
    });
  };

  const [searchVal, setSearchVal] = React.useState("");
  const onInputSearch = (e: any) => {
    setSearchVal(e);
  };

  React.useEffect(() => {
    console.log("searchVal", searchVal == "");
    if (allIconsList.length > 0) {
      if (searchVal !== "") {
        const searchTerm = searchVal.replace(/<|\/|\>/g, "").trim();
        // 过滤,模糊搜索
        let nextList: any = [];

        allIconsList.filter((item: any) => {
          const title = item.title.toLowerCase();
          const term = searchTerm.toLowerCase();
          if (title.includes(term)) {
            nextList.push(item);
          }
        });
        setIconsList(nextList);
      } else {
        setIconsList(allIconsList);
      }
    }
  }, [searchVal]);

  return (
    <View className="arcoIconBox">
      <YkNavBar title="arco-icon图标库-npm引入不支持设定颜色" />
      <SearchBar
        value={searchVal}
        onClear={() => {
          setSearchVal("");
        }}
        onInput={(e, value) => {
          onInputSearch(value);
        }}
      />
      <View className="arcoIconList">
        {IconsList.length > 0 &&
          IconsList.map((item: any, index: number) => {
            return (
              <View
                className="arcoIconItem"
                key={index}
                onClick={() => handleCopyTitle(item.title)}
              >
                <View className="arcoIconItemContent">
                  <item.htmlName className="ykIconClass"  />
                </View>
                <View className="arcoIconItemTitle">{item.title}</View>
              </View>
            );
          })}
      </View>
    </View>
  );
}
