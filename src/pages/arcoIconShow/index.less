@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pages/arcoIconShow/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });
}

.arcoIconBox {
  width: 100%;
  position: relative;
}

.arcoIconList {
  width: 100%;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 4px;
  padding: 12px;

  .arcoIconItem {
    width: 85px;
    height: 85px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: @cell-background-color;
    .use-dark-mode-query({
        background-color: @dark-cell-background-color;
      });

    &:active {
      opacity: 0.8;
    }

    .arcoIconItemContent {
      font-size: 20px;
    }

    .arcoIconItemTitle {
      font-size: 12px;
      color: @font-color;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      word-break: break-all;

      color: var(--primary-color);
      .use-dark-mode-query({
       color: var(--dark-primary-color);
      });
    }
  }
}


.ykIconClass [fill] {
  fill: var(--dark-primary-color);
  .use-dark-mode-query({
    fill: var(--dark-primary-color);
    });
}
.ykIconClass [stroke] {
  stroke: var(--primary-color);
  .use-dark-mode-query({
      stroke: var(--dark-primary-color);
    });
}
