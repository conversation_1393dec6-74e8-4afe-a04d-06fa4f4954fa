import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Toast, Input, Avatar, Image, Loading,SearchBar, LoadMore } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import { IconSearch } from "@arco-iconbox/react-yk-arco";
import React from "react";
import YkNavBar from "@/components/ykNavBar/index";
import { getFriendsList } from "@/utils/api/common/common_user";
import YkSwitchTabBar from "@/components/ykSwitchTabBar/index";
import { navigateToVue } from "@/utils/yk-common";
import { LoadMoreStatus } from "@arco-design/mobile-react/cjs/load-more";
import { useDebounce } from "@/utils/yk-common";
export default function Friends() {
  const [keyword, setKeyword] = React.useState("");
  const [list, setList] = React.useState<any>([]);
  const [loadStatus, setLoadStatus] = React.useState<LoadMoreStatus>("prepare"); //"before-ready" | "prepare" | "loading" | "nomore" | "retry"
  //是否已经加载完 ，且没有更多数据
  const [isLoadEnd, setIsLoadEnd] = React.useState(false);

  const [pages, setPages] = React.useState({
    page: 1,
    limit: 10,
    pageTurn: true,
    loading: false
  });

  // const getAlbumListData = async () => {
  //   let data = {
  //     page: albumPageRef.current,
  //     limit: albumLimit,
  //     type: albumType,
  //   };
  //   if (isLoadEnd) {
  //     setLoadStatus("nomore");
  //     return;
  //   }
  //   const res: any = await getAlbumList(data);
  //   if (res.code == 0) {
  //     setAlbumList(res.data);
  //     // 判断返回数据的总条数
  //     if (res.data.length < albumLimit) {
  //       setIsLoadEnd(true);
  //       setLoadStatus("nomore");
  //     } else {
  //       setLoadStatus("prepare");
  //       setIsLoadEnd(false);
  //       albumPageRef.current = albumPageRef.current + 1;
  //     }
  //   } else {
  //     setLoadStatus("retry");
  //     toast("error", {
  //       content: res.msg,
  //       duration: 2000,
  //     });
  //   }
  // };
  // 获取好友列表
  const getFriends = async () => {
    if (pages.loading || !pages.pageTurn) return;
    
    let data: any = {
      page: pages.page,
      limit: pages.limit
    };
    if (keyword) {
      data.nickname = keyword;
    }
    if (isLoadEnd) {
      setLoadStatus("nomore");
      return;
    }
    setPages(prev => ({...prev, loading: true}));
    
    const res: any = await getFriendsList(data);
    if (res.code === 0) {
      if (res.data.length < pages.limit) {
        setIsLoadEnd(true);
        setLoadStatus("nomore");
      } else {
        setLoadStatus("prepare");
        setIsLoadEnd(false);
      }
      if (pages.page === 1) {
        setList(res.data);
      } else {
        if (res.data.length > 0) {
          setList(prev => [...prev, ...res.data]);
        } else {
          setPages(prev => ({...prev, page: prev.page - 1}));
        }
      }

      setPages(prev => ({
        ...prev,
        loading: false,
        pageTurn: res.data.length >= pages.limit,
        page: res.data.length >= pages.limit ? prev.page + 1 : prev.page
      }));
    } else {
      setLoadStatus("retry");
      Toast.error({
        content: res.msg,
        duration: 2000,
      });
    }
  };

  // 搜索
  const handleSearch = (value: string) => {
    setKeyword(value);
    setPages({
      page: 1,
      limit: 10,
      pageTurn: true,
      loading: false
    });
    setIsLoadEnd(false);
    getFriends();
  };


  const debouncedSearch = useDebounce(handleSearch, 500);

  // 搜索框onChange处理
  const handleSearchChange = (e) => {
    setKeyword(e.target.value); // 立即更新输入框的值
    if (e.target.value.length > 0) {
      debouncedSearch(e.target.value); // 延迟执行搜索
    } else {
      setKeyword('');
      getFriends();
    }
  };

  // 跳转用户详情
  const goToUserDetail = (userId: string) => {
    navigateToVue(`/pages/pagesA/userDetail/index?user_id=${userId}`);
  };

  useLoad(() => {
    getFriends();
  });

  return (
    <View className="friendsBox">
      <YkNavBar switchTab title="好友" />
      
      <SearchBar
        shape="round"
        placeholder={`搜索${list.length}个好友`}
        value={keyword}
        onChange={handleSearchChange}
      />
      

      <View className="fansEntry" onClick={() => navigateToVue('/pages/pagesA/fans/index')}>
        <Image src={require('../../assets/images/common/fans_icon.png')} className="fansIcon" bottomOverlap={null}  />
        <Text>粉丝</Text>
      </View>

      <View className="listBox">
        {list.length > 0 ? (
          <>
            <Text className="listTitle">全部</Text>
            {list.map((item, index) => (
              <View key={item.user_id} className="listItem" onClick={() => goToUserDetail(item.user_id)}>
                <Avatar src={item.head_img || require('../../assets/images/common/default_head.png')} />
                <View className="itemContent">
                  <View className="nameRow">
                    <Text className="nickname">{item.nickname}</Text>
                    {item.sub_mchid && (
                      <Image className="jinjianIcon" src={require('../../assets/images/common/jinjian_y.png')} bottomOverlap={null} />
                    )}
                  </View>
                  <View className="countRow">
                    <Text className="count">上新 {item.new_count}</Text>
                    <Text className="count">总共 {item.total_count}</Text>
                  </View>
                </View>
              </View>
            ))}
          </>
        ) : (
          <View className="emptyBox">
            <Image src={require('../../assets/images/common/not_content.png')} className="emptyIcon" bottomOverlap={null} />
            <Text>暂无好友</Text>
          </View>
        )}

<LoadMore
            style={{ paddingTop: 16, paddingBottom: 16 }}
            status={loadStatus}
            threshold={0}
            getData={getFriends}
          />
      </View>
      <YkSwitchTabBar activeTab={1} />
    </View>
  );
} 