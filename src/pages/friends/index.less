@import "@arco-design/mobile-react/style/mixin.less";


[id^="/pages/friends/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
}

.friendsBox {
  min-height: 100vh;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background: @dark-background-color;
  });
}

// .searchBox {
//   padding: 12px 16px;
//   background: #fff;
//   position: sticky;
//   top: 0;
//   z-index: 100;

//   :global(.arco-input) {
//     border-radius: 20px;
//     border-color: #6cbe70;
//   }
// }

.fansEntry {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background: @dark-background-color;
  });
  margin-bottom: 10px;

  .use-var(color,font-color);
  .use-dark-mode-query({
    color: @dark-font-color;
  });

  .fansIcon {
    width: 44px;
    height: 44px;
    margin-right: 12px;
  }
}

.listBox {
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background: @dark-background-color;
  });
  padding: 0 16px;

  .listTitle {
    display: block;
    .use-var(color, sub-font-color);
    .use-dark-mode-query({
      color: @dark-sub-font-color;
    });
    font-size: 13px;
    padding: 12px 0;
  }

  .listItem {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    .use-dark-mode-query({
      border-bottom: 1px solid #444;
    });

    &:last-child {
      border-bottom: none;
    }

    .itemContent {
      flex: 1;
      margin-left: 12px;

      .nameRow {
        display: flex;
        align-items: center;

        .nickname {
          font-size: 14px;
          .use-var(color, font-color);
          .use-dark-mode-query({
            color: @dark-font-color;
          });
        }

        .jinjianIcon {
          width: 12px;
          height: 11px;
          margin-left: 6px;
        }
      }

      .countRow {
        margin-top: 6px;

        .count {
          // color: #8f8f8f;
          .use-var(color, sub-font-color);
          .use-dark-mode-query({
            color: @dark-sub-font-color;
          });
          font-size: 11px;
          margin-right: 16px;
        }
      }
    }
  }
}

.emptyBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 0;

  .emptyIcon {
    width: 100px;
    height: 100px;
    margin-bottom: 16px;
  }

  text {
    .use-var(color, sub-font-color);
    .use-dark-mode-query({
      color: @dark-sub-font-color;
    });
    font-size: 15px;
  }
}

.noMore {
  display: block;
  text-align: center;
  .use-var(color, sub-font-color);
  .use-dark-mode-query({
    color: @dark-sub-font-color;
  });
  font-size: 12px;
  padding: 12px 0;
} 