/**
 * 调试系统演示页面样式
 */

.debug-test {
  height: 100vh;
  background-color: #f8f9fa;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .test-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    padding-bottom: 120px; // 为调试面板留出空间
  }

  .test-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .test-title {
      display: block;
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }

    .test-description {
      display: block;
      font-size: 14px;
      color: #666;
      margin-bottom: 12px;
      line-height: 1.5;
    }

    .test-status {
      display: block;
      font-size: 12px;
      color: #1890ff;
      margin-bottom: 4px;
    }

    .test-timestamp {
      display: block;
      font-size: 12px;
      color: #999;
    }
  }

  // 数据展示样式
  .data-info {
    margin-top: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 12px;
    background-color: #fafafa;
  }

  .data-row {
    display: flex;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px dashed #eee;

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }
  }

  .data-label {
    width: 80px;
    font-size: 14px;
    color: #666;
    flex-shrink: 0;
  }

  .data-value {
    flex: 1;
    font-size: 14px;
    color: #333;
    word-break: break-all;
  }

  .status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  .user-type {
    font-size: 14px;
  }



  .test-instructions {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .instructions-title {
      display: block;
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 12px;
    }

    .instructions-text {
      display: block;
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
      line-height: 1.5;
    }

    .scenario-group {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .scenario-group-title {
      display: block;
      font-size: 15px;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }
  }

  .test-examples {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .examples-title {
      display: block;
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 12px;
    }

    .examples-url {
      display: block;
      font-size: 12px;
      font-family: 'Courier New', monospace;
      color: #1890ff;
      background: #f0f8ff;
      padding: 8px 12px;
      border-radius: 4px;
      margin-bottom: 8px;
      word-break: break-all;
    }
  }

  // 组件展示样式
  .component-display {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .component-showcase {
    text-align: center;
    padding: 40px 20px;

    .showcase-title {
      display: block;
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 12px;
    }

    .showcase-description {
      display: block;
      font-size: 14px;
      color: #666;
      line-height: 1.5;
    }
  }

  // YkIcons 组件展示样式
  .icons-showcase {
    text-align: center;
    width: 100%;

    .icons-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .showcase-title {
      display: block;
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }

    .refresh-button {
      padding: 6px 12px;
      background: #f0f8ff;
      border: 1px solid #1890ff;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: #e6f7ff;
      }

      &:active {
        background: #d1f2ff;
      }

      text {
        font-size: 12px;
        color: #1890ff;
      }
    }

    .icons-count {
      display: block;
      font-size: 14px;
      color: #666;
      margin-bottom: 4px;
    }

    .icons-info {
      display: block;
      font-size: 12px;
      color: #999;
      margin-bottom: 20px;
    }

    .icons-grid {
      display: flex;
      justify-content: center;
      gap: 20px;
      flex-wrap: wrap;
    }

    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      background: #fafafa;
      min-width: 120px;
      transition: all 0.2s;

      &:hover {
        border-color: #1890ff;
        background: #f0f8ff;
      }

      svg {
        margin-bottom: 8px;
        color: #1890ff;
      }

      .icon-name {
        font-size: 11px;
        color: #333;
        font-weight: bold;
        margin-bottom: 2px;
      }

      .icon-display-name {
        font-size: 10px;
        color: #999;
      }
    }
  }
}
