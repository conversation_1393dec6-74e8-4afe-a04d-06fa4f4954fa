/**
 * 通用调试器工具函数
 * 提供可复用的调试功能，包括URL参数解析、调试日志、场景管理等
 *
 * 注意：所有涉及 window 对象的操作都被包装在运行时检查中，
 * 以避免在构建时期访问 window 对象导致的错误。
 */

// 基础调试场景枚举（所有页面通用）
export enum BaseDebugScenario {
  NONE = '',
  BASIC = 'basic',                    // 基础调试
  API_CALLS = 'api',                  // API调用调试
  MOCK_DATA = 'mock',                 // 模拟数据测试
  ERROR_HANDLING = 'error',           // 错误处理测试
  LOADING_STATE = 'loading',          // 加载状态测试
  EMPTY_STATE = 'empty'               // 空状态测试
}

// 调试场景类型（支持扩展）
export type DebugScenario = BaseDebugScenario | string;

// 调试配置接口
export interface DebugConfig {
  moduleName: string;                 // 模块名称，用于日志前缀
  scenarios: Record<string, any>;     // 自定义场景配置
  mockDataGenerators?: Record<string, (scenario: DebugScenario) => any>; // 模拟数据生成器
}



// 获取URL参数的通用函数
const getUrlParam = (paramName: string): string | null => {
  // 多重安全检查
  if (typeof window === 'undefined') return null;
  if (typeof window.location === 'undefined') return null;
  if (typeof URLSearchParams === 'undefined') return null;

  try {
    // 在Taro H5环境中，URL参数可能在hash后面
    const searchUrl = window.location.search || '';
    const hashUrl = window.location.hash || '';

    // 尝试从search参数中获取
    let urlParams = new URLSearchParams(searchUrl);
    let param = urlParams.get(paramName);

    // 如果search中没有，尝试从hash中获取
    if (param === null && hashUrl.includes('?')) {
      const hashSearch = hashUrl.split('?')[1];
      urlParams = new URLSearchParams(hashSearch);
      param = urlParams.get(paramName);
    }

    return param;
  } catch (error) {
    // 在构建时或其他环境中可能出错，返回null
    return null;
  }
};

// 检查URL中是否存在指定参数（无论是否有值）
const hasUrlParam = (paramName: string): boolean => {
  // 多重安全检查
  if (typeof window === 'undefined') return false;
  if (typeof window.location === 'undefined') return false;
  if (typeof URLSearchParams === 'undefined') return false;

  try {
    // 在Taro H5环境中，URL参数可能在hash后面
    const searchUrl = window.location.search || '';
    const hashUrl = window.location.hash || '';

    // 检查search参数中是否存在
    let urlParams = new URLSearchParams(searchUrl);
    if (urlParams.has(paramName)) {
      return true;
    }

    // 检查hash中是否存在
    if (hashUrl.includes('?')) {
      const hashSearch = hashUrl.split('?')[1];
      urlParams = new URLSearchParams(hashSearch);
      if (urlParams.has(paramName)) {
        return true;
      }
    }

    return false;
  } catch (error) {
    // 在构建时或其他环境中可能出错，返回false
    return false;
  }
};

// 检查是否启用调试模式（只要有debug参数就启用，无论是否有值）
export const isDebugMode = (): boolean => {
  // 延迟执行，确保在运行时调用
  try {
    return hasUrlParam('debug');
  } catch (error) {
    return false;
  }
};

// 获取调试场景
export const getDebugScenario = (validScenarios?: string[]): DebugScenario => {
  // 延迟执行，确保在运行时调用
  try {
    // 如果没有debug参数，返回NONE
    if (!hasUrlParam('debug')) {
      return BaseDebugScenario.NONE;
    }

    const debugParam = getUrlParam('debug');

    // 如果debug参数存在但没有值（如 ?debug），返回基础调试模式
    if (debugParam === null || debugParam === '') {
      return BaseDebugScenario.BASIC;
    }

    // 如果提供了有效场景列表，检查是否在列表中
    if (validScenarios && validScenarios.length > 0) {
      if (validScenarios.includes(debugParam)) {
        return debugParam as DebugScenario;
      }
    } else {
      // 检查是否是基础调试场景
      const baseScenarios = Object.values(BaseDebugScenario) as string[];
      if (baseScenarios.includes(debugParam)) {
        return debugParam as DebugScenario;
      }
    }

    // 如果不是预定义的场景，但有debug参数，返回基础调试模式
    return BaseDebugScenario.BASIC;
  } catch (error) {
    return BaseDebugScenario.NONE;
  }
};

// 创建调试日志函数工厂
export const createDebugLogger = (config: DebugConfig) => {
  return (message: string, data?: any, scenario?: DebugScenario) => {
    if (!isDebugMode()) {
      return;
    }

    const currentScenario = getDebugScenario(Object.keys(config.scenarios));

    // 尝试从 message 中解析场景信息
    let messageScenario: DebugScenario | null = null;
    const scenarioMatch = message.match(/^\[([^\]]+)\]/);
    if (scenarioMatch) {
      const scenarioName = scenarioMatch[1].toLowerCase();
      // 检查是否是有效的场景
      const allScenarios = [...Object.values(BaseDebugScenario), ...Object.keys(config.scenarios)] as string[];
      if (allScenarios.includes(scenarioName)) {
        messageScenario = scenarioName as DebugScenario;
      }
    }

    // 如果指定了场景参数，使用指定的场景
    if (scenario) {
      messageScenario = scenario;
    }

    // 如果从 message 解析出了特定场景，只在该场景下输出日志
    if (messageScenario && messageScenario !== BaseDebugScenario.BASIC && currentScenario !== messageScenario) {
      return;
    }

    const timestamp = new Date().toLocaleTimeString();
    const prefix = `[${config.moduleName} Debug ${timestamp}]`;
    const scenarioSuffix = currentScenario !== BaseDebugScenario.BASIC ? ` [${currentScenario.toUpperCase()}]` : '';

    if (data !== undefined) {
      console.log(`${prefix}${scenarioSuffix} ${message}`, data);
    } else {
      console.log(`${prefix}${scenarioSuffix} ${message}`);
    }
  };
};

// 通用调试日志函数（向后兼容）
export const debugLog = (message: string, data?: any, moduleName: string = 'App') => {
  // 暂时关闭
  // if (!isDebugMode()) {
  //   return;
  // }

  const currentScenario = getDebugScenario();
  const timestamp = new Date().toLocaleTimeString();
  const prefix = `[${moduleName} Debug ${timestamp}]`;
  const scenarioSuffix = currentScenario !== BaseDebugScenario.BASIC ? ` [${currentScenario.toUpperCase()}]` : '';

  if (data !== undefined) {
    console.log(`${prefix}${scenarioSuffix} ${message}`, data);
  } else {
    console.log(`${prefix}${scenarioSuffix} ${message}`);
  }
};

// 模拟API响应工厂
export const createMockApiResponse = <T>(config: DebugConfig) => {
  return (data: T, scenario: DebugScenario, delay: number = 500) => {
    const logger = createDebugLogger(config);
    logger(`[mock] 模拟API响应，场景: ${scenario}`, data);

    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 0,
          data,
          msg: 'success'
        });
      }, delay);
    });
  };
};

// 通用模拟API响应（向后兼容）
export const mockApiResponse = <T>(data: T, scenario: DebugScenario, moduleName: string = 'App') => {
  debugLog(`[mock] 模拟API响应，场景: ${scenario}`, data, moduleName);

  return Promise.resolve({
    code: 0,
    data,
    msg: 'success'
  });
};

// 获取场景显示名称
export const getScenarioDisplayName = (scenario: DebugScenario, config: DebugConfig): string => {
  // 检查是否是基础场景
  switch (scenario) {
    case BaseDebugScenario.BASIC:
      return '基础调试';
    case BaseDebugScenario.API_CALLS:
      return 'API调试';
    case BaseDebugScenario.MOCK_DATA:
      return '模拟数据';
    case BaseDebugScenario.ERROR_HANDLING:
      return '错误处理';
    case BaseDebugScenario.LOADING_STATE:
      return '加载状态';
    case BaseDebugScenario.EMPTY_STATE:
      return '空状态';
    default:
      // 检查自定义场景
      if (config.scenarios[scenario]) {
        return config.scenarios[scenario].displayName || scenario;
      }
      return scenario;
  }
};

// 生成模拟数据
export const generateMockData = <T>(scenario: DebugScenario, config: DebugConfig): T | null => {
  if (config.mockDataGenerators && config.mockDataGenerators[scenario]) {
    return config.mockDataGenerators[scenario](scenario);
  }
  return null;
};

// 验证调试场景是否有效
export const isValidDebugScenario = (scenario: string, config: DebugConfig): boolean => {
  const baseScenarios = Object.values(BaseDebugScenario) as string[];
  const customScenarios = Object.keys(config.scenarios);
  return [...baseScenarios, ...customScenarios].includes(scenario);
};

// ==================== 配置工具函数 ====================

// 场景配置类型定义（供页面使用）
export interface ScenarioConfig {
  displayName: string;
  icon?: string;
  description?: string;
  color?: 'primary' | 'success' | 'warning' | 'danger';
}

// 合并多个调试配置的辅助函数
export const mergeDebugConfigs = (...configs: Partial<DebugConfig>[]): Partial<DebugConfig> => {
  const merged: Partial<DebugConfig> = {
    scenarios: {},
    mockDataGenerators: {}
  };

  configs.forEach(config => {
    if (config.scenarios) {
      Object.assign(merged.scenarios!, config.scenarios);
    }
    if (config.mockDataGenerators) {
      Object.assign(merged.mockDataGenerators!, config.mockDataGenerators);
    }
  });

  return merged;
};

// 创建完整调试配置的辅助函数
export const createDebugConfig = (
  moduleName: string,
  customConfig: Partial<DebugConfig>
): DebugConfig => {
  return {
    moduleName,
    scenarios: customConfig.scenarios || {},
    mockDataGenerators: customConfig.mockDataGenerators || {}
  };
};

// 调试配置构建器
export class DebugConfigBuilder {
  private config: Partial<DebugConfig> = {
    scenarios: {},
    mockDataGenerators: {}
  };

  constructor(private moduleName: string) {}

  // 添加场景
  addScenario(key: string, scenario: ScenarioConfig): this {
    if (!this.config.scenarios) {
      this.config.scenarios = {};
    }
    this.config.scenarios[key] = scenario;
    return this;
  }

  // 添加模拟数据生成器
  addMockDataGenerator<T>(key: string, generator: (scenario: string) => T): this {
    if (!this.config.mockDataGenerators) {
      this.config.mockDataGenerators = {};
    }
    this.config.mockDataGenerators[key] = generator;
    return this;
  }

  // 批量添加场景
  addScenarios(scenarios: Record<string, ScenarioConfig>): this {
    if (!this.config.scenarios) {
      this.config.scenarios = {};
    }
    Object.assign(this.config.scenarios, scenarios);
    return this;
  }

  // 批量添加模拟数据生成器
  addMockDataGenerators(generators: Record<string, (scenario: string) => any>): this {
    if (!this.config.mockDataGenerators) {
      this.config.mockDataGenerators = {};
    }
    Object.assign(this.config.mockDataGenerators, generators);
    return this;
  }

  // 构建最终配置
  build(): DebugConfig {
    return {
      moduleName: this.moduleName,
      scenarios: this.config.scenarios || {},
      mockDataGenerators: this.config.mockDataGenerators || {}
    };
  }
}

// ==================== 调试器创建器 ====================

// 调试器接口
export interface YkDebugger {
  config: DebugConfig;
  isDebugMode: () => boolean;
  getDebugScenario: () => DebugScenario;
  debugLog: (message: string, data?: any, scenario?: DebugScenario) => void;
  mockApiResponse: <T>(data: T, scenario: DebugScenario, delay?: number) => Promise<any>;
  generateMockData: <T>(scenario: DebugScenario) => T | null;
  isValidScenario: (scenario: string) => boolean;
}

// 创建调试器
export const createDebugger = (config: DebugConfig): YkDebugger => {
  // 创建专用的日志函数
  const debugLog = createDebugLogger(config);

  // 创建专用的模拟API响应函数
  const mockApiResponse = createMockApiResponse(config);

  // 获取有效场景列表
  const getValidScenarios = (): string[] => {
    return Object.keys(config.scenarios);
  };

  // 获取当前调试场景
  const getCurrentDebugScenario = (): DebugScenario => {
    return getDebugScenario(getValidScenarios());
  };

  // 生成模拟数据
  const generateMockData = <T>(scenario: DebugScenario): T | null => {
    if (config.mockDataGenerators && config.mockDataGenerators[scenario]) {
      return config.mockDataGenerators[scenario](scenario);
    }
    return null;
  };

  // 验证场景是否有效
  const isValidScenario = (scenario: string): boolean => {
    return getValidScenarios().includes(scenario);
  };

  return {
    config,
    isDebugMode,
    getDebugScenario: getCurrentDebugScenario,
    debugLog,
    mockApiResponse,
    generateMockData,
    isValidScenario
  };
};

// 快速创建简单调试器的辅助函数
export const createSimpleDebugger = (
  moduleName: string,
  scenarios: Record<string, { displayName: string; icon?: string; description?: string }>,
  mockDataGenerators?: Record<string, (scenario: DebugScenario) => any>
): YkDebugger => {
  const config: DebugConfig = {
    moduleName,
    scenarios,
    mockDataGenerators
  };

  return createDebugger(config);
};
