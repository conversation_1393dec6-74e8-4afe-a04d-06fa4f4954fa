/**
 * 通用调试器面板样式
 * 提供统一的调试界面样式，支持多种位置和主题
 * 用于开发阶段的页面调试和测试
 */

.debug-panel {
  position: fixed;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 8px;
  font-size: 12px;
  z-index: 9999;
  max-width: 320px;
  min-width: 220px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);

  // 位置变体
  &--bottom-right {
    bottom: 20px;
    right: 10px;
  }

  &--bottom-left {
    bottom: 20px;
    left: 10px;
  }

  &--top-right {
    top: 20px;
    right: 10px;
  }

  &--top-left {
    top: 20px;
    left: 10px;
  }

  // 头部样式
  &__header {
    padding: 10px 12px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }

    &:active {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  &__title {
    font-weight: bold;
    font-size: 13px;
    flex: 1;
    margin-right: 8px;
  }

  &__toggle {
    font-size: 14px;
    transition: transform 0.2s ease;
  }

  // 内容区域
  &__content {
    padding: 12px;
    max-height: 60vh;
    overflow-y: auto;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 2px;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }

  // 章节标题
  &__section-title {
    display: block;
    font-size: 11px;
    font-weight: bold;
    color: #ccc;
    margin-bottom: 6px;
    margin-top: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &:first-child {
      margin-top: 0;
    }
  }

  // 状态信息区域
  &__status {
    margin-bottom: 8px;
  }

  &__status-item {
    display: flex;
    margin-bottom: 4px;
    font-size: 11px;
    line-height: 1.4;
  }

  &__status-label {
    color: #aaa;
    margin-right: 6px;
    min-width: 60px;
    flex-shrink: 0;
  }

  &__status-value {
    color: #fff;
    word-break: break-all;
    flex: 1;
  }

  // 按钮区域
  &__buttons {
    margin-bottom: 8px;
  }

  &__button-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px;
    margin-top: 6px;
  }

  &__button {
    font-size: 10px !important;
    padding: 6px 8px !important;
    height: auto !important;
    min-height: 28px !important;
    border-radius: 4px !important;
    border: none !important;
    transition: all 0.2s ease !important;
    text-align: center !important;
    line-height: 1.2 !important;

    // 按钮颜色变体
    &--primary {
      background-color: #1890ff !important;
      color: white !important;

      &:hover {
        background-color: #40a9ff !important;
      }

      &:active {
        background-color: #096dd9 !important;
      }
    }

    &--success {
      background-color: #52c41a !important;
      color: white !important;

      &:hover {
        background-color: #73d13d !important;
      }

      &:active {
        background-color: #389e0d !important;
      }
    }

    &--warning {
      background-color: #fa8c16 !important;
      color: white !important;

      &:hover {
        background-color: #ffa940 !important;
      }

      &:active {
        background-color: #d46b08 !important;
      }
    }

    &--danger {
      background-color: #ff4d4f !important;
      color: white !important;

      &:hover {
        background-color: #ff7875 !important;
      }

      &:active {
        background-color: #d9363e !important;
      }
    }
  }

  // 当前场景信息
  &__current {
    margin-bottom: 8px;
  }

  &__current-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  &__reload-button {
    font-size: 10px !important;
    padding: 2px 6px !important;
    height: 20px !important;
    line-height: 16px !important;
  }

  &__current-scenario {
    display: block;
    font-size: 12px;
    color: #1890ff;
    font-weight: bold;
    margin-bottom: 4px;
  }

  &__current-description {
    display: block;
    font-size: 11px;
    color: #ccc;
    line-height: 1.4;
  }

  // 帮助信息
  &__help {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 8px;
    margin-top: 8px;
  }

  &__help-text {
    display: block;
    font-size: 10px;
    color: #999;
    margin-bottom: 2px;
    font-family: 'Courier New', monospace;
    word-break: break-all;
  }

  // 响应式设计
  @media (max-width: 480px) {
    max-width: 280px;
    min-width: 200px;

    &__button-grid {
      grid-template-columns: 1fr;
    }

    &__button {
      font-size: 9px !important;
      padding: 4px 6px !important;
      min-height: 24px !important;
    }
  }

  // 暗色主题变体
  &--dark {
    background: rgba(20, 20, 20, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  // 亮色主题变体
  &--light {
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    border: 1px solid rgba(0, 0, 0, 0.1);

    .debug-panel__header {
      border-bottom-color: rgba(0, 0, 0, 0.1);

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }

    .debug-panel__section-title {
      color: #666;
    }

    .debug-panel__status-label {
      color: #999;
    }

    .debug-panel__status-value {
      color: #333;
    }

    .debug-panel__current-description {
      color: #666;
    }

    .debug-panel__help {
      border-top-color: rgba(0, 0, 0, 0.1);
    }

    .debug-panel__help-text {
      color: #999;
    }
  }

  // 动画效果
  &__content {
    animation: debugPanelSlideIn 0.3s ease-out;
  }
}

@keyframes debugPanelSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
