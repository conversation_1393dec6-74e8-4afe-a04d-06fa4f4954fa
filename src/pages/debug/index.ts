/**
 * 通用调试器主入口
 * 导出所有调试相关的工具函数、组件和类型定义
 */

// 导出工具函数
export {
  BaseDebugScenario,
  isDebugMode,
  getDebugScenario,
  createDebugLogger,
  debugLog,
  createMockApiResponse,
  mockApiResponse,
  getScenarioDisplayName,
  generateMockData,
  isValidDebugScenario,
  // 配置工具
  mergeDebugConfigs,
  createDebugConfig,
  DebugConfigBuilder,
  // 调试器创建器
  createDebugger,
  createSimpleDebugger
} from './utils';

// 导出类型定义
export type {
  DebugScenario,
  DebugConfig,
  ScenarioConfig,
  YkDebugger
} from './utils';

// 导出调试面板组件
export { default as DebugPanel } from './DebugPanel';
export type {
  DebugPanelProps,
  DebugButton
} from './DebugPanel';
