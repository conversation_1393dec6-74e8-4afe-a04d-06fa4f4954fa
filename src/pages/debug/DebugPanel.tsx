/**
 * 通用调试器面板组件
 * 提供可复用的调试界面，支持场景切换、状态显示、模拟数据加载等功能
 * 用于开发阶段的页面调试和测试
 */

import React, { useState } from 'react';
import { View, Text } from '@tarojs/components';
import { Button } from '@arco-design/mobile-react';
import { 
  DebugScenario, 
  DebugConfig, 
  BaseDebugScenario,
  getScenarioDisplayName,
  createDebugLogger
} from './utils';
import './DebugPanel.less';

// 调试面板属性接口
export interface DebugPanelProps {
  scenario: DebugScenario;
  config: DebugConfig;
  onLoadMockData: (scenario: DebugScenario) => void;
  statusInfo?: Record<string, any>;        // 状态信息显示
  customButtons?: DebugButton[];           // 自定义按钮
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'; // 面板位置
  className?: string;                      // 自定义样式类名
}

// 调试按钮接口
export interface DebugButton {
  label: string;
  scenario: DebugScenario;
  icon?: string;
  color?: 'primary' | 'success' | 'warning' | 'danger';
}

// 默认调试按钮配置
const getDefaultButtons = (config: DebugConfig): DebugButton[] => {
  const buttons: DebugButton[] = [];
  
  // 添加基础场景按钮
  buttons.push(
    { label: '🔄 基础调试', scenario: BaseDebugScenario.BASIC },
    { label: '📡 API调试', scenario: BaseDebugScenario.API_CALLS },
    { label: '🎭 模拟数据', scenario: BaseDebugScenario.MOCK_DATA },
    { label: '❌ 错误处理', scenario: BaseDebugScenario.ERROR_HANDLING },
    { label: '⏳ 加载状态', scenario: BaseDebugScenario.LOADING_STATE },
    { label: '📭 空状态', scenario: BaseDebugScenario.EMPTY_STATE }
  );

  // 添加自定义场景按钮
  Object.keys(config.scenarios).forEach(scenarioKey => {
    const scenarioConfig = config.scenarios[scenarioKey];
    buttons.push({
      label: scenarioConfig.icon ? `${scenarioConfig.icon} ${scenarioConfig.displayName || scenarioKey}` : scenarioKey,
      scenario: scenarioKey,
      color: scenarioConfig.color || 'primary'
    });
  });

  return buttons;
};

const DebugPanel: React.FC<DebugPanelProps> = ({
  scenario,
  config,
  onLoadMockData,
  statusInfo = {},
  customButtons,
  position = 'bottom-right',
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const logger = createDebugLogger(config);

  // 获取按钮列表
  const buttons = customButtons || getDefaultButtons(config);

  // 处理按钮点击
  const handleButtonClick = (buttonScenario: DebugScenario) => {
    logger(`[basic] 切换调试场景: ${buttonScenario}`);
    onLoadMockData(buttonScenario);
  };

  // 重新加载当前组件
  const handleReloadComponent = () => {
    logger(`[basic] 重新加载当前组件: ${scenario}`);
    onLoadMockData(scenario);
  };

  // 获取位置样式类名
  const getPositionClassName = () => {
    switch (position) {
      case 'bottom-left':
        return 'debug-panel--bottom-left';
      case 'top-right':
        return 'debug-panel--top-right';
      case 'top-left':
        return 'debug-panel--top-left';
      default:
        return 'debug-panel--bottom-right';
    }
  };

  // 获取按钮颜色样式
  const getButtonColorClass = (color?: string) => {
    switch (color) {
      case 'success':
        return 'debug-panel__button--success';
      case 'warning':
        return 'debug-panel__button--warning';
      case 'danger':
        return 'debug-panel__button--danger';
      default:
        return 'debug-panel__button--primary';
    }
  };

  return (
    <View className={`debug-panel ${getPositionClassName()} ${className}`}>
      {/* 调试面板头部 */}
      <View
        className="debug-panel__header"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <Text className="debug-panel__title">
          🐛 {config.moduleName}: {getScenarioDisplayName(scenario, config)}
        </Text>
        <Text className="debug-panel__toggle">
          {isExpanded ? '▼' : '▶'}
        </Text>
      </View>

      {/* 展开的调试内容 */}
      {isExpanded && (
        <View className="debug-panel__content">
          {/* 状态信息显示 */}
          {Object.keys(statusInfo).length > 0 && (
            <View className="debug-panel__status">
              <Text className="debug-panel__section-title">状态信息</Text>
              {Object.entries(statusInfo).map(([key, value]) => (
                <View key={key} className="debug-panel__status-item">
                  <Text className="debug-panel__status-label">{key}:</Text>
                  <Text className="debug-panel__status-value">
                    {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                  </Text>
                </View>
              ))}
            </View>
          )}

          {/* 调试按钮区域 */}
          <View className="debug-panel__buttons">
            <Text className="debug-panel__section-title">调试场景</Text>
            <View className="debug-panel__button-grid">
              {buttons.map((button, index) => (
                <Button
                  key={index}
                  size="mini"
                  className={`debug-panel__button ${getButtonColorClass(button.color)}`}
                  onClick={() => handleButtonClick(button.scenario)}
                >
                  {button.label}
                </Button>
              ))}
            </View>
          </View>

          {/* 当前场景信息 */}
          <View className="debug-panel__current">
            <View className="debug-panel__current-header">
              <Text className="debug-panel__section-title">当前场景</Text>
            </View>
            <Text className="debug-panel__current-scenario">
              {getScenarioDisplayName(scenario, config)}
            </Text>
            {config.scenarios[scenario] && config.scenarios[scenario].description && (
              <Text className="debug-panel__current-description">
                {config.scenarios[scenario].description}
              </Text>
            )}
            <Button
              size="mini"
              type="primary"
              className="debug-panel__reload-button"
              onClick={handleReloadComponent}
            >
              🔄 重新加载
            </Button>
          </View>

          {/* 使用说明 */}
          <View className="debug-panel__help">
            <Text className="debug-panel__section-title">使用说明</Text>
            <Text className="debug-panel__help-text">
              URL参数: ?debug={scenario !== BaseDebugScenario.BASIC ? scenario : ''}
            </Text>
            <Text className="debug-panel__help-text">
              模块: {config.moduleName}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

export default DebugPanel;
