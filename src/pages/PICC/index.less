@import "@arco-design/mobile-react/style/mixin.less";
.module {
  width: 100%;
  box-sizing: border-box;
  padding: 10px;

  &-title {
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    padding-left: 16px;

    &-text {
      font-size: 13px;
      color: #999999;
    }
  }

  &-content {
    width: 100%;
    box-sizing: border-box;
    border-radius: 10px;
    overflow: hidden;
  }
}

.aboutUsPageContent {
  width: 100%;
  height: 100%;

  &-title {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 50px 27px 27px 27px;

    &-name {
      font-size: 20px;
      font-weight: bold;
      color: #1e1e1e;
      .use-dark-mode-query({
        color: #ffffff;
      });
    }

    &-desc {
      margin-top: 12px;
      font-size: 15px;
      font-weight: normal;
      color: #0E0E0E;
      .use-dark-mode-query({
        color: #dddddd;
      });
    }
  }
}
