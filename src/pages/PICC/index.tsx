import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Cell, Image } from "@arco-design/mobile-react";
import React from "react";
import Taro from "@tarojs/taro";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

export default function aboutUs() {
  const [isIos, setIsIos] = React.useState(false);

  useLoad(() => {});

  return (
    <View className="aboutUsPageContent">
      <YkNavBar title="" />
      <View className="aboutUsPageContent-title">
        <View className="aboutUsPageContent-title-name">个人信息收集清单</View>
        <View className="aboutUsPageContent-title-desc">
          你可以查阅APP名称对你的个人信息的收集情况。
        </View>
      </View>
      <View className="module">
        <View className="module-title">
          <Text className="module-title-text">用户基本信息</Text>
        </View>
        <View className="module-content">
          <Cell.Group bordered={false}>
            {/* 头像 */}
            <Cell
              label="头像"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: "/pages/PICCAvatar/index",
                });
              }}
            ></Cell>
            {/* 昵称 */}
            <Cell
              label="昵称"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: "/pages/PICCNickName/index",
                });
              }}
            ></Cell>
            {/* 手机号 */}
            <Cell
              label="手机号"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: "/pages/PICCPhone/index",
                });
              }}
            ></Cell>
          </Cell.Group>
        </View>
      </View>
    </View>
  );
}
