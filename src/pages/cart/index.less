.cart-page {
  background: #f7f7f7;
  min-height: 100vh;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
}

.cart-header {
  display: flex;
  align-items: center;
  height: 48px;
  background: #fff;
  font-size: 18px;
  font-weight: 500;
  border-bottom: 1px solid #eee;
  padding: 0 16px;
  position: sticky;
  top: 0;
  z-index: 10;
}
.cart-back-icon {
  font-size: 22px;
  margin-right: 16px;
  color: #333;
}
.cart-title {
  flex: 1;
  text-align: center;
  color: #222;
}

.cart-list {
  margin-bottom: 80px;
}

.cart-shop {
  background: #fff;
  margin-top: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding-bottom: 8px;
}
.cart-shop-header {
  display: flex;
  align-items: center;
  padding: 12px 12px 0 12px;
  min-height: 36px;
}
.cart-checkbox {
  width: 20px !important;
  height: 20px !important;
  margin-right: 8px;
  vertical-align: middle;
}
.cart-shop-name {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  line-height: 28px;
  margin-right: 6px;
  display: flex;
  align-items: center;
}
.cart-shop-tag {
  width: 18px;
  height: 18px;
  margin-left: 4px;
  vertical-align: middle;
}

.cart-shop-products {
  padding: 0 12px;
}
.cart-product-card {
  background: #f8fafd;
  border-radius: 10px;
  margin-top: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  padding: 8px;
  border: 1px solid #f0f0f0;
}
.cart-product-main {
  display: flex;
  align-items: flex-start;
  padding: 12px 12px 0 12px;
}
.cart-product-img {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 12px;
  border: 1px solid #eee;
}
.cart-product-info {
  flex: 1;
  min-width: 0;
}
.cart-product-title {
  font-size: 14px;
  color: #222;
  font-weight: 500;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  line-height: 20px;
  max-height: 40px;
}
.cart-product-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 2px;
}
.cart-product-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 2px;
  margin-top: 4px;
}
.cart-product-size {
  font-size: 14px;
  color: #222;
  font-weight: 500;
  line-height: 22px;
}
.cart-product-price {
  color: #ff3b30;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.cart-product-count {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.count-btn {
  width: 24px !important;
  height: 24px !important;
  line-height: 24px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  color: #333;
  font-size: 18px;
  background: #fafafa;
  cursor: pointer;
  user-select: none;
  min-width: 24px;
  padding: 0;
}
.count-num {
  width: 32px;
  text-align: center;
  font-size: 15px;
  color: #222;
  margin: 0 6px;
}
.cart-product-remark-row {
  padding: 6px 12px 0 0;
}
.cart-product-remark {
    font-size: 12px;
    color: #aaa;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    line-height: 18px;
    max-height: 18px;

}
.cart-product-extra-row {
//   padding: 4px 12px 0 52px;
}
.cart-product-send {
  font-size: 15px;
  color: #1677ff !important;
  border-radius: 4px !important;
  padding: 2px 16px !important;
  background: #f8fafd !important;
  cursor: pointer;
  min-width: 48px;
  height: 28px !important;
  line-height: 24px !important;
  font-weight: 500;
}

.cart-footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.04);
  display: flex;
  align-items: flex-start;
  height: 72px;
  padding: 0 12px;
  z-index: 10;
}
.cart-footer-left {
  display: flex;
  align-items: center;
  flex: none;
  margin-top: 16px;
}
.cart-footer-all {
  margin-left: 8px;
  font-size: 15px;
  color: #222;
}
.cart-footer-manage {
  margin-left: 10px;
  font-size: 15px;
  color: #1677ff;
  cursor: pointer;
  font-weight: 500;
}
.cart-footer-info {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  margin-left: 30px;
  flex: 1;
  margin-top: 10px;
}
.cart-footer-total {
  font-size: 15px;
  color: #222;
  font-weight: 500;
  line-height: 22px;
  text-align: right;
}
.cart-footer-total-price {
  color: #ff3b30;
  font-size: 15px;
  font-weight: 600;
  margin-left: 2px;
}
.cart-footer-other {
  font-size: 13px;
  color: #888;
  line-height: 20px;
  text-align: right;
}
.cart-footer-other-price {
  color: #ff3b30;
  font-size: 13px;
  margin-left: 2px;
}
.cart-footer-buy {

  background: #93bfff !important;
  color: #fff !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 0 16px !important;
  height: 40px !important;
  font-size: 15px !important;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(22,119,255,0.08);
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.cart-footer-btn-wrap {
  display: flex;
  align-items: center;
  padding-right: 20px;
  margin-left: 10px;
}


.arco-swipe-action-menu-right.arco-swipe-action-menu.action-close{
    position: relative ;
    //right: 110px;
    //margin-left: 25px;

}
.arco-swipe-action-menu-right.arco-swipe-action-menu.action-open{

    
}

.arco-swipe-action-menu-action-info-container::after{
    height: 0;
}