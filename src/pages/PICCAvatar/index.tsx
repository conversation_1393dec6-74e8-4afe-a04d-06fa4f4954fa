import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Image } from "@arco-design/mobile-react";
import React from "react";
import Taro from "@tarojs/taro";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

export default function aboutUs() {
  const [userInfo, setUserInfo] = React.useState<any>(false);

  useLoad(() => {
    setUserInfo(Taro.getStorageSync("userInfo").userInfo);
  });

  React.useEffect(() => {
    console.log(userInfo);
  }, [userInfo]);

  return (
    <View className="PICCPageContent">
      <YkNavBar title="" />
      <View className="PICCPageContent-title">
        <View className="PICCPageContent-title-name">「头像」收集情况</View>
      </View>
      <View className="PICCModule">
        <View className="PICCModule-item">
          <View className="PICCModule-item-label">内容</View>
          <View className="PICCModule-item-content">
            <Image
              fit="cover"
              // showLoading={false}
              // showError={false}
              src={userInfo.head_img}
              className="userInfoImage"
            />
          </View>
        </View>
        <View className="PICCModule-item">
          <View className="PICCModule-item-label">目的</View>
          <View className="PICCModule-item-content">
            <Text className="PICCModule-item-content-text">
              用于完成APP名称注册
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}
