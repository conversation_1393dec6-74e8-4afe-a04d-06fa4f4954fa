@import "@arco-design/mobile-react/style/mixin.less";

.PICCPageContent {
  width: 100%;
  height: 100%;

  &-title {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 50px 27px 27px 27px;
    .use-var(color, font-color);
    .use-dark-mode-query({
      color: @dark-font-color;
    });

    &-name {
      font-size: 20px;
      font-weight: bold;
    }

    &-desc {
      margin-top: 12px;
      font-size: 15px;
      font-weight: normal;
    }
  }
}

.PICCModule {
  width: calc(100% - 60px);
  box-sizing: border-box;
  border-top: 1px solid rgba(#999999, 0.2);
  margin: 0 30px;

  &-item {
    width: 100%;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(#999999, 0.2);
    padding: 21px 0 18px 0;

    &-label {
      width: 100%;
      margin-bottom: 16px;
      font-size: 16px;
      .use-var(color, font-color);
      .use-dark-mode-query({
      color: @dark-font-color;
    });
    }

    &-content {
      width: 100%;
      font-size: 12px;
      .use-var(color, sub-font-color);
      .use-dark-mode-query({
      color: @dark-sub-font-color;
    });
    }
  }

  .userInfoImage {
    width: 57px;
    height: 57px;
  }
}
