import { useState, useRef, useEffect } from "react";
import { View, Text } from "@tarojs/components";
import { Cell, Switch, Dialog } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import PageSwipeBack from "@/components/PageSwipeBack";
import { useSetPermission } from "@/stores/permissionStore";
import {
  AuthTypes,
  AuthStatusAndroid,
  AuthStatusIos,
} from "@/utils/config/authTypes";
import PermissionPopup from "@/components/PermissionPopup";
// import * as Icons from '@arco-design/mobile-react/esm/icon';
import * as Icons from "@arco-iconbox/react-yk-arco";
import "./index.less";
export default function PermissionPage() {
  const [isPermissionPopupVisible, setPermissionPopupVisible] = useState(false); // 控制权限弹窗的显示状态
  const popupTitle = useRef("");
  const popupText = useRef("");
  const permissionHint = useRef(""); // 权限弹窗的说明
  const platformRef = useRef("H5");
  const authConfirmType = useRef(-1);
  const allPermissions = useRef([]);
  const clickItem = useRef({});
  const setPermission = useSetPermission();
  const isRequestPermission = useRef(true);
  const [permissionsState, setPermissionsState] = useState([]); // 用于存储并渲染权限数据
  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;

    window.checkPermissionCallBack = checkPermissionCallBack;
    isRequestPermission.current = true;
    if (isAndroid) {
      platformRef.current = "Android";
      window.webPermissonDeny = webPermissonDeny;
      window.webPermissonConsent = webPermissonConsent;

      window.checkPermission.checkPermission();
    } else if (isIos) {
      platformRef.current = "IOS";

      window.webkit.messageHandlers.checkPermission.postMessage("");
    } else {
      platformRef.current = "H5";
    }

    return () => {
      delete window.checkPermissionCallBack;
      delete window.webPermissonDeny;
      delete window.webPermissonConsent;
    };
  }, []);

  // 定义权限提示信息的映射
  const permissionMapping = {
    [AuthTypes.CAMERA]: {
      hint: "在下方弹窗中选择允许后，你可以拍摄照片或视频以发送给朋友、使用视频通话等功能。你还可以在其他场景中访问摄像头进行拍摄照片和视频。",
    },
    [AuthTypes.GALLERY_PHOTO]: {
      hint: "在下方弹窗中选择允许后，你可以从手机相册中选择图片发送给朋友。",
    },
    [AuthTypes.GALLERY_VIDEO]: {
      hint: "在下方弹窗中选择允许后，你可以从手机相册中选择视频发送给朋友。",
    },
    [AuthTypes.GALLERY_AUDIO]: {
      hint: "在下方弹窗中选择允许后，你可以从手机相册中选择音频发送给朋友。",
    },
    [AuthTypes.STORAGE]: {
      hint: "在下方弹窗中选择允许后，你可以保存图片到相册。你还可以在其他场景访问设备里的照片视频和文件，以及保存内容到设备。",
    },
    [AuthTypes.AUDIO]: {
      hint: "录音权限说明",
    },
    [AuthTypes.NOTICE]: {
      hint: "通知权限说明",
    },
    [AuthTypes.LOCATION]: {
      hint: "定位权限说明",
    },
    [AuthTypes.CONTACT]: {
      hint: "通讯录权限说明",
    },
    [AuthTypes.FLOATWIN]: {},
    [AuthTypes.ACCESS]: {},
    [AuthTypes.AUTOSTART]: {},
  };

  // 权限回调处理
  const checkPermissionCallBack = async (e) => {
    console.log(JSON.stringify(e));
    // 更新权限状态
    allPermissions.current = await setPermission(e); // 确保更新后赋值
    setPermissionsState([...allPermissions.current]); // 更新状态
    const currentAuth = authConfirmType.current;
    const permissionConfig = permissionMapping[currentAuth];
    if (isRequestPermission.current) {
      isRequestPermission.current = false;
      if (
        !permissionConfig &&
        currentAuth !== AuthTypes.FLOATWIN &&
        currentAuth !== AuthTypes.ACCESS &&
        currentAuth !== AuthTypes.AUTOSTART
      ) {
        console.error("Invalid authConfirmType:", currentAuth);
        return;
      }

      if (!hasPermission(currentAuth)) {
        // 设置权限提示信息（如果存在）
        if (permissionConfig.hint) {
          permissionHint.current = permissionConfig.hint;
          // 显示权限弹窗

          setPermissionPopupVisible(true);
        }
      }

      if (platformRef.current === "Android") {
        window.requestPermission.requestPermission(currentAuth + "");
      } else {
        if (hasPermission(currentAuth)) {
          webPermissonConsent();
        } else {
          webPermissonDeny();
        }
      }
    }
  };

  const hasPermission = (authType) => {
    const permission = allPermissions.current.find(
      (perm) => perm.authType === authType
    );
    return permission ? permission.state : false; // 如果找到了对应权限，返回权限状态；如果找不到，返回 false
  };

  const webPermissonDeny = () => {
    isRequestPermission.current = false;
    handlePermissionClose();
    // openSetting();
    // openSetPopup(clickItem.current);
  };

  const webPermissonConsent = () => {
    handlePermissionClose();
    if (platformRef.current === "Android") {
      window.checkPermission.checkPermission();
    } else {
      window.webkit.messageHandlers.checkPermission.postMessage("");
    }
  };

  // 关闭权限说明弹窗
  const handlePermissionClose = () => {
    setPermissionPopupVisible(false);
  };

  const handlePermissionConfirm = (type) => {
    if (platformRef.current === "Android") {
      window.requestPermission.requestPermission(type);
    } else {
      window.webkit.messageHandlers.requestPermission.postMessage(type);
    }
  };

  const openCloseSetPopup = (item) => {
    clickItem.current = item;
    authConfirmType.current = item.authType;
    popupTitle.current = `${item.name}权限`;
    if (platformRef.current === "Android") {
      popupText.current = `你可以前往「设置＞应用＞${APP_NAME_CN}＞权限中」关闭${item.name}权限。`;
    } else {
      popupText.current = `你可以前往「设置＞${APP_NAME_CN}」关闭${item.name}权限。`;
    }
    showDialog();
  };

  const openSetPopup = (item) => {
    clickItem.current = item;
    authConfirmType.current = item.authType;
    console.log(authConfirmType.current);
    if (
      item.authType === AuthTypes.FLOATWIN ||
      item.authType === AuthTypes.AUTOSTART ||
      item.authType === AuthTypes.ACCESS
    ) {
      popupTitle.current = `${item.name}权限`;
      if (platformRef.current === "Android") {
        popupText.current = `无法使用${item.name}，前往「设置＞应用＞${APP_NAME_CN}＞权限中」打开${item.name}权限。」`;
      } else {
        popupText.current = `无法使用${item.name}，前往「设置＞${APP_NAME_CN}」打开${item.name}权限。」`;
      }
      showDialog();
    } else {
      if (item.currentStatus === AuthStatusAndroid.DENIEDANDNOTASK) {
        openSetting();
      } else {
        requestPermissionWeb(authConfirmType.current);
      }
    }
  };

  const showDialog = () => {
    Dialog.confirm({
      title: popupTitle.current ? popupTitle.current : "",
      children: popupText.current ? popupText.current : "",
      okText: "前往设置",
      cancelText: "取消",
      onOk: () => {
        handleSettingConfirm();
      },
      platform: platformRef.current === "Android" ? "android" : "ios",
    });
  };

  // 打开设置
  const openSetting = () => {
    if (platformRef.current === "Android") {
      window.openSetting.openSetting();
    } else {
      window.webkit.messageHandlers.openSetting.postMessage("");
    }
  };

  const handleSettingConfirm = () => {
    console.log(clickItem.current.authType);
    if (platformRef.current === "Android") {
      if (
        clickItem.current.authType === AuthTypes.FLOATWIN ||
        clickItem.current.authType === AuthTypes.AUTOSTART ||
        clickItem.current.authType === AuthTypes.ACCESS
      ) {
        requestPermissionWeb(clickItem.current.authType);
      } else {
        openSetting();
      }
    } else {
      window.webkit.messageHandlers.openSetting.postMessage("");
    }
  };

  // 请求权限
  const requestPermissionWeb = (type) => {
    isRequestPermission.current = true;
    authConfirmType.current = type;
    if (platformRef.current === "Android") {
      window.checkPermission.checkPermission();
    } else {
      window.webkit.messageHandlers.requestPermission.postMessage(`${type}`);
    }
  };

  const getIcon = (iconName) => {
    const IconComponent = Icons[iconName]; // 从 Icons 对象中获取对应图标组件
    return IconComponent ? <IconComponent className="primary-icon" /> : null; // 确保存在对应图标
  };

  return (
    <PageSwipeBack leftDrag={true}>
      <View className="permission">
        <YkNavBar title="系统权限管理" />

        {/* 权限管理列表 */}
        <View className="cell-container">
          <Cell.Group className="cell-group" bordered={false}>
            {allPermissions.current.map((item) => (
              <Cell
                className="cell-label"
                label={item.name}
                icon={
                  getIcon(item.imgPath)
                  // <img
                  //   src={require(`../../assets/images/permission/${item.imgPath}`)}
                  // />
                }
              >
                <Switch
                  platform={
                    platformRef.current === "Android" ? "android" : "ios"
                  }
                  checked={item.state}
                  onChange={(checked) => {}}
                  onClick={() => {
                    if (item.state) {
                      openCloseSetPopup(item);
                    } else {
                      openSetPopup(item);
                    }
                  }}
                />
              </Cell>
            ))}
          </Cell.Group>

          {/* 前往系统设置 */}
          <Text className="setting-text" onClick={() => openSetting()}>
            前往系统设置
          </Text>
        </View>

        <PermissionPopup
          visible={isPermissionPopupVisible}
          type={authConfirmType.current}
          hintText={permissionHint.current}
          onClose={handlePermissionClose}
          onConfirm={handlePermissionConfirm}
        />
      </View>
    </PageSwipeBack>
  );
}
