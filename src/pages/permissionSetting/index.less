@import "@arco-design/mobile-react/style/mixin.less";

.permission {
  width: 100vw;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
        background-color: @dark-background-color;
    });
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;



//   .primary-icon [fill] {
//     fill: var(--dark-primary-color);
//     .use-dark-mode-query({
//         fill: var(--dark-primary-color);
//     });
// }
// .primary-icon [stroke] {
//     stroke: var(--primary-color);
//     .use-dark-mode-query({
//         stroke: var(--dark-primary-color);
//     });
// }

  .cell-container {
    .use-var(background-color, background-color);
    .use-dark-mode-query({
        background-color: @dark-background-color;
    });
    border: none;
    box-sizing: border-box;
    width: 100%;
    display: flex;
    flex-direction: column;

    .cell-group {
      .rem(margin-top, 10);
    }

    .cell-title {
      background-color: transparent;
      .rem(font-size, 15);
      .use-var(color, font-color);
      .use-dark-mode-query({
			  color: @dark-font-color;
			});
    }

    .cell-text {
      .rem(font-size, 15);
      .use-var(color, sub-font-color);
      .use-dark-mode-query({
			  color: @dark-sub-font-color;
			});
    }

    .setting-text {
      .rem(margin-top, 33);
      text-align: center;
      .rem(font-size, 13);
      .use-var(color, font-color);
      .use-dark-mode-query({
				  color: @dark-font-color;
				});
    }
  }
}
