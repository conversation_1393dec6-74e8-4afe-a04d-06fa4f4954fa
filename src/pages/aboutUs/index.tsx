import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import {
  Cell,
  Image
} from "@arco-design/mobile-react";
import React from "react";
import Taro from "@tarojs/taro";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

export default function aboutUs() {

  const [isIos,setIsIos] = React.useState(false);

  const getInLink = (id:string,name:string) => {
    Taro.navigateTo({
      url: `/pages/webView/index?id=${id}&name=${name}`,
    });
  }

  // 给我们评分
  const scoreforUs = () => {
    let _window:any = window;
    _window.webkit.messageHandlers.storeReview.postMessage('');
  }

  useLoad(() => {
    let uaAll = window.navigator.userAgent;
    let ios = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    setIsIos(ios);
  });

  return (
    <View className="aboutUsPageContent">
      <YkNavBar title="关于我们" />

      <View className="aboutUsTop">
        <Image
          className="aboutUsTop-logo"
          src={require("@/assets/images/login/logo.png")}
          bottomOverlap={null}
        />
        <Text className="aboutUsTop-name">东东相册</Text>
      </View>
      <View className="module">
        <View className="module-content">
          <Cell.Group bordered={false}>
            {/* 关于我们 */}
            <Cell
              label="关于我们"
              className="module-content-cell"
              text=""
              showArrow
              onClick={()=>getInLink('121','关于我们')}
            ></Cell>
            {/* 给我们评分 */}
           {isIos && <Cell
              label="给我们评分"
              className="module-content-cell"
              text=""
              showArrow
              onClick={scoreforUs}
            ></Cell>}
            {/* 用户协议 */}
            <Cell
              label="用户协议"
              className="module-content-cell"
              text=""
              showArrow
              onClick={()=>getInLink('123','用户协议')}
            ></Cell>
            {/* 隐私政策 */}
            <Cell
              label="隐私政策"
              className="module-content-cell"
              text=""
              showArrow
              onClick={()=>getInLink('126','隐私政策')}
            ></Cell>
          </Cell.Group>
        </View>
      </View>
    </View>
  );
}
