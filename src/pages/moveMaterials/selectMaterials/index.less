@import '@arco-design/mobile-react/style/mixin.less';

[id^="/pages/catalog/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;  
  });
}


.user-header {

    .use-dark-mode-query({
        background-color: @dark-background-color;     //黑色背景
    });
    
    // .user-header-bar {
    //   display: flex;
    //   align-items: center;
    //   justify-content: space-between;
    //   height: 48px;
    //   padding: 0 16px;
    //   position: relative;
    //   .user-header-back {
    //     font-size: 22px;
    //     color: #222;
    //     cursor: pointer;
    //     .use-dark-mode-query({
    //         color: var(--dark-font-color);    //白色字体
    //       });
    //   }
    //   .user-header-action {
    //     color: #8c8c8c;
    //     font-size: 15px;
    //     .iconfont {
    //       font-size: 18px;
    //       margin-right: 2px;
    //     }
    //   }
    // }
    .user-header-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 8px;
      .user-header-avatar-wrap {
        position: relative;
        .user-header-avatar {
          width: 64px;
          height: 64px;
          border-radius: 50%;
          object-fit: cover;
          background: #f7f8fa;
        }
        .user-header-qrcode {
          position: absolute;
          right: -10px;
          bottom: 0;
          width: 20px;
          height: 20px;
        //   background: url('二维码icon地址') no-repeat center/cover;
        }
      }
      .user-header-nick {
        font-size: 15px;
        font-weight: 500;
        color: #222;
        margin-top: 8px;
        max-width: 200px;
        text-align: center;
        // white-space: nowrap;
        // overflow: hidden;
        // text-overflow: ellipsis;
        .use-dark-mode-query({
            color: var(--dark-font-color);    //白色字体
          });
      }
      .user-header-desc {
        font-size: 14px;
        color: #8c8c8c;
        margin-top: 4px;
        max-width: 320px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .user-header-stats {
        display: flex;
        justify-content: center;
        margin-top: 12px;
        .user-header-stat {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 32px;
          .user-header-stat-num {
            font-size: 20px;
            color: #222;
            font-weight: 500;
            .use-dark-mode-query({
                color: var(--dark-font-color);    //白色字体
              });
          }
          .user-header-stat-label {
            font-size: 13px;
            color: #8c8c8c;
            margin-top: 2px;
          }
        }
      }
    }
    .user-header-tabs {
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-top: 16px;
      margin-bottom: 3px;
    //   border-bottom: 2px solid #f0f0f0;
      .user-header-tab {
        font-size: 16px;
        color: #222;
        padding: 8px 0;
        position: relative;
        cursor: pointer;
        .use-dark-mode-query({
            color: var(--dark-font-color);    //白色字体
        });
        &.active {
          color: #165DFF;
          font-weight: 500;
          &::after {
            content: '';
            display: block;
            height: 2px;
            background: #165DFF;
            border-radius: 2px;
            position: absolute;
            left: 0; right: 0; bottom: -2px;
          }
        }
      }
    }
    .user-header-searchbar {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      background: #fff;
    //   border-bottom: 1px solid #f0f0f0;
      .use-dark-mode-query({
        background-color: @dark-background-color;     //黑色背景
      });
    //   .user-header-search {
    //     display: flex;
    //     align-items: center;
    //     flex: 1;
    //     height: 32px;
    //     line-height: 32px;
    //     background: #f7f8fa;
    //     border-radius: 8px;
    //     border: none;
    //     font-size: 14px;
    //     color: #222;
    //     padding: 0 12px;
    //     box-sizing: border-box;
    //     .use-dark-mode-query({
    //         background-color: var(--dark-container-background-color);   //灰色背景
    //     });
    //     .use-dark-mode-query({
    //         color: var(--dark-font-color);    //白色字体
    //     });
          
    //   }

    }
  }


  .user-header-filter {
    color: #165DFF;
    font-size: 15px;
    margin-left: 8px;
    cursor: pointer;
  }
  .move-list {
    //height: 550px;
    flex: 1;
    background: #fff;
    .use-dark-mode-query({
        background-color: @dark-background-color;    //黑色背景
    });
  }
  .move-group-title {
    
    font-size: 16px;
    color: #222;
    font-weight: bold;
    margin: 16px 0 8px 16px;
    .use-dark-mode-query({
        color: var(--dark-font-color);    //白色字体
    });
  }
  .move-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 16px 0 16px;
    // border-bottom: 1px solid #f0f0f0;
    background: #fff;
    .use-dark-mode-query({
        background-color: @dark-background-color;   //黑色背景
    });
  }
  .move-checkbox {
    margin-right: 12px;
    margin-top: 6px;
  }
  .move-img {

    width: 60px;
    height: 60px;
    border-radius: 6px;
    object-fit: cover;
    background: #f7f8fa;
    margin-right: 12px;
  }
  .move-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }
  .move-title-text {
    width: 200px;
    font-size: 15px;
    color: #222;
    margin-bottom: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    .use-dark-mode-query({
        color: var(--dark-font-color);    //白色字体
    });
    
  }
  .move-price {
    color: #F53F3F;
    font-size: 15px;
    margin-bottom: 4px;
  }
  .move-attrs {
    font-size: 13px;
    color: #8c8c8c;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    .move-attrs-arrow {
      font-size: 12px;
      margin-left: 2px;
      color: #c0c0c0;
    }
  }
  .move-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px 16px 16px;
    background: #fff;
    height: 40px;
    .use-dark-mode-query({
        background-color: @dark-background-color;     //黑色背景
    });
    
    margin-top: 20px;
  }
  .move-footer-checkbox {
    font-size: 15px;
    color: #222;
    width: 100px;
  }
  .move-footer-btn {
    flex: 1;
    height: 44px;
    line-height: 44px;
    text-align: center;
    background: #165DFF;
    color: #c0c0c0;
    border-radius: 8px;
    font-size: 16px;
    margin-left: 12px;
    border: none;
    padding: 0;


  }

.attr-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.45);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.attr-modal {
  background: #fff;
  border-radius: 18px;
  min-width: 320px;
  max-width: 90vw;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  padding: 20px 20px 18px 20px;
  .use-dark-mode-query({
    background: @dark-container-background-color;
  });
}
.attr-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.attr-title {
  font-size: 17px;
  font-weight: 500;
  color: #222;
  .use-dark-mode-query({ color: var(--dark-font-color); });
}
.attr-close {
  font-size: 22px;
  color: #b0b0b0;
  cursor: pointer;
  font-weight: 400;
}
.attr-content {
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.attr-row {
  display: flex;
  align-items: flex-start;
}
.attr-label {
  font-size: 15px;
  color: #8c8c8c;
  min-width: 48px;
  margin-top: 4px;
}
.attr-values {

  width: 215px;

}
.attr-value {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 4px 16px;
  font-size: 12px;
  color: #222;
  //margin-bottom: 6px;
  cursor: pointer;
  transition: all 0.2s;
  .use-dark-mode-query({
    background: @dark-background-color;
    color: var(--dark-font-color);
  });
}
.attr-value.selected {
  background: #165DFF;
  color: #fff;
  .use-dark-mode-query({
    background: #165DFF;
    color: #fff;
  });
}

.card-attr-content {
  background: #f7f8fa;
  border-radius: 14px;
  //margin: 12px 0 0 0;
  padding: 16px 0px 10px 18px;
  margin-right: 5px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  .use-dark-mode-query({
    background: @dark-container-background-color;
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
  });
}
.card-attr-content .attr-row + .attr-row {
  border-top: 1px solid #ececec;
  padding-top: 12px;
  .use-dark-mode-query({
    border-top: 1px solid #222;
  });
}
.card-attr-content .attr-label {
  font-size: 15px;
  color: #222;
  font-weight: 600;
  min-width: 48px;
  margin-top: 4px;
  .use-dark-mode-query({ color: var(--dark-font-color); });
}
.card-attr-content .attr-values {
  display: flex;
  flex-wrap: wrap;
  gap: 0;
}
.card-attr-content .attr-value {
  font-size: 12px;
  color: #444;
  background: none;
  border: none;
  padding: 0 18px 0 0;
  margin-bottom: 6px;
  position: relative;
  cursor: pointer;
  transition: none;
  .use-dark-mode-query({ color: var(--dark-font-color); });
}
.card-attr-content .attr-value:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 18px;
  background: #e5e6eb;
  .use-dark-mode-query({ background: #333; });
}
.card-attr-content .attr-value.selected {
  background: #165DFF;
  color: #fff;
  border: 1px solid #165DFF;
  .use-dark-mode-query({
    background: #165DFF;
    color: #fff;
    border: 1px solid #165DFF;
  });
}

.move-attrs-wrap{
  margin-top: -5px;
  margin-left: 70px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  background: #fff;
  .use-dark-mode-query({
    background: @dark-background-color;
  });
}

.empty-image {
  width: 83px;
  height: 82px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #86909C;
}

.content-scroll-view{
   // height: 570px
}

/* 清除采集记录弹窗样式 */
.confirm-dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7); /* Semi-transparent dark background */
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-dialog {
  background: #fff; /* White background for the dialog */
  border-radius: 12px; /* Rounded corners */
  width: 75%; /* Adjust width as needed */
  max-width: 300px; /* Max width to keep it centered and readable */
  padding: 24px 0px 0px 0px; /* Padding inside the dialog */
  display: flex;
  flex-direction: column;
  align-items: center;
  .use-dark-mode-query({
    background: @dark-container-background-color; /* Dark mode background */
  });
}

.confirm-dialog-title {
  font-size: 18px;
  font-weight: bold;
  color: #222; /* Dark text for title */
  margin-bottom: 8px;
  .use-dark-mode-query({
    color: var(--dark-font-color); /* White text for title in dark mode */
  });
}

.confirm-dialog-content {
  font-size: 14px;
  color: #666; /* Slightly lighter text for content */
  text-align: center;
  padding: 0 24px 0 24px;
  margin-bottom: 24px;
  line-height: 1.6;
  .use-dark-mode-query({
    color: #ccc; /* Lighter gray text for content in dark mode */
  });
}

.confirm-dialog-buttons {
   display: flex;
   width: 100%;
  // background:#fff;
   border-top: 1px solid #eee; /* Separator line above buttons */
   .use-dark-mode-query({
     border-top: 1px solid #333; /* Dark mode separator */
   });
}

.confirm-dialog-button {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 16px;
  cursor: pointer;
  
  &.cancel {
    color: #165DFF; /* Blue color for cancel button */
    border-right: 1px solid #eee; /* Separator between buttons */
    .use-dark-mode-query({
      border-right: 1px solid #333; /* Dark mode separator */
    });
  }
  
  &.clear {
    color: #165DFF; /* Blue color for clear button */
  }
}

.yk-navbar-back{
  cursor: pointer;
}

