import { View, Text, Image, Button, Input, ScrollView } from "@tarojs/components";
import React, { useState, useEffect, useRef  } from "react";
import Taro, { useDidShow,  useReady } from "@tarojs/taro";
import { Checkbox, SearchBar, ShowMonitor, Toast } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import { getCollectDynamicList, getLinkId } from "@/utils/api/common/common_user";
import "./index.less";


// 定义 dynamic 类型
interface DynamicItem {
  id: number;
  timeStamp: number;
  title: string;
  imgsSrc: string[];
  optimaPrice: string;
  attrs: string;
  createTime: string;
  skus?: string[];  // 添加可选的 skus 属性
  color?: string[]; // 添加可选的 color 属性
  digitalWatermark?: string | number; // 将 digitalWatermark 的类型更改为 string | number
}

interface DynamicData {
  merchantAvatar: string;
  merchantName: string;
  newCount: number;
  totalCount: number;
  time: string;
  items: DynamicItem[];
}

// 定义 tab 类型
type TabType = 'all' | 'new' | 'video' | 'image';

export default function selectMaterials() {
  const scrollViewRef = useRef(null);
  const [selectedMap, setSelectedMap] = useState<Map<number | string, DynamicItem>>(new Map());
  const [search, setSearch] = useState("");
  const [activate, setActivate] = useState("all");
  const [timeStamp, setTimeStamp] = useState("");
  const [linkId, setlinkId] = useState("");
  const [expandedId, setExpandedId] = useState<string | number | null>(null);
  const [currentTab, setCurrentTab] = useState<TabType>('all');  // 添加当前 tab 状态
  const [dynamic, setDynamic] = useState<DynamicData>({
    merchantAvatar: '',
    merchantName: '',
    newCount: 0,
    totalCount: 0,
    time: '',
    items: []
  });
  //const [value, setValue] = React.useState([2, '22328490198975000']);
  const selectNum = 10    //最多能选择的商品数量
  const [newFilteredData, setNewFilteredData]= useState<any[]>([]); 
  const [ids, setIds]= useState<any[]>([]); 
  // 获取所有商品对象
  const allItems = dynamic?.items || [];
  const selectedIds = Array.from(selectedMap.keys());

  const allSelected = selectedIds.length === selectNum && allItems.length > 0;
  

  const getLinkIdInfo = async () => {
    const params = Taro.getCurrentInstance().router?.params;
    if (!params?.id) return;    
    const data = {
      id: params.id,
    }
    
    const res: any = await getLinkId(data);
    if (res.code === 0 && res.data) {
      if (res.data && res.data) {
        // 确保所有ID都是字符串类型
        // const selectedIds = res.data.split(',').map(id => String(id));
        // const initialMap = new Map<number | string, DynamicItem>();
        // selectedIds.forEach(id => {
        //   initialMap.set(id, {} as DynamicItem);
        // });
        // setSelectedMap(initialMap);
        setIds(res.data.split(','))
      }
    }
  }

  const getDynamicListData = async () => {
    const params = Taro.getCurrentInstance().router?.params;
    if (!params?.id) return;
    
    Taro.showLoading({
      title: '加载中...',
      mask: true
    });

    const data = {
      id: params.id,
      timeStamp: timeStamp,
      type: 2,
      url: ''
    } as any;

    try {
      const res: any = await getCollectDynamicList(data);
      let time;
      Taro.hideLoading();
      if (res.code === 0 && res.data) {
        setlinkId(params.id);
        const itemsWithSkus = res.data.items?.map(item => {
          time = item.timeStamp;
          return ({
            ...item,
            color: [],
            digitalWatermark: String(item.digitalWatermark) // 确保 digitalWatermark 是字符串
          });
        }) || [];

        const newItems = [...dynamic.items, ...itemsWithSkus];
        setDynamic({
          ...res.data,
          items: newItems
        });
        setTimeStamp(time);

        // 更新选中状态
        const currentSelectedIds = Array.from(selectedMap.keys());
        console.log('currentSelectedIds before update:', currentSelectedIds);
        if (currentSelectedIds.length > 0) {
          const newSelectedMap = new Map<number | string, DynamicItem>();
          currentSelectedIds.forEach(id => {
            const item = newItems.find(item => String(item.digitalWatermark) === String(id));
            if (item) {
              newSelectedMap.set(String(id), item);
              console.log('Found and set item for id:', id, item);
            } else {
              console.log('Item not found for id:', id);
            }
          });
          console.log('newSelectedMap size:', newSelectedMap.size);
          setSelectedMap(newSelectedMap);
        }

        // 处理分组数据
        const grouped = {};
        newItems.forEach(item => {
          const time = item.time;
          if (!grouped[time]) {
            grouped[time] = [];
          }
          grouped[time].push(item);
        });

        const newFilteredData_s = Object.keys(grouped).map(time => ({
          //group: `${time.replace('/', '月')}日`,
          group: time,
          items: grouped[time].map(item => ({
            ...item,
            digitalWatermark: item.digitalWatermark,
          }))
        }));    
        setNewFilteredData(newFilteredData_s);
      }
    } catch (error) {
      Taro.hideLoading();
      Taro.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });
      console.error('获取动态列表失败:', error);
    }
  }

  const rollOver =  (event) => {

 
      if (scrollViewRef.current) {
        if( event.detail.scrollTop==0 ){
          scrollViewRef.current.style.height = "330px"; // 直接修改 DOM  
        }else{
          scrollViewRef.current.style.height = "530px"; // 直接修改 DOM
        }
      }
    const footer = document.getElementsByClassName("move-footer")[0]; // 假设有一个底部元素
    footer && footer.scrollIntoView({
        behavior: "smooth"
    });
  }

  useDidShow(() => {

  // 直接瞬间跳转到顶部（无动画）
  
    //setSelectedMap(new Map());  //清空全选
    //setIds([]);           //清空已ID  
  });

  useEffect(() => {

    getLinkIdInfo();
    getDynamicListData();

  }, []);


  // 修改 handleChange 处理函数
  const handleChange = (selectid: (number | string)[]) => {
    
    const filteredArrayIds = selectid.filter(item => !ids.includes(item));
    // 如果当前选中的数量已经达到10条，且用户尝试选择新的项
    if (selectedIds.length >= selectNum && filteredArrayIds.length > selectedIds.length) {
      Taro.showToast({
        title: `最多只能选择${selectNum}条`,
        icon: 'none',
        duration: 2000
      });
      return;
    }
    console.log('selectedIds before update:', filteredArrayIds);
    const newMap = new Map<number | string, DynamicItem>();
    filteredArrayIds.forEach(id => {
      const item = allItems.find(item => String(item.digitalWatermark) === String(id));
      if (item) {
        newMap.set(id, item);
      }
    });
    setSelectedMap(newMap);
  };

  // 获取选中的商品对象数组
  const getSelectedItems = () => Array.from(selectedMap.values());

  // 根据 tab 类型过滤数据
  const getFilteredDataByTab = (items: DynamicItem[]) => {
    switch (currentTab) {
      case 'new':
        // 上新 tab 显示所有商品
        return items;
      case 'video':
        // 假设视频类型的商品标题包含"视频"或"小视频"
        return items.filter(item => 
          item.title.includes('视频') || item.title.includes('小视频')
        );
      case 'image':
        // 假设图集类型的商品有多张图片
        return items.filter(item => item.imgsSrc.length > 1);
      default:
        return items;
    }
  };

  // 组合搜索和 tab 过滤
  const filteredData = getFilteredDataByTab(dynamic?.items || [])
    .filter(item => item.title.toLowerCase().includes(search.toLowerCase()));

  // 处理 tab 切换
  const handleTabChange = (tab: TabType) => {

    setCurrentTab(tab);
    // 切换 tab 时清空选中状态
    //setSelectedMap(new Map());
  };

  const loadingData = () => {

    const footer = document.getElementsByClassName("move-footer")[0]; // 假设有一个底部元素
    footer && footer.scrollIntoView({
        behavior: "smooth"
    });
    getDynamicListData();


  };



  // 商品属性弹窗选中逻辑
  const [attrSelected, setAttrSelected] = useState({});

  // 新增状态控制弹窗显示
  const [showClearConfirm, setShowClearConfirm] = useState(false);

  // 渲染上新编辑页面 - 使用和商品列表一样的界面
  const renderNewEditPage = () => {
    // 直接使用 dynamic.items，不进行过滤
    const newItems = dynamic?.items || [];
    
    return (
      <View className="move-list">
        <Checkbox.Group 
          value={[...ids, ...selectedIds]} 
          onChange={handleChange}
          disabled={true}
        >
          {newFilteredData.map(group => (
            <View key={group.group} style={{marginTop: '10px'}}>
              <Text className="move-group-title">{group.group}</Text>
              {group.items.map(item => {
                const itemId = String(item.digitalWatermark);
                console.log('Rendering item with id:', itemId, 'selected:', selectedMap.has(itemId));
                return (
                  <View key={itemId}>
                    <View className="move-item">
                      {item.digitalWatermark !== undefined && (
                        <Checkbox 
                          value={itemId}
                          className="move-checkbox" 
                          shape="circle"
                          disabled={ids.includes(String(item.digitalWatermark))}
                        >
                          <View></View>
                        </Checkbox>
                      )}
                      <Image className="move-img" src={item.imgsSrc[0]} />
                      <View className="move-info">
                        <Text className="move-title-text">{item.title}</Text>
                        {item.optimaPrice && <Text className="move-price">{item.optimaPrice}</Text>}
                      </View>
                    </View>
                    <View className="move-attrs-wrap">
                      {((item.skus && item.skus.length > 0) || (item.color && item.color.length > 0)) && (
                        <>
                          <Text
                            className="move-attrs"
                            onClick={() => 
                              setExpandedId(item.digitalWatermark === undefined 
                                ? null 
                                : (expandedId === item.digitalWatermark ? null : item.digitalWatermark)
                              )
                            }
                          >
                            商品属性
                            {expandedId === item.digitalWatermark ? 
                              <Text className="move-attrs-arrow">▼</Text> : 
                              <Text className="move-attrs-arrow">▶</Text>
                            }
                          </Text>
                          {expandedId === item.digitalWatermark && (
                            <View className="attr-content card-attr-content">
                              {item.skus && item.skus.length ?
                              (<View className="attr-row" key="规格">
                                <Text className="attr-label">规格：</Text>
                                <View className="attr-values">
                                  {item.skus.map(val => (
                                    <Text
                                      className={`attr-value${attrSelected['规格'] === val ? ' ' : ''}`}
                                      key={val}
                                    >
                                      {val}
                                    </Text>
                                  ))}
                                </View>
                              </View>):(<></>)
                              }

                              {item.color && item.color.length ?
                                (<View className="attr-row" key="颜色">
                                  <Text className="attr-label">颜色：</Text>
                                  <View className="attr-values">
                                    {item.color.map(val => (
                                      <Text
                                        className={`attr-value${attrSelected['颜色'] === val ? ' ' : ''}`}
                                        key={val}
                                      >
                                        {val}
                                      </Text>
                                    ))}
                                  </View>
                                </View>):(<></>)
                              }  
                            </View>
                          )}
                        </>
                      )}
                    </View>
                  </View>
                );
              })}
            </View>
          ))}
        </Checkbox.Group>
      </View>
    );
  };

  // 渲染商品列表
  const renderProductList = () => {
    return (
      <View className="move-list" >
        <Checkbox.Group 
          value={[...ids, ...selectedIds]} 
          onChange={handleChange}
        >
 
          {filteredData.map(item => (
            <View key={item.digitalWatermark}>
              <View className="move-item">
                <Checkbox 
                  disabled={ids.includes(String(item.digitalWatermark))}
                  value={String(item.digitalWatermark)} 
                  className="move-checkbox" 
                  shape="circle"
                >
                  <View></View>
                </Checkbox>
                <Image className="move-img" src={item.imgsSrc[0]} />
                <View className="move-info">
                  <Text className="move-title-text">{item.title}</Text>
                  {item.optimaPrice && <Text className="move-price">{item.optimaPrice}</Text>}
                </View>
              </View>
              <View className="move-attrs-wrap">
                {((item.skus && item.skus.length > 0) || (item.color && item.color.length > 0)) && (
                  <>
                    <Text
                      className="move-attrs"
                      onClick={() => 
                        setExpandedId(item.digitalWatermark === undefined 
                          ? null 
                          : (expandedId === item.digitalWatermark ? null : item.digitalWatermark)
                        )
                      }
                    >
                      商品属性
                      {expandedId === item.digitalWatermark ? 
                        <Text className="move-attrs-arrow">▼</Text> : 
                        <Text className="move-attrs-arrow">▶</Text>
                      }
                    </Text>
                    {expandedId === item.digitalWatermark && (
                      <View className="attr-content card-attr-content">
                        {item.skus && item.skus.length ?
                        (<View className="attr-row" key="规格">
                          <Text className="attr-label">规格：</Text>
                          <View className="attr-values">
                            {item.skus.map(val => (
                              <Text
                                className={`attr-value${attrSelected['规格'] === val ? ' ' : ''}`}
                                key={val}
                              >
                                {val}
                              </Text>
                            ))}
                          </View>
                        </View>):(<></>)
                        }

                        {item.color && item.color.length ?
                          (<View className="attr-row" key="颜色">
                            <Text className="attr-label">颜色：</Text>
                            <View className="attr-values">
                              {item.color.map(val => (
                                <Text
                                  className={`attr-value${attrSelected['颜色'] === val ? ' ' : ''}`}
                                  key={val}
                                >
                                  {val}
                                </Text>
                              ))}
                            </View>
                          </View>):(<></>)
                        }  

                      </View>
                    )}
                  </>
                )}
              </View>
            </View>
          ))}
        </Checkbox.Group>
      </View>
    );
  };

  return (
    <View >
      <YkNavBar title="采集内容" 
      rightContent={<Text onClick={() => setShowClearConfirm(true)} className="yk-navbar-back">清除采集记录</Text>} 
      />
      {dynamic.items.length > 0 ? (
      <View className="move-content-page" >
        <View className="user-header">
          {/* 头像昵称简介 */}
          <View className="user-header-info">
            
            <View className="user-header-avatar-wrap">
              <Image className="user-header-avatar" src={dynamic.merchantAvatar} />
              <View className="user-header-qrcode" />
            </View>
            <Text className="user-header-nick">{dynamic.merchantName}</Text>
            <Text className="user-header-desc">
              {/* {dynamic.merchantName} - 优质商品，品质保证... */}
            </Text>
            <View className="user-header-stats">
              <View className="user-header-stat">
                <Text className="user-header-stat-num">{dynamic.newCount}</Text>
                <Text className="user-header-stat-label">上新</Text>
              </View>
              <View className="user-header-stat">
                <Text className="user-header-stat-num">{dynamic.totalCount}</Text>
                <Text className="user-header-stat-label">总数</Text>
              </View>
            </View>
          </View>
          {/* tab栏 */}
          <View className="user-header-tabs">
            <View 
              className={`user-header-tab ${currentTab === 'all' ? 'active' : ''}`}
              onClick={() => {handleTabChange('all');console.log(selectedIds,'all');}}
            >
              全部
            </View>
            <View 
              className={`user-header-tab ${currentTab === 'new' ? 'active' : ''}`}
              onClick={() => handleTabChange('new')}
            >
              上新
            </View>
            <View 
              className={`user-header-tab ${currentTab === 'video' ? 'active' : ''}`}
              onClick={() => handleTabChange('video')}
            >
              小视频
            </View>
            <View 
              className={`user-header-tab ${currentTab === 'image' ? 'active' : ''}`}
              onClick={() => handleTabChange('image')}
            >
              图集
            </View>
          </View>
          {/* 搜索栏 */}
          {currentTab !== 'new' && (
            <View>
              <SearchBar 
                actionButton={<span className="user-header-filter">搜索</span>} 
                placeholder="搜索"
                onChange={(e) => setSearch(e.target.value)}
                className="demo-input-btn-input"
                clearable
                onClear={() => setSearch('')}
              />  
            </View>
          )}
        </View>
        <ScrollView 
            ref={scrollViewRef}
            onScroll={rollOver}
            className="content-scroll-view"
            style={{height: '320px'}}
            scrollY
            onScrollToLower={loadingData}
        >

        {currentTab === 'new' ? renderNewEditPage() : renderProductList()}
        </ScrollView>
    
        {/* 底部操作栏 - 只在非上新页面显示 */}
        {currentTab !== 'new1' && (
          <View className="move-footer">
            <Checkbox
              value="all"
              checked={allSelected}
              //disabled={selectedIds.length > 0}  // 如果有选中的项，则禁用全选
              onChange={() => {
                const new_allItems = allItems.filter(item => !ids.includes(item.digitalWatermark));
                if (allSelected) {
                  setSelectedMap(new Map());  // 清空选中状态
                } else {
                  // 如果总数超过10条，只选择前10条
                  if (new_allItems.length > selectNum) {
                    Taro.showToast({
                      title: `最多只能选择${selectNum}条`,
                      icon: 'none',
                      duration: 2000
                    });
                    const newMap = new Map<number | string, DynamicItem>();
                    new_allItems.slice(0, selectNum).forEach((item,key) => {
                        if (item.digitalWatermark !== undefined) {
                            newMap.set(item.digitalWatermark, item);
                        }
                    });
                    setSelectedMap(newMap);
                  } else {
                    
                    const newMap = new Map<number | string, DynamicItem>();
                    new_allItems.forEach(item => {
                        if (item.digitalWatermark !== undefined) {
                            newMap.set(item.digitalWatermark, item);
                        }
                    });
                    setSelectedMap(newMap);
                  }
                }
              }}
              className="move-footer-checkbox"
              shape="circle"
            >
              全选
            </Checkbox>
            <Button 
              className={`move-footer-btn ${selectedIds.length === 0 ? 'disabled' : ''}`}
              disabled={selectedIds.length === 0}
              onClick={() => {
                const selectedItems = getSelectedItems();
                Taro.setStorageSync("selectedDynamicItems", selectedItems);
                console.log(selectedItems, '-----------selected items');
                if(selectedIds.length>0){
                  Taro.navigateTo({
                    url: '/pages/moveMaterials/selectMaterials/edit?linkId='+linkId
                  });
                }else{
                  Taro.showToast({
                    title: `线选择商品在进行搬家`,
                    icon: 'none',
                    duration: 2000
                  }); 
                }
                
              }}
            >
              开始搬家({selectedIds.length}/{selectNum})
            </Button>
          </View>
        )}
      </View>
      ):(
        <View className="empty-state">
          <Image 
            className="empty-image" 
            src="https://img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" 
          />
          <Text className="empty-text">暂无数据</Text>
        </View>
      )}
      
      {/* 清除采集记录确认弹窗 */}
      {showClearConfirm && (
        <View className="confirm-dialog-mask">
          <View className="confirm-dialog">
            <Text className="confirm-dialog-title">清除采集记录</Text>
            <Text className="confirm-dialog-content">
              清除后不会永久删除采集记录,下次进入素材搬家页面依然可以展示过往的采集记录。
            </Text>
            <View className="confirm-dialog-buttons">
              <Text className="confirm-dialog-button cancel" onClick={() => setShowClearConfirm(false)}>取消</Text>
              <Text className="confirm-dialog-button clear" onClick={() => {
                // TODO: Add logic to clear records
                setSelectedMap(new Map());
                setShowClearConfirm(false);
                Taro.showToast({
                  title: '清除成功',
                  icon: 'none',
                  duration: 2000    
                });
              }}>清除</Text>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}

