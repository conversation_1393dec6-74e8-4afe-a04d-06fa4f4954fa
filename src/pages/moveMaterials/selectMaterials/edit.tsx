import { View, Text, Image, Button } from "@tarojs/components";
import React, { useState } from "react";
import Taro, { useDidShow } from "@tarojs/taro";
import { Input, Textarea } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import { moveHouseDynamic } from "@/utils/api/common/common_user";
import "./edit.less";
import { link } from "fs";



// 定义 tab 类型

export default function selectMaterialsEdit() {
  const [expandedId, setExpandedId] = useState<number | null>(null);
  const [dynamic, setDynamic] = useState<any[]>([]); 
  const userInfo = Taro.getStorageSync('userInfo');

  
  const getDynamicListData = async () => {
    const data=Taro.getStorageSync("selectedDynamicItems")
    const itemsWithSkus = data.map(item => ({
        ...item,
        color: [] 
      })) || [];
      setDynamic(itemsWithSkus);
  }


  const moveHouse = async (dynamic) => {      
    const updatedDynamic = dynamic.map(item => ({
      ...item,       
      userId: userInfo.id,    
      linkId: Taro.getCurrentInstance().router?.params?.linkId || '',
    }));
    const data={
      content: JSON.stringify(updatedDynamic),
    }
    console.log(data)
    let res: any = await moveHouseDynamic(data);
    if (res.code == 0) {
      Taro.setStorageSync("selectedDynamicItems", []);
      Taro.showToast({ title: '搬家完成', icon: 'success' });
      setTimeout(() => {
        Taro.navigateBack({
          delta:1
        });
      }, 1000)
    }else{
      Taro.showToast({ title: '搬家失败', icon: 'error' });
    }
  }

  useDidShow(() => {
    getDynamicListData();
  });





  // 新增状态控制描述编辑弹窗
  const [showDescriptionEditDialog, setShowDescriptionEditDialog] = useState(false);
  const [editingItemId, setEditingItemId] = useState<number | string | null>(null);
  const [editingItemDescription, setEditingItemDescription] = useState('');

  // 处理描述修改
  const handleDescriptionChange = (itemId: number | string | null, newDescription: string) => {
    const updatedDynamic = dynamic.map(item => {
      if (item.digitalWatermark === itemId) {
        return { ...item, title: newDescription };
      }
      return item;
    });
    setDynamic(updatedDynamic);
    Taro.setStorageSync("selectedDynamicItems", updatedDynamic);
  };

  // 渲染商品列表
  const renderProductList = () => {
    return (
      <View className="move-list" >
          {dynamic.map(item => (
            <View className="move-item-wrap" key={item.digitalWatermark}> 
                    <View className="move-item-s">
                        <Image className="move-img" src={item.imgsSrc[0]} />
                        <View className="move-info">
                            <View className="move-title">
                                <Text className="move-title-text-s" >{item.title}</Text>
                                
                            </View>
                            <Text className="move-title-edit" style={{width:'50px',marginLeft: '200px',marginTop: '10px'}}
                                 onClick={() => {
                                   setEditingItemId(item.digitalWatermark);
                                   setEditingItemDescription(item.title);
                                   setShowDescriptionEditDialog(true);
                                 }}
                                 >编辑</Text>
                            <View style={{marginTop: '-20px',width: '50px'}}>
                                <Text style={{float: 'left'}} className="move-price" >售价</Text>
                                <Input
                            
                                style={{width: '150px',marginTop: '-13px'}}
                                placeholder={item.optimaPrice  || 0} 
                                type="number"
                                border="none"
                                onChange={(val) => {
                                  const updatedDynamic = dynamic.map(dItem => {
                                    if (dItem.digitalWatermark === item.digitalWatermark) {
                                      return { ...dItem, optimaPrice: val.target.value };
                                    }
                                    return dItem;
                                  });
                                  setDynamic(updatedDynamic);
                                  Taro.setStorageSync("selectedDynamicItems",updatedDynamic)
                                }}
                                />
                              </View>
                        </View>
                    </View>
                    <View className="move-attrs-wrap-s">
                        {(item.skus.length > 0 || item.color.length > 0) ? (
                        <>
                            <Text
                            className="move-attrs"
                            onClick={() => setExpandedId(expandedId === item.digitalWatermark ? null : item.digitalWatermark)}
                            >
                            商品属性
                            {expandedId === item.digitalWatermark ? 
                                <Text className="move-attrs-arrow">▼</Text> : 
                                <Text className="move-attrs-arrow">▶</Text>
                            }
                            </Text>
                            {expandedId === item.digitalWatermark && (
                            <View className="attr-content card-attr-content">
                                {item.skus.length ?
                                (<View className="attr-row" key="规格">
                                <Text className="attr-label">规格：</Text>
                                <View className="attr-values">
                                    {item.skus.map(val => (
                                    <Text
                                        className='attr-value'
                                        key={val}
                                    >
                                        {val}
                                    </Text>
                                    ))}
                                </View>
                                </View>):(<></>)
                                }
                                {item.color.length ?
                                (<View className="attr-row" key="颜色">
                                    <Text className="attr-label">颜色：</Text>
                                    <View className="attr-values">
                                    {item.color.map(val => (
                                        <Text
                                        className='attr-value'
                                        key={val}
                                        >
                                        {val}
                                        </Text>
                                    ))}
                                    </View>
                                </View>):(<></>)
                                }  
                            </View>
                            )}
                        </>
                        ):(<Text className="move-attrs"></Text>)
                        }
                    </View>
            </View>
          ))}
      </View>
    );
  };

  return (
    <View >
      <YkNavBar title="商品设置"   />
      <View className="move-content-page">
        {renderProductList()}
        <View className="move-footer">
            <Button 
              className="move-footer-btn"
              onClick={() => {
                moveHouse(dynamic);
              }}
            >
              开始搬家({Taro.getStorageSync("selectedDynamicItems").length})
            </Button>
          </View>
      </View>

      {/* 修改描述弹窗 */}
      {showDescriptionEditDialog  && (
        <View className="description-edit-mask">
          <View className="description-edit-dialog">
            <View className="description-edit-header">
              <Text className="description-edit-title">修改描述</Text>
              <Text className="description-edit-close" onClick={() => setShowDescriptionEditDialog(false)}>×</Text>
            </View>
            <View className="description-edit-content">
              <Textarea
            
                className="description-edit-textarea"
                value={editingItemDescription}
                onChange={(val) => setEditingItemDescription(val.target.value)}
                placeholder="请输入描述"
                border="none"
                autosize
              />
            </View>
            <View className="description-edit-footer">
              <Button className="description-edit-confirm-button" onClick={() => {

                handleDescriptionChange(editingItemId, editingItemDescription);
                setShowDescriptionEditDialog(false);
                
              }}>确认修改</Button>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}

