@import '@arco-design/mobile-react/style/mixin.less';

[id^="/pages/catalog/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;  
  });
}

.collect-link-page {
    min-height: 100vh;
    background: #fff;
    display: flex;
    flex-direction: column;
    position: relative;
    .use-dark-mode-query({
      background-color: @dark-background-color;     //黑色背景
    });
  }
  .collect-link-navbar {
    
    display: flex;
    align-items: center;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    .use-dark-mode-query({
      background-color: @dark-background-color;     //黑色背景
    });

  }
  .collect-link-back {
    font-size: 22px;
    color: #222;
    margin-left: 12px;
    cursor: pointer;
  }
  .collect-link-input-wrap {
    //padding: 16px;
    background: #fff;
    .use-dark-mode-query({
      background-color: @dark-background-color;     //黑色背景
    });
  }
  .collect-link-input {
    width: 78%;
    height: 40px;
    display: flex;
    align-items: center;
    background: #f7f8fa;
    border-radius: 8px;
    border: none;
    font-size: 15px;
    color: #222;
    padding: 0 12px;
    box-sizing: border-box;
    float: left;
    .use-dark-mode-query({
      background: @dark-container-background-color;
      color: var(--dark-font-color);
    });


  }
  .collect-link-list {
    margin-top: 8px;
    .use-dark-mode-query({
      background-color: @dark-background-color;     //黑色背景
    });
  }
  .collect-link-item {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background 0.2s;
    &.selected {
      background: #f5faff;
    }
    .use-dark-mode-query({
      background-color: @dark-background-color;     //黑色背景
    });
  }
  .collect-link-icon {
    font-size: 20px;
    color: #165DFF;
    margin-right: 10px;
    margin-top: 2px;
  }
  .collect-link-info {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .collect-link-title {
    font-size: 16px;
    color: #222;
    margin-bottom: 2px;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }
  .collect-link-url {
    font-size: 13px;
    color: #8c8c8c;
    word-break: break-all;
  }
  .collect-link-more {
    font-size: 22px;
    color: #c9cdd4;
    margin-left: 8px;
  }
  .collect-link-tip {
    color: #bfc3c9;
    font-size: 14px;
    text-align: center;
    margin: 32px 0 0 0;
    display: block;
  }
  .collect-link-btn {
    width: 20%;
    
    height: 40px;
    background: #165DFF;
    color: #fff;
    border-radius: 8px;
    font-size: 15px;
    border: none;
    padding: 0;
    display: block;
    //position: fixed;
    //left: 4vw;
    //bottom: 24px;
    //opacity: 0.7;
    cursor: pointer;
    &.enabled,
    &:not([disabled]) {
      opacity: 1;
    }
  }



  .collect-link-action-mask {
    position: fixed;
    left: 0; right: 0; top: 0; bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 9999;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    


  }
  .collect-link-action-sheet {
    width: 100vw;
    background: #fff;
    border-radius: 16px 16px 0 0;
    overflow: hidden;
    box-shadow: 0 -2px 16px rgba(0,0,0,0.08);
    animation: slideUp 0.2s;
    .use-dark-mode-query({
      background: @dark-container-background-color;
      color: var(--dark-font-color);
    });
  }
  @keyframes slideUp {
    from { transform: translateY(100%);}
    to { transform: translateY(0);}
  }
  .collect-link-action-btn {
    height: 56px;
    line-height: 56px;
    text-align: center;
    font-size: 18px;
    color: #222;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
    cursor: pointer;
    .use-dark-mode-query({
      background: @dark-container-background-color;
      color: var(--dark-font-color);
    });
    &:last-child {
      border-bottom: none;
    }
    &.danger {
      color: #F53F3F;
    }
    &.cancel {
      margin-top: 8px;
      color: #222;
      border-radius: 0 0 16px 16px;
      .use-dark-mode-query({
        background: @dark-container-background-color;
        color: var(--dark-font-color);
      });  
    }
  }

  .collect-link-confirm-mask {
    position: fixed;
    left: 0; right: 0; top: 0; bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;

    
  }
  .collect-link-confirm-modal {
    background: #fff;
    border-radius: 12px;
    width: 80vw;
    max-width: 320px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.10);
    padding: 24px 0 0 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    .use-dark-mode-query({
      background: @dark-container-background-color;
      color: var(--dark-font-color);
    }); 
  }
  .collect-link-confirm-title {
    font-size: 18px;
    font-weight: bold;
    color: #222;
    text-align: center;
    margin-bottom: 12px;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
    
  }
  .collect-link-confirm-content {
    font-size: 15px;
    color: #666;
    text-align: center;
    margin-bottom: 18px;
    line-height: 1.5;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }
  .collect-link-confirm-actions {
    display: flex;
    width: 100%;
    border-top: 1px solid #f0f0f0;
  }
  .collect-link-confirm-cancel {
    flex: 1;
    text-align: center;
    padding: 16px 0;
    color: #165DFF;
    font-size: 17px;
    border-right: 1px solid #f0f0f0;
    cursor: pointer;
  }
  .collect-link-confirm-delete {
    flex: 1;
    text-align: center;
    padding: 16px 0;
    color: #165DFF;
    font-size: 17px;
    cursor: pointer;
  }

.user-header-avatar{
  width: 32px;
  height: 32px;
  border-radius: 12px;
  margin-right: 12px;
  margin-top: 8px;
}

.user-header-send {
  color: #165DFF;
  font-size: 15px;
  margin-left: 8px;
  cursor: pointer;
}