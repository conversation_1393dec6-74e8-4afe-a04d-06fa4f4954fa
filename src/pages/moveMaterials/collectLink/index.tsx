import { View, Text, Input, Button, Image } from "@tarojs/components";
import Taro, {useDidShow} from "@tarojs/taro";
import React, { useState, useEffect } from "react";
import { getCollectLinkList, createCollectLink, delCollectLink  } from "@/utils/api/common/common_user";
import {  SearchBar } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import "./index.less";
import { config } from "process";


export default function CollectLink() {
  const [input, setInput] = useState("");
  //const [links, setLinks] = useState<any[]>([]);  // 明确定义类型
  const [links, setLinks] = useState<any[]>([]);  // 明确定义类型
  const [selected, setSelected] = useState<number | null>(null);
  const [showAction, setShowAction] = useState(false); // 控制操作弹窗显示
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false); // 控制删除确认弹窗显示
  const userInfo = Taro.getStorageSync('userInfo');
  const handleConfirm = async () => {
    if (input === '') {
      Taro.showToast({ title: '请输入链接', icon: 'none' });
      return;
    }
    let res: any = await createCollectLink({userId: userInfo.id,url: input}); //创建链接
    if (res.code == 0) {
        
        //getCollectLink();
        // setLinks(prevLinks => {
        //     const newLinks = Array.isArray(prevLinks) ? [...prevLinks, data] : [data];
        //     Taro.setStorageSync("collectLinks", newLinks);
        //     return newLinks;
        //   });
        Taro.navigateTo({
          url: '/pages/moveMaterials/selectMaterials/index?id='+res.data
        });
        setInput('');
        
    }
  }

  const delLink = async () => {      
    const data={
      id:selected,
    }
    let res: any = await delCollectLink(data);
    if (res.code == 0) {
          getCollectLink();
    }
  }

  const getCollectLink = async () => {      
    const data={
      pageNo:1,
      pageSize:10,
      userId:userInfo.id
    }
    let res: any = await getCollectLinkList(data);
    if (res.code == 0) {
          // setLinks(prevLinks => {
          //   const newLinks = Array.isArray(prevLinks) ? [...prevLinks, res.data.list] : [res.data.list];
          //   Taro.setStorageSync("collectLinks", newLinks);
          //   return newLinks;
          // });
          setLinks(res.data.list)
          
    }
  }
  useEffect(() => {
      //getCollectLink();
      //setLinks(Taro.getStorageSync("collectLinks") || []);
  }, []);

  useDidShow(() => {
    getCollectLink();
  });

  return (
    <View className="collect-link-page">
      {/* 顶部返回 */}
      <YkNavBar title="采集链接" />
      {/* 输入框 */}
      <View className="collect-link-input-wrap">

        <SearchBar 
                actionButton={<span className="user-header-send"   onClick={() => handleConfirm()}>前往采集</span>} 
                placeholder="请输入微商相册链接" 
                value={input}
                prefix={null} 
                onChange={(e) => {setInput(e.target.value);}}
                className="demo-input-btn-input"
                clearable
                onClear={() => setInput('')}
        />     


      </View>
      {/* 链接列表 */}
      <View className="collect-link-list">
       
        {links.map(link => (
          <View
            className={`collect-link-item${selected === link.id ? ' selected' : ''}`}
            key={link.id}
            //onClick={() => setSelected(link.id)}
          >
            {!link.urlAvatar ? (<Text className="collect-link-icon">🔗</Text>):(<Image className="user-header-avatar" src={link.urlAvatar} />)}
            
            <View className="collect-link-info"
                onClick={() => {
                    Taro.navigateTo({
                        url: '/pages/moveMaterials/selectMaterials/index?id='+link.id
                    });
                }}
            >
              <Text className="collect-link-title">{link.urlName}</Text>
              <Text className="collect-link-url">{link.url}</Text>
            </View>
            <Text className="collect-link-more"
                onClick={e => {
                  e.stopPropagation();
                  setShowAction(true);
                  setSelected(link.id);
                }}
            >...</Text>
          </View>
        ))}
      </View>
      {/* 轻触提示 */}
      <Text className="collect-link-tip">轻触以选择链接</Text>
      {/* 底部按钮 */}
      {/* <Button
        className="collect-link-btn"
        disabled={selected === null}
        onClick={() => {
          // 跳转或采集逻辑
                Taro.navigateTo({
                        url: '/pages/moveMaterials/selectMaterials/index'
                });
        }}
      >
        前往采集
      </Button> */}
      {/* 底部操作弹窗 */}
      {showAction && (
        <View className="collect-link-action-mask" onClick={() => setShowAction(false)}>
          <View className="collect-link-action-sheet" onClick={e => e.stopPropagation()}>
            {/* <View className="collect-link-action-btn">编辑</View> */}
            <View className="collect-link-action-btn danger" 
                onClick={() => {
                    setShowAction(false);
                    setShowDeleteConfirm(true);
                }}
                >删除</View>
            <View className="collect-link-action-btn cancel" onClick={() => setShowAction(false)}>取消</View>
          </View>
        </View>
      )}
      {/* 删除确认弹窗 */}
      {showDeleteConfirm && (
        <View className="collect-link-confirm-mask" onClick={() => setShowDeleteConfirm(false)}>
          <View className="collect-link-confirm-modal" onClick={e => e.stopPropagation()}>
            <View className="collect-link-confirm-title">删除链接</View>
            <View className="collect-link-confirm-content">删除后，数据将不可恢复，确定要删除吗？</View>
            <View className="collect-link-confirm-actions">
              <View className="collect-link-confirm-cancel" onClick={() => setShowDeleteConfirm(false)}>取消</View>
              <View className="collect-link-confirm-delete" onClick={() => {
                //setLinks(links.filter(link => link.id !== selected));
                // setLinks(prevLinks => {
                //     const newLinks = Array.isArray(prevLinks) ? links.filter(link => link.id !== selected) : [];
                //     Taro.setStorageSync("collectLinks", newLinks);
                //     return newLinks;
                //   });    

                delLink();
                Taro.showToast({ title: '删除成功', icon: 'success',duration: 2000 });
                Taro.setStorageSync("collectLinks", links.filter(link => link.id !== selected));
                setSelected(null);
                setShowDeleteConfirm(false);
              }}>删除</View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}