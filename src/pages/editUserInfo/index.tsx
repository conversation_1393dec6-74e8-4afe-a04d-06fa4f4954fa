import { useState, useEffect, useRef } from "react";
import { Icon, View } from "@tarojs/components";
import {
  Avatar,
  Cell,
  NavBar,
  Image,
  Button,
  Dialog,
} from "@arco-design/mobile-react";
import PageSwipeBack from "@/components/PageSwipeBack";
import FullScreenLoading from "../../components/FullSrceenLoading"; // 引入全屏遮罩组件
import "./index.less";
import Taro from "@tarojs/taro";
import {
  IconPicture,
  IconUpload,
  IconUser,
} from "@arco-design/mobile-react/esm/icon";

import {
  getUserInfo,
  uploadHeadImg,
  uploadWxCodeImg,
  uploadBgImg,
  uploadFile,
  editUser
} from "@/utils/api/common/common_user";
import BottomPopup from "@/components/BottomPopup";
import { useSetPermission } from "@/stores/permissionStore";
import { AuthTypes } from "@/utils/config/authTypes";
import PermissionPopup from "@/components/PermissionPopup";
import { createFromIconfontCN } from "@arco-design/mobile-react/icon";

const EditUserInfo = () => {
  const [isPopupVisible, setPopupVisible] = useState(false); // 控制选择图片方式弹窗的显示状态
  const [isPermissionPopupVisible, setPermissionPopupVisible] = useState(false); // 控制权限弹窗的显示状态
  const popupTitle = useRef("");
  const popupText = useRef("");
  const permissionHint = useRef(""); // 权限弹窗的说明
  const [userInfo, setUserInfo] = useState(
    () => Taro.getStorageSync("userInfo").userInfo || {}
  );
  const [loading, setLoading] = useState(false); //  loading 状态
  const platformRef = useRef("H5");
  const authConfirmType = useRef(-1);
  const popupType = useRef(null);
  const allPermissions = useRef([]);
  const setPermission = useSetPermission();
  // const permissionStatus = useHasPermission(authConfirmType);
  // 页面加载时调用 getUserInfo
  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;

    window.checkPermissionCallBack = checkPermissionCallBack;
    if (isAndroid) {
      platformRef.current = "Android";
      window.webPermissonDeny = webPermissonDeny;
      window.webPermissonConsent = webPermissonConsent;
    } else if (isIos) {
      platformRef.current = "IOS";
    } else {
      platformRef.current = "H5";
    }

    Taro.eventCenter.on("refreshUserInfo", fetchUserInfo);
    fetchUserInfo();

    return () => {
      Taro.eventCenter.off("refreshUserInfo", fetchUserInfo);
      delete window.checkPermissionCallBack;
      delete window.webPermissonDeny;
      delete window.webPermissonConsent;
    };
  }, []);

  // 定义权限提示信息的映射
  const permissionMapping = {
    [AuthTypes.CAMERA]: {
      hint: "在下方弹窗中选择允许后，你可以拍摄照片或视频以发送给朋友、使用视频通话等功能。你还可以在其他场景中访问摄像头进行拍摄照片和视频。",
    },
    [AuthTypes.GALLERY_PHOTO]: {
      hint: "在下方弹窗中选择允许后，你可以从手机相册中选择图片发送给朋友。",
    },
    [AuthTypes.GALLERY_VIDEO]: {
      hint: "在下方弹窗中选择允许后，你可以从手机相册中选择视频发送给朋友。",
    },
    [AuthTypes.GALLERY_AUDIO]: {
      hint: "在下方弹窗中选择允许后，你可以从手机相册中选择音频发送给朋友。",
    },
    [AuthTypes.STORAGE]: {
      hint: "在下方弹窗中选择允许后，你可以保存图片到相册。你还可以在其他场景访问设备里的照片视频和文件，以及保存内容到设备。",
    },
    [AuthTypes.AUDIO]: {
      hint: "录音权限说明",
    },
    [AuthTypes.NOTICE]: {
      hint: "通知权限说明",
    },
    [AuthTypes.LOCATION]: {
      hint: "定位权限说明",
    },
    [AuthTypes.CONTACT]: {
      hint: "通讯录权限说明",
    },
    [AuthTypes.FLOATWIN]: {},
    [AuthTypes.ACCESS]: {},
    [AuthTypes.AUTOSTART]: {},
  };

  // 权限回调处理
  const checkPermissionCallBack = async (e) => {
    console.log(JSON.stringify(allPermissions));
    // 更新权限状态
    allPermissions.current = await setPermission(e); // 确保更新后赋值

    const currentAuth = authConfirmType.current;
    const permissionConfig = permissionMapping[currentAuth];
    if (!permissionConfig) {
      console.error("Invalid authConfirmType:", currentAuth);
      return;
    }

    if (!hasPermission(currentAuth)) {
      // 设置权限提示信息（如果存在）
      if (permissionConfig.hint) {
        permissionHint.current = permissionConfig.hint;
      }

      // 显示权限弹窗
      setPermissionPopupVisible(true);
    }

    if (platformRef.current === "Android") {
      window.requestPermission.requestPermission(currentAuth);
    } else {
      if (hasPermission(currentAuth)) {
        webPermissonConsent();
      } else {
        webPermissonDeny();
      }
    }
  };

  const hasPermission = (authType) => {
    const permission = allPermissions.current.find(
      (perm) => perm.authType === authType
    );
    return permission ? permission.state : false; // 如果找到了对应权限，返回权限状态；如果找不到，返回 false
  };

  const webPermissonDeny = () => {
    handlePermissionClose();
    openSetPopup();
  };

  const webPermissonConsent = () => {
    handlePermissionClose();
    if (authConfirmType.current === AuthTypes.CAMERA) {
      // 拍照
      chooseImage("camera");
    } else if (authConfirmType.current === AuthTypes.GALLERY_PHOTO) {
      // 从相册选择
      chooseImage("album");
    }
  };

  const openSetPopup = () => {
    if (platformRef.current === "Android") {
      popupTitle.current = "权限申请";
      if (authConfirmType.current === AuthTypes.STORAGE) {
        popupText.current = `在设置＞应用＞${APP_NAME_CN}＞权限中开启存储权限，以正常使用拍照、录制视频等功能`;
      } else if (authConfirmType.current === AuthTypes.CAMERA) {
        popupText.current = `在「设置＞应用＞${APP_NAME_CN}＞权限」中开启相机权限，以正常使用拍照、录制视频等功能`;
      } else if (authConfirmType.current === AuthTypes.GALLERY_PHOTO) {
        popupText.current = `在「设置＞应用＞${APP_NAME_CN}＞权限」中开启相册权限，以使用查看或选择图片视频、文件等功能。`;
      }
    } else {
      if (authConfirmType.current === AuthTypes.STORAGE) {
        popupTitle.current = "无法保存";
        popupText.current = `请在iphone的「设置＞隐私＞照片」选项中，允许${APP_NAME_CN}访问你的照片。`;
      } else if (authConfirmType.current === AuthTypes.CAMERA) {
        popupTitle.current = "相机权限未开启";
        popupText.current = `无法录制视频，前往「设置＞${APP_NAME_CN}」中打开相机权限`;
      }
    }
    showDialog();
  };

  const showDialog = () => {
    Dialog.confirm({
      title: popupTitle.current ? popupTitle.current : "",
      children: popupText.current ? popupText.current : "",
      okText: "去设置",
      cancelText: "取消",
      onOk: () => {
        openSetting();
      },

      platform: "ios",
    });
  };

  // 打开设置
  const openSetting = () => {
    if (platformRef.current === "Android") {
      window.openSetting.openSetting();
    } else {
      window.webkit.messageHandlers.openSetting.postMessage("");
    }
  };

  // 请求权限
  const requestPermissionWeb = (type) => {
    authConfirmType.current = type;
    if (platformRef.current === "Android") {
      window.checkPermission.checkPermission();
    } else if (platformRef.current === "ios") {
      window.webkit.messageHandlers.requestPermission.postMessage(`${type}`);
    } else {
      chooseImage("album");
    }
  };

  const fetchUserInfo = () => {
    const storedUserInfo = Taro.getStorageSync("userInfo") || {};
    getUserInfo()
      .then((res) => {
        console.log("用户信息" + JSON.stringify(res));
        if (res.code === 0) {
          // 成功处理
          const fetchedUserInfo = res.data || {};

          // 合并本地存储和接口返回的数据
          const updatedUserInfo = { ...storedUserInfo, ...fetchedUserInfo };
          console.log("更新后的用户信息" + JSON.stringify(updatedUserInfo));
          // 更新状态和本地存储
          setUserInfo(updatedUserInfo);
          Taro.setStorageSync("userInfo", updatedUserInfo);
        } else {
          // 错误处理
          console.log("错误处理");
        }
      })
      .catch((err) => {})
      .finally(() => {
        setTimeout(() => {
          setLoading(false);
        }, 500);
      });
  };

  // 打开选择图片方式弹窗
  const openPopup = (type) => {
    popupType.current = type;
    setPopupVisible(true);
  };

  // 关闭选择图片方式弹窗
  const handleClose = () => {
    setPopupVisible(false);
  };

  const handleConfirm = (index: number) => {
    if (index === 0) {
      // 请求相机权限
      requestPermissionWeb(AuthTypes.CAMERA);
    } else if (index === 1) {
      // 请求相册权限
      requestPermissionWeb(AuthTypes.GALLERY_PHOTO);
    }
  };

  // 关闭权限说明弹窗
  const handlePermissionClose = () => {
    setPermissionPopupVisible(false);
  };

  const handlePermissionConfirm = (type) => {
    if (platformRef.current === "Android") {
      window.requestPermission.requestPermission(type);
    } else {
      window.webkit.messageHandlers.requestPermission.postMessage(type);
    }
  };

  // 选择图片的方法
  const chooseImage = (sourceType: "album" | "camera") => {
    Taro.chooseImage({
      count: 1,
      sourceType: [sourceType], // 'album' 为从相册选择，'camera' 为拍照
      success: (res) => {
        const imagePath = res.tempFilePaths[0];
        console.log(imagePath)
        console.log("图片路径: ", imagePath);
        // 根据 popupType 更新不同的字段
        let updatedUserInfo;
        if (popupType.current === "headImg") {
          updatedUserInfo = { ...userInfo, avatar: imagePath };

          uploadFile(imagePath)
            .then((response) => {
              if (response.code === 0) {
                console.log("上传成功", response.data);
                handleSave(response.data)
              } else {
                console.error("上传失败", response.message);
              }
            })
            .catch((error) => {
              console.error("上传错误", error);
            });
        } else if (popupType.current === "bg") {
          updatedUserInfo = { ...userInfo, backgroundImage: imagePath };

          uploadFile(imagePath)
            .then((response) => {
              if (response.code === 0) {
                console.log("上传成功", response.data);
                handleSave(response.data)
              } else {
                console.error("上传失败", response.message);
              }
            })
            .catch((error) => {
              console.error("上传错误", error);
            });
        } else if (popupType.current === "qrCode") {
          updatedUserInfo = { ...userInfo, wechatQrCode: imagePath };

          uploadFile(imagePath)
            .then((response) => {
              if (response.code === 0) {
                console.log("上传成功", response.data);
                handleSave(response.data)
              } else {
                console.error("上传失败", response.message);
              }
            })
            .catch((error) => {
              console.error("上传错误", error);
            });
        }
        setUserInfo(updatedUserInfo); // 更新状态
        Taro.setStorageSync("userInfo", updatedUserInfo);
      },
      fail: (err) => {},
    });
  };

  const handleSave = (path: string) => {
    let formData = {}
    if (popupType.current === "headImg") {
        formData["avatar"] = path
    } else if (popupType.current === "bg") {
      formData["backgroundImage"] = path
    } else if (popupType.current === "qrCode") {
      formData["wechatQrCode"] = path
    }

    // 调用 editUser 接口
    editUser(formData)
        .then((res) => {
            console.log(JSON.stringify(res))
            if (res.code === 0) {
                // 成功处理
                Taro.showToast({
                    title: '保存成功',
                    icon: 'success',
                });
            } else {
                // 错误处理
                Taro.showToast({
                    title: '保存失败',
                    icon: 'success',
                });
            }
        })
        .catch((err) => {
            Taro.showToast({
                title: '保存失败',
                icon: 'success',
            });
        });

};

  return (
    <PageSwipeBack leftDrag={true}>
      <View className="edit">
        <View className="bg">
          {/* {userInfo.bg_img ? (
            <Image className="bg-img" src={userInfo.bg_img} />
          ) : (
            <View className="bg-default" />
          )} */}
          <Image className="bg-img" src={userInfo.backgroundImage} />
          <View className="bg-cover" />

          <NavBar
            className={`navBar setPageContent-navbar ${
              platformRef.current !== "H5" ? "navbarPaddingTop" : ""
            }`}
            fixed={false}
            hasBottomLine={false}
            style={{
              color: "white",
              background: "#00000000",
              height: "2.2rem",
              ...(platformRef.current === "H5" ? {} : { paddingTop: "2rem" }),
            }}
            onClickLeft={() => Taro.navigateBack()}
          />

          <Button
            className="edit-bg"
            inline
            icon={
              <IconPicture className="edit-bg-icon" />
              // <img
              //   className="edit-bg-icon"
              //   src={require("../../assets/images/userinfo/change_bg_icon.png")}
              // />
            }
            onClick={() => openPopup("bg")}
          >
            换背景
          </Button>
        </View>

        <View className="cell-container">
          <Cell.Group className="cell-group" bordered={false}>
            <Cell
              className="cell-label-head"
              label="头像"
              showArrow
              text={
                userInfo.avatar ? (
                  <Image
                    className="cell-label-head-img"
                    src={userInfo.avatar}
                  />
                ) : (
                  <IconUser className="cell-label-head-icon" />
                )
              }
              onClick={() => openPopup("headImg")}
            />
            <Cell
              className="cell-label"
              label="昵称"
              text={userInfo.nickname}
              showArrow
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/editUserInfoItem/index?type=${1}&content=${encodeURIComponent(
                    userInfo.nickname
                  )}`,
                })
              }
            />
            <Cell
              className="cell-label"
              label="手机号"
              text={userInfo.mobile}
              showArrow
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/editUserInfoItem/index?type=${2}&content=${
                    userInfo.mobile
                  }`,
                })
              }
            />
            <Cell
              className="cell-label"
              label="微信号"
              text={userInfo.wechatNumber}
              showArrow
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/editUserInfoItem/index?type=${3}&content=${encodeURIComponent(
                    userInfo.wechatNumber
                  )}`,
                })
              }
            />
            <Cell
              className="cell-label-code"
              label="微信二维码"
              showArrow
              text={
                userInfo.wechatQrCode ? (
                  <Image
                    className="cell-label-code-img"
                    src={userInfo.wechatQrCode}
                  />
                ) : (
                  <IconUpload className="cell-label-code-icon" />
                )
              }
              onClick={() => openPopup("qrCode")}
            />
            <Cell
              className="cell-label"
              label="个性签名"
              text={userInfo.personalProfile}
              showArrow
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/editUserInfoItem/index?type=${4}&content=${encodeURIComponent(
                    userInfo.personalProfile
                  )}`,
                })
              }
            />
            <Cell
              className="cell-label"
              label="账号ID"
              text={userInfo.id}
            />
            <Cell
              className="cell-label"
              label="注册方式"
              text={userInfo.platform}
            />
          </Cell.Group>
        </View>

        {/* 底部弹出对话框 */}
        <BottomPopup
          // title="请选择操作"
          options={["拍照", "从相册选择"]}
          btnCloseText="取消"
          onConfirm={handleConfirm}
          onClose={handleClose}
          visible={isPopupVisible}
        />

        <PermissionPopup
          visible={isPermissionPopupVisible}
          type={authConfirmType.current}
          hintText={permissionHint.current}
          onClose={handlePermissionClose}
          onConfirm={handlePermissionConfirm}
        />

        {/* <FullScreenLoading visible={loading} /> */}
      </View>
    </PageSwipeBack>
  );
};

export default EditUserInfo;
