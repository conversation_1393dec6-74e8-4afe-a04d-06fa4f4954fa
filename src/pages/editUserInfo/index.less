@import '@arco-design/mobile-react/style/mixin.less';

.edit {
	width: 100vw;
	.use-var(background-color, background-color);
    .use-dark-mode-query({
        background-color: @dark-background-color;
    });
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	align-items: center;

	.bg {
		display: flex;
		flex-direction: column;
		width: 100%;
		.rem(height, 231);

		.navBar {
			z-index: 102;
			position: absolute;
			top: 0;
			.rem(height, 44);
		}

		.setPageContent-navbar {
			width: 100%;
			.arco-nav-bar-title-text {
				.rem(font-size, 17) !important;
			}
		}
		  
		.navbarPaddingTop {
		    .rem(padding-top, 40) !important;
		}

		&-img {
			position: absolute;
			top: 0;
			width: 100%;
			display: block;
			.rem(height, 231);
		}

		&-cover{
			width: 100%;
			position: absolute;
			top: 0;
			z-index: 101;
			background: rgba(0, 0, 0, 0.1);
			.rem(height, 231);
		}

		// &-default {
		// 	width: 100%;
		// 	position: absolute;
		// 	top: 0;
		// 	z-index: 101;
		// 	background: rgba(0, 0, 0, 0.1);
		// 	.rem(height, 231);
		//   }

		.edit-bg {
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 102;
			position: absolute;
			right: 0;
			.rem(top, 88);
			.rem(height, 26);
			.rem(padding, 6);
			.rem(border-top-left-radius, 50);
			.rem(border-bottom-left-radius, 50);
			background: rgba(0, 0, 0, 0.3);
			.rem(font-size, 10);
			// color: #FFFFFF;

			.arco-button-text {
				.rem(margin-left, 4);
				.rem(font-size, 10);
				// color: #FFFFFF;
			}

			.edit-bg-icon {
				.rem(width, 13);
				.rem(height, 12);
				display: inline-block;
			}
		}
	}

	

	.cell-container {
		position: absolute;
		.rem(top, 127);
		.rem(left, 0);
		border: none;
		z-index: 102;
		box-sizing: border-box;
		.use-var(background-color, background-color);
    	.use-dark-mode-query({
        	background-color: @dark-container-background-color;
    	});
		width: calc(100% - 20px);
		.rem(margin-left, 10);
		.rem(margin-right, 10);
		.rem(border-radius, 10);
		display: flex;
		flex-direction: column;

		.cell-group{
			.rem(margin-top, 10);
			.rem(border-radius, 10);
		}

		.cell-label {
			background-color: transparent;
			flex-shrink: 0;
		}

		.cell-title {
			background-color: transparent;
			.rem(font-size, 15);
			// color: #000000;
			.use-var(color, font-color);
			.use-dark-mode-query({
			  color: @dark-font-color;
			});
		}

		.cell-text {
			.rem(max-width, 200);
			.rem(margin-right, 5);
			.rem(font-size, 14);
			// color: #333333;
			.use-var(color, sub-font-color);
			.use-dark-mode-query({
			  color: @dark-sub-font-color;
			});
		}

		.cell-label-head {
			background-color: transparent;
			border: none;
			.rem(padding-top, 20);
			.rem(padding-bottom, 20);

			&-img {
				.rem(border-radius, 5);
				.rem(width, 50);
				.rem(height, 50);
				display: inline-block;
			}

			&-icon{
				.rem(font-size, 50);
			}
		}

		.cell-label-code {
			background-color: transparent;


			&-img {
				.rem(border-radius, 4);
				.rem(width, 23);
				.rem(height, 23);
				display: inline-block;
			}

			&-icon{
				.rem(font-size, 23);
			}
		}
	}
}