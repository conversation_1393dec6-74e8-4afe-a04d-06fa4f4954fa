import { View, Text } from "@tarojs/components";
import { useLoad, useReachBottom } from "@tarojs/taro";
import "./index.less";
import { Toast, TabBar, SearchBar, Tabs, Sticky, LoadMore, Image, Ellipsis, Button, Dialog, ImagePreview, PullRefresh } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import BottomPopup from "@/components/BottomPopup";
import {
  IconApps,
  IconUser,
  IconInteraction,
  IconSettings,
  IconCommand,
  IconNotification,
  IconHeart,
  IconDownload,
} from "@arco-iconbox/react-yk-arco";
import { IconPicture } from '@arco-design/mobile-react/esm/icon';

import { useSetPermission } from "@/stores/permissionStore";
import {
  AuthTypes,
  AuthStatusAndroid,
  AuthStatusIos,
} from "@/utils/config/authTypes";
import PermissionPopup from "@/components/PermissionPopup";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
import YkSwitchTabBar from "@/components/ykSwitchTabBar/index";
import { navigateToVue } from "@/utils/yk-common";
import { getAlbumList,getNewAlbumList, dynamicBatchDel, dynamicRefresh, updateDynamic, dynamicBatchOperate, favoritesDynamic, deleteFavoritesDynamic, getSearchAlbumList } from "@/utils/api/common/common_user";
import { LoadMoreStatus } from "@arco-design/mobile-react/cjs/load-more";
import React from "react";
import { useState, useRef, useEffect, useCallback } from "react";
import { useDebounce } from "@/utils/yk-common";

export default function Home() {
  const userInfo = Taro.getStorageSync("userInfo");
  const [isPermissionPopupVisible, setPermissionPopupVisible] = useState(false); // 控制权限弹窗的显示状态
  const popupTitle = useRef("");
  const popupText = useRef("");
  const permissionHint = useRef(""); // 权限弹窗的说明
  const platformRef = useRef("H5");
  const authConfirmType = useRef(-1);
  const allPermissions = useRef([]);
  const clickItem = useRef({});
  const setPermission = useSetPermission();
  const isRequestPermission = useRef(true);
  const [permissionsState, setPermissionsState] = useState([]); // 用于存储并渲染权限数据
  const [previewIndex, setPreviewIndex] = useState(-1);
  const [currentImages, setCurrentImages] = useState([]);
  const imageRefs = useRef({});
  const [isPopupVisible, setPopupVisible] = useState(false); // 控制选择图片方式弹窗的显示状态
  const [expandedId, setExpandedId] = useState<string | number | null>(null);

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;

    window.checkPermissionCallBack = checkPermissionCallBack;
    isRequestPermission.current = true;
    if (isAndroid) {
      platformRef.current = "Android";
      window.webPermissonDeny = webPermissonDeny;
      window.webPermissonConsent = webPermissonConsent;

      window.checkPermission.checkPermission();
    } else if (isIos) {
      platformRef.current = "IOS";

      window.webkit.messageHandlers.checkPermission.postMessage("");
    } else {
      platformRef.current = "H5";
    }

    return () => {
      delete window.checkPermissionCallBack;
      delete window.webPermissonDeny;
      delete window.webPermissonConsent;
    };
  }, []);

  // 定义权限提示信息的映射
  const permissionMapping = {
    [AuthTypes.CAMERA]: {
      hint: "在下方弹窗中选择允许后，你可以拍摄照片或视频以发送给朋友、使用视频通话等功能。你还可以在其他场景中访问摄像头进行拍摄照片和视频。",
    },
    [AuthTypes.GALLERY_PHOTO]: {
      hint: "在下方弹窗中选择允许后，你可以从手机相册中选择图片发送给朋友。",
    },
    [AuthTypes.GALLERY_VIDEO]: {
      hint: "在下方弹窗中选择允许后，你可以从手机相册中选择视频发送给朋友。",
    },
    [AuthTypes.GALLERY_AUDIO]: {
      hint: "在下方弹窗中选择允许后，你可以从手机相册中选择音频发送给朋友。",
    },
    [AuthTypes.STORAGE]: {
      hint: "在下方弹窗中选择允许后，你可以保存图片到相册。你还可以在其他场景访问设备里的照片视频和文件，以及保存内容到设备。",
    },
    [AuthTypes.AUDIO]: {
      hint: "录音权限说明",
    },
    [AuthTypes.NOTICE]: {
      hint: "通知权限说明",
    },
    [AuthTypes.LOCATION]: {
      hint: "定位权限说明",
    },
    [AuthTypes.CONTACT]: {
      hint: "通讯录权限说明",
    },
    [AuthTypes.FLOATWIN]: {},
    [AuthTypes.ACCESS]: {},
    [AuthTypes.AUTOSTART]: {},
  };

  // 权限回调处理
  const checkPermissionCallBack = async (e) => {
    console.log(JSON.stringify(e));
    // 更新权限状态
    allPermissions.current = await setPermission(e); // 确保更新后赋值
    setPermissionsState([...allPermissions.current]); // 更新状态
    const currentAuth = authConfirmType.current;
    const permissionConfig = permissionMapping[currentAuth];
    if (isRequestPermission.current) {
      isRequestPermission.current = false;
      if (
        !permissionConfig &&
        currentAuth !== AuthTypes.FLOATWIN &&
        currentAuth !== AuthTypes.ACCESS &&
        currentAuth !== AuthTypes.AUTOSTART
      ) {
        console.error("Invalid authConfirmType:", currentAuth);
        return;
      }

      if (!hasPermission(currentAuth)) {
        // 设置权限提示信息（如果存在）
        if (permissionConfig.hint) {
          permissionHint.current = permissionConfig.hint;
          // 显示权限弹窗

          setPermissionPopupVisible(true);
        }
      }

      if (platformRef.current === "Android") {
        window.requestPermission.requestPermission(currentAuth + "");
      } else {
        if (hasPermission(currentAuth)) {
          webPermissonConsent();
        } else {
          webPermissonDeny();
        }
      }
    }
  };

  const hasPermission = (authType) => {
    const permission = allPermissions.current.find(
      (perm) => perm.authType === authType
    );
    console.log(allPermissions.current);
    return permission ? permission.state : false; // 如果找到了对应权限，返回权限状态；如果找不到，返回 false
  };

  const webPermissonDeny = () => {
    isRequestPermission.current = false;
    handlePermissionClose();
    // openSetting();
    // openSetPopup(clickItem.current);
  };

  const webPermissonConsent = () => {
    handlePermissionClose();
    if (platformRef.current === "Android") {
      window.checkPermission.checkPermission();
    } else {
      window.webkit.messageHandlers.checkPermission.postMessage("");
    }
  };

  // 关闭权限说明弹窗
  const handlePermissionClose = () => {
    setPermissionPopupVisible(false);
  };

  const handlePermissionConfirm = (type) => {
    if (platformRef.current === "Android") {
      window.requestPermission.requestPermission(type);
    } else {
      window.webkit.messageHandlers.requestPermission.postMessage(type);
    }
  };

  const openCloseSetPopup = (item) => {
    clickItem.current = item;
    authConfirmType.current = item.authType;
    popupTitle.current = `${item.name}权限`;
    if (platformRef.current === "Android") {
      popupText.current = `你可以前往「设置＞应用＞${APP_NAME_CN}＞权限中」关闭${item.name}权限。`;
    } else {
      popupText.current = `你可以前往「设置＞${APP_NAME_CN}」关闭${item.name}权限。`;
    }
    showDialog();
  };

  const openSetPopup = (item) => {
    clickItem.current = item;
    authConfirmType.current = item.authType;
    console.log(authConfirmType.current);
    if (
      item.authType === AuthTypes.FLOATWIN ||
      item.authType === AuthTypes.AUTOSTART ||
      item.authType === AuthTypes.ACCESS
    ) {
      popupTitle.current = `${item.name}权限`;
      if (platformRef.current === "Android") {
        popupText.current = `无法使用${item.name}，前往「设置＞应用＞${APP_NAME_CN}＞权限中」打开${item.name}权限。」`;
      } else {
        popupText.current = `无法使用${item.name}，前往「设置＞${APP_NAME_CN}」打开${item.name}权限。」`;
      }
      showDialog();
    } else {
      if (item.currentStatus === AuthStatusAndroid.DENIEDANDNOTASK) {
        openSetting();
      } else {
        requestPermissionWeb(authConfirmType.current);
      }
    }
  };

  const showDialog = () => {
    Dialog.confirm({
      title: popupTitle.current ? popupTitle.current : "",
      children: popupText.current ? popupText.current : "",
      okText: "前往设置",
      cancelText: "取消",
      onOk: () => {
        handleSettingConfirm();
      },
      platform: platformRef.current === "Android" ? "android" : "ios",
    });
  };

  // 打开设置
  const openSetting = () => {
    if (platformRef.current === "Android") {
      window.openSetting.openSetting();
    } else {
      window.webkit.messageHandlers.openSetting.postMessage("");
    }
  };

  const handleSettingConfirm = () => {
    console.log(clickItem.current.authType);
    if (platformRef.current === "Android") {
      if (
        clickItem.current.authType === AuthTypes.FLOATWIN ||
        clickItem.current.authType === AuthTypes.AUTOSTART ||
        clickItem.current.authType === AuthTypes.ACCESS
      ) {
        requestPermissionWeb(clickItem.current.authType);
      } else {
        openSetting();
      }
    } else {
      window.webkit.messageHandlers.openSetting.postMessage("");
    }
  };

  // 请求权限
  const requestPermissionWeb = (type) => {
    isRequestPermission.current = true;
    authConfirmType.current = type;
    if (platformRef.current === "Android") {
      window.checkPermission.checkPermission();
    } else {
      window.webkit.messageHandlers.requestPermission.postMessage(`${type}`);
    }
  };

  // 提示框
  const toast = (func, options) => {
    let _window: any = window;
    if (!!_window.toastInstance) {
      _window.toastInstance.close();
    }
    _window.toastInstance = Toast[func](options);
  };

  const ArcoGridData = [
    {
      img: <IconInteraction className="gridModeBoxContent-grid-icon" />,
      title: <Text className="gridModeBoxContent-grid-text">Arco组件</Text>,
      onClick: () => {
        navigateToVue('/pages/workDesk/index')
      },
      className: "gridModeBoxContent-grid-item",
    },
    {
      img: <IconApps className="gridModeBoxContent-grid-icon" />,
      title: <Text className="gridModeBoxContent-grid-text">Arco图标</Text>,
      onClick: () => {
        Taro.navigateTo({ url: "/pages/arcoIconShow/index" });
      },
      className: "gridModeBoxContent-grid-item",
    },
    {
      img: <IconSettings className="gridModeBoxContent-grid-icon" />,
      title: <Text className="gridModeBoxContent-grid-text">设置</Text>,
      onClick: () => {
        Taro.navigateTo({ url: "/pages/settings/index" });
      },
      className: "gridModeBoxContent-grid-item",
    },
    {
      img: <IconUser className="gridModeBoxContent-grid-icon" />,
      title: <Text className="gridModeBoxContent-grid-text">编辑资料</Text>,
      onClick: () => {
        Taro.navigateTo({ url: "/pages/editUserInfo/index" });
      },
      className: "gridModeBoxContent-grid-item",
    },
  ];

  const systemGridData = [
    {
      img: <IconNotification className="gridModeBoxContent-grid-icon" />,
      title: <Text className="gridModeBoxContent-grid-text">系统权限</Text>,
      onClick: () => {
        Taro.navigateTo({ url: "/pages/permissionSetting/index" });
      },
      className: "gridModeBoxContent-grid-item",
    },
    {
      img: <IconCommand className="gridModeBoxContent-grid-icon" />,
      title: <Text className="gridModeBoxContent-grid-text">颜色设置</Text>,
      onClick: () => {
        Taro.navigateTo({ url: "/pages/colorSettings/index" });
      },
      className: "gridModeBoxContent-grid-item",
    },
  ]

  const [albumList, setAlbumList] = React.useState<any>([]);
  const albumPageRef = useRef(1);
  const [albumLimit, setAlbumLimit] = React.useState(100);
  const [albumType, setAlbumType] = React.useState(0); // 0:全部 1:上新
  //是否已经加载完 ，且没有更多数据
  const [isLoadEnd, setIsLoadEnd] = React.useState(false);
  // 加载状态
  const [loadStatus, setLoadStatus] = React.useState<LoadMoreStatus>("prepare"); //"before-ready" | "prepare" | "loading" | "nomore" | "retry"
  // 是否显示省略号,把id添加进来
  const [ellipsis, setEllipsis] = React.useState<any>([]);
  const [searchVal, setSearchVal] = React.useState("");
  const searchImgRef = useRef('');


  // 展开or收起
  const handleEllipsis = (id: string) => {
    console.log(ellipsis,id);
    if (ellipsis.includes(id)) {
      setEllipsis(ellipsis.filter((item) => item !== id));
    } else {
      setEllipsis([...ellipsis, id]);
    }
  };

  // 获取相册列表
  const getAlbumListData = async () => {
    let data = {
      pageNo: albumPageRef.current,
      pageSize: albumLimit,
      userId: userInfo.id
    };
    if (isLoadEnd) {
      setLoadStatus("nomore");
      return;
    }
    let res: any;
    if (albumType == 0) {
      res = await getAlbumList(data);
    } else {
      res = await getNewAlbumList(data);
    }
    if (res.code == 0) {
      setAlbumList(res.data.list);
      // 判断返回数据的总条数
      if (res.data.list.length < albumLimit) {
        setIsLoadEnd(true);
        setLoadStatus("nomore");
      } else {
        setLoadStatus("prepare");
        setIsLoadEnd(false);
        albumPageRef.current = albumPageRef.current + 1;
      }
    } else {
      setLoadStatus("retry");
      toast("error", {
        content: res.msg,
        duration: 2000,
      });
    }
  };

   // 获取相册列表
   const getSearchAlbumListData = async () => {
    let data: any = {
      page: albumPageRef.current,
      limit: albumLimit,
      type: albumType,
    };
    if (searchImgRef.current.length > 0) {
      data.content = searchImgRef.current;
    } else {
      data.content = searchVal;
    }

    if (isLoadEnd) {
      setLoadStatus("nomore");
      return;
    }
    const res: any = await getSearchAlbumList(data);
    if (res.code == 0) {
      setAlbumList(res.data);
      // 判断返回数据的总条数
      if (res.data.length < albumLimit) {
        setIsLoadEnd(true);
        setLoadStatus("nomore");
      } else {
        setLoadStatus("prepare");
        setIsLoadEnd(false);
        albumPageRef.current = albumPageRef.current + 1;
      }
    } else {
      setLoadStatus("retry");
      toast("error", {
        content: res.msg,
        duration: 2000,
      });
    }
  };

  // const tabData = [{ title: "全部" }, { title: "上新" }];
  const tabData = [{ title: "全部" }];

  const onTabsChange = (e) => {
    console.log(e);
  };

  // 删除
  const handleDelete = (id: string) => {
    Dialog.confirm({
      title: "温馨提示",
      children: "删除图文后不可恢复，确定删除？",
      okText: "删除",
      cancelText: "取消",
      onOk: () => {
        delConfirm(id);
      },

      platform: "ios",
    });
  };

  const handleDown = (id: string) => {
    Dialog.confirm({
      title: "温馨提示",
      children: "确定要下架商品吗？",
      okText: "确定",
      cancelText: "取消",
      onOk: () => {
        downConfirm(id);
      },

      platform: "ios",
    });
  };

  const downConfirm = (id: string) => {
    let data = {
      isListed: 2,
      id: id
    };
    updateDynamic(data).then((res: any) => {
      if (res.code == 0) {
        toast("success", {
          content: "下架成功",
          duration: 2000,
        });
        getAlbumListData();
      } else {
        toast("error", {
          content: res.msg,
          duration: 2000,
        });
      }
    });
  };

  // 删除
  const delConfirm = (id: string) => {
    let data = {
      id: id,
      isListed: 2
    };
    updateDynamic(data).then((res: any) => {
      if (res.code == 0) {
        toast("success", {
          content: "删除成功",
          duration: 2000,
        });
        getAlbumListData();
      } else {
        toast("error", {
          content: res.msg,
          duration: 2000,
        });
      }
    });
  };

  const handleCollect = (item: any) => {
    let data = {
      dynamicsId: item.id,
      userId: userInfo.id
    };

    if (item.isCollect == 1) {
      deleteFavoritesDynamic(data).then((res: any) => {
        if (res.code == 0) {
          if (item.isCollect == 1) {
            item.isCollect = 0
            toast("success", {
              content: "取消成功",
              duration: 2000,
            });
          } else {
            item.isCollect = 1
            toast("success", {
              content: "收藏成功",
              duration: 2000,
            });
          }
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      });
    } else {
      favoritesDynamic(data).then((res: any) => {
        if (res.code == 0) {
          if (item.isCollect == 1) {
            item.isCollect = 0
            toast("success", {
              content: "取消成功",
              duration: 2000,
            });
          } else {
            item.isCollect = 1
            toast("success", {
              content: "收藏成功",
              duration: 2000,
            });
          }
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      });
    }

    
  };

  // 刷新
  const handleItemRefresh = (id: string) => {
    let data = {
      dynamic_id: id
    };
    dynamicRefresh(data).then((res) => {
      toast("success", {
        content: "刷新成功",
        duration: 2000,
      });
    });
  };


  // 动态置顶
  const handleItemTop = (item) => {
    let data = {
      isTop: item.isTop == 1 ? 0 : 1,
      id: item.id
    };
    updateDynamic(data).then((res: any) => {
      if (res.code == 0) {
        if (item.sort == 1) {
          item.sort = 0;
          toast("success", {
            content: "取消置顶成功",
            duration: 2000,
          });
        } else {
          item.sort = 1;
          toast("success", {
            content: "置顶成功",
            duration: 2000,
        });
      }
    }});
  };

  
  // 跳转到详情页
  const goToDetailPage = (item) => {
    Taro.navigateTo({
      url: `/pages/detail/index?dynamicId=${item.id}&dynamicUserId=${item.userId}`
    });
  };

  // 编辑
  const handleEdit = (item) => {
    Taro.setStorageSync('releaseDynamicList', item);
    console.log(item,'--------------');
    Taro.navigateTo({
      url: `/pages/releaseDynamic/index?type=2`
    });
  };

  const handleSave = (item) => {
    Taro.setStorageSync('releaseDynamicList', item);
    Taro.navigateTo({
      url: `/pages/releaseDynamic/index?type=4`
    });
  };

  const handleDownload = (item) => {
      // 检查存储权限
  if (!hasPermission(AuthTypes.STORAGE)) {
    // 如果没有权限，请求权限
    requestPermissionWeb(AuthTypes.STORAGE);
    return;
  }
  // 有权限则执行下载
  downloadConfirm(item);
  };

  const downloadConfirm = (item) => {
    let imageList = item.dynamic_img.split(',');
    for (let i = 0; i < imageList.length; i++) {
      if (platformRef.current === "Android") {
        window.downloadImg.downloadImg(imageList[i]);
      } else {
        window.webkit.messageHandlers.saveImgWithUrlStr.postMessage(imageList[i]);
      }
    }
    if (item.dynamic_title == '') {
      toast("success", {
        content: "下载成功",
        duration: 2000,
      });
    } else {
      Taro.setClipboardData({
        data: item.dynamic_title,
      });
      // toast("success", {
      //   content: "下载成功，文字已复制",
      //   duration: 2000,
      // });
    }
  };

  // 处理图片点击预览
  const handleImagePreview = (images, index, itemId) => {
    const imageUrls = images.split(',').map(url => ({
      src: url,
      fallbackSrc: url
    }));
    setCurrentImages(imageUrls);
    setPreviewIndex(index);
  };

  const goToUserDetailPage = (user_id) => {
    if (user_id == userInfo.user_id) {
      navigateToVue("/pages/pagesA/albumManage/albumManage");
    } else {
      navigateToVue(`/pages/pagesA/userDetail/index?user_id=${user_id}`);
    }
  };

  // goToUserDetailPage(user_id) {
  //   if (!this.isLogin()) {
  //     return;
  //   }
  //   if (user_id == this.loginUserInfo.user_id) {
  //     uni.navigateTo({
  //       url: '/pages/pagesA/albumManage/albumManage'
  //     });
  //   } else {
  //     uni.navigateTo({
  //       url: `/pages/pagesA/userDetail/index?user_id=${user_id}`
  //     });
  //   }
  // },
  const handleRefresh = async () => {
    albumPageRef.current = 1;
    setIsLoadEnd(false);
    await getAlbumListData();
  };

  const handleSearch = (value: string) => {
    setSearchVal(value);
    searchImgRef.current = '';
    // setIsSearch(true);
    albumPageRef.current = 1;
    setIsLoadEnd(false);
    getSearchAlbumListData();
  };

  const debouncedSearch = useDebounce(handleSearch, 500);

  // 搜索框onChange处理
  const handleSearchChange = (e) => {
    setSearchVal(e.target.value); // 立即更新输入框的值
    if (e.target.value.length > 0) {
      debouncedSearch(e.target.value); // 延迟执行搜索
    } else {
      searchImgRef.current = '';
      getAlbumListData();
    }
  };

    // 打开选择图片方式弹窗
    const openPopup = () => {
      setPopupVisible(true);
    };
  
    // 关闭选择图片方式弹窗
    const handleClose = () => {
      setPopupVisible(false);
    };
  
    const handleConfirm = (index: number) => {
      if (index === 0) {
        // 请求相机权限
  if (!hasPermission(AuthTypes.CAMERA)) {
    // 如果没有权限，请求权限
    requestPermissionWeb(AuthTypes.CAMERA);
    return;
  }
chooseImage('camera');
      } else if (index === 1) {
        // 请求相册权限
  if (!hasPermission(AuthTypes.GALLERY_PHOTO)) {
    console.log('相册权限请求');
    // 如果没有权限，请求权限
    requestPermissionWeb(AuthTypes.GALLERY_PHOTO);
    return;
  }
  console.log('相册权限');
chooseImage('album');
      }
    };

    const blobToBase64 = async (blobUrl: string): Promise<string> => {
      try {
        // 首先通过fetch获取blob数据
        const response = await fetch(blobUrl);
        const blob = await response.blob();
        
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            // reader.result包含base64字符串
            const base64String = reader.result as string;
            // 移除开头的 "data:image/jpeg;base64," 部分（如果后端不需要这个前缀）
            const base64 = base64String.split(',')[1];
            resolve(base64);
          };
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });
      } catch (error) {
        console.error('转换base64失败:', error);
        throw error;
      }
    };


     // 选择图片的方法
  const chooseImage = (sourceType: "album" | "camera") => {
    console.log(sourceType);
    Taro.chooseImage({
      count: 1,
      sourceType: [sourceType], // 'album' 为从相册选择，'camera' 为拍照
      success: async (res) => {
        const imagePath = res.tempFilePaths[0];
        //转为base64
        const base64Data = await blobToBase64(imagePath);
        setSearchVal('');
        searchImgRef.current = base64Data;
        getSearchAlbumListData();
      },
      fail: (err) => {},
    });
  };

  // 初始化
  useLoad(() => {
    console.log("Page loaded.");
    getAlbumListData();
  });
  // 上拉触底
  // useReachBottom(() => {
  //   setLoadStatus("loading");
  //   setTimeout(() => {
  //     getAlbumListData();
  //   }, 2000);
  // });

  return (
    <View className="indexBox">
       <YkNavBar switchTab title="首页" />
      <View className="searchLine">
      <SearchBar shape="round" placeholder="搜索" clearable={false} value={searchVal} onChange={handleSearchChange} suffix={<View onClick={() => openPopup()}><IconPicture></IconPicture></View>} />
      </View>
       <Tabs
        tabBarStyle={{ padding: "0 18px" }}
        tabs={tabData}
        tabBarHasDivider={false}
        swipeable
        tabBarArrange={"start"}
        onChange={onTabsChange}
        renderTabBar={(TabBar) => <Sticky topOffset={44}>{TabBar}</Sticky>}
      >
        <PullRefresh onRefresh={() => handleRefresh()}>
        <View className="showAlbumBox">
          {albumList.map((item, index) => {
            return (
              <View className="showAlbumBox-item" >
                <View 
                  className="showAlbumBox-item-top" 
                  onClick={(e) => {
                    e.stopPropagation();
                    goToUserDetailPage(item.user_id);
                  }}
                >
                  <Image
                    fit="cover"
                    showLoading={true}
                    showError={true}
                    src={item.avatar}
                    className="showAlbumBox-item-top-image"
                    radius="50%"
                  />
                  <View className="showAlbumBox-item-top-content">
                    <View className="showAlbumBox-item-top-content-name">
                      {item.nickname}
                    </View>
                    <View className="showAlbumBox-item-top-content-date">
                      {item.date}
                    </View>
                  </View>
                </View>
                <View className="showAlbumBox-item-title" onClick={() => goToDetailPage(item)}>
                  <Ellipsis
                    className="showAlbumBox-item-title-content"
                    text={item.content}
                    maxLine={3}
                    ellipsisNode={<span className="demo-link">...<span style={{color:'#355A95'}}>展开</span></span>}
                    collapseNode={<span className="demo-link"  style={{color:'#355A95'}}>收起</span>}
                    ellipsis={!ellipsis.includes(item.id)}
                    onCollapseNodeClick={(e) => {
                      e.stopPropagation();
                      handleEllipsis(item.id);
                    }}
                    onEllipsisNodeClick={(e) => {
                      e.stopPropagation();
                      handleEllipsis(item.id);
                    }}
                    // reflowOnResize={true}
                  />
                </View>
                <View className="showAlbumBox-item-center">
                  {item.pictures &&
                   item.pictures.split(",").map((item2, index2) => {
                    console.log(item2);
                      return (
                        index2 < 9 && (
                          <Image
                            fit="cover"
                            showLoading={true}
                            showError={true}
                            src={item2}
                            className="showAlbumBox-item-center-item"
                            key={index2}
                            onClick={() => handleImagePreview(item.pictures, index2, item.id)}
                            ref={(ref) => {
                              if (!imageRefs.current[item.id]) {
                                imageRefs.current[item.id] = [];
                              }
                              imageRefs.current[item.id][index2] = ref;
                            }}
                          />
                        )
                      );
                    })}
                </View>
                <View className="showAlbumBox-item-jiage">
                  <View className="showAlbumBox-item-jiage-icon">￥</View>
                  <View className="showAlbumBox-item-jiage-val">
                    {item.price}
                  </View>


                  
                </View>


                <View className="move-attrs-wrap-my">
                        { (item.labelAndCatalogueIds	 || item.productSpecificationsNames	 || item.productColorNames	) && (                        
                          <Text
                            className="move-attrs-my"
                            onClick={() =>
                                setExpandedId(item.id === undefined
                                ? null
                                : (expandedId === item.id ? null : item.id)
                                )
                            }
                          >
                            商品属性
                            {expandedId === item.id ?
                                <Text className="move-attrs-arrow">▼</Text> :
                                <Text className="move-attrs-arrow">▶</Text>
                            }
                          </Text>
                        )}                     
                        

                        {expandedId === item.id && (
                          <View className="attr-content card-attr-content">
                                  {item.labelAndCatalogueIds  ?
                                    (<View className="attr-row" key="标签">                        
                                      <View className="attr-values">
                                        {[...item.labelAndCatalogueIds.split(",")].map((val, index) => (
                                        <Text
                                            className="tag-badge" // 使用 .selected 样式
                                            key={index} // 使用 index 作为 key
                                        >
                                            {val}
                                        </Text>
                                        ))}
                                      </View>
                                    </View>):(<></>)
                                  }
                                  {item.productSpecificationsNames  ?
                                    (<View className="attr-row" key="规格">                        
                                      <View className="attr-values">
                                        <Text className="attr-label">规格：</Text>
                                        {[...item.productSpecificationsNames.split(",")].map((val, index) => (
                                        <Text
                                            className="attr-value" // 使用 .selected 样式
                                            key={index} // 使用 index 作为 key
                                        >
                                            {val}
                                        </Text>
                                        ))}
                                      </View>
                                    </View>):(<></>)
                                  }
                                  {item.productColorNames ?
                                    (<View className="attr-row" key="颜色">                                    
                                        <View className="attr-values">
                                            <Text className="attr-label">颜色：</Text>
                                            {[...item.productColorNames.split(",")].map((val, index) => (
                                            <Text
                                                className="attr-value" // 使用 .selected 样式
                                                key={index} // 使用 index 作为 key
                                            >
                                                {val}
                                            </Text>
                                            ))}
                                        </View>
                                    </View>):(<></>)
                                  }
                          </View>
                        )}
                </View>    


                <View className="showAlbumBox-item-playMode">
                  <Text className="showAlbumBox-item-playMode-link" onClick={() => handleDelete(item.id)}>删除</Text>
                  <Text className="showAlbumBox-item-playMode-link" onClick={() => handleDown(item.id)}>下架</Text>
                  <Text className="showAlbumBox-item-playMode-link" onClick={() => handleItemRefresh(item.id)}>刷新</Text>
                  <Text className="showAlbumBox-item-playMode-link" onClick={() => handleItemTop(item)}> {item.sort == '1' ? '取顶' : '置顶'}</Text>
                  <Text className="showAlbumBox-item-playMode-link" onClick={() => handleEdit(item)}>编辑</Text>
                </View>
                <View className="showAlbumBox-item-playBtn">
                  <View className="showAlbumBox-item-playBtn-left">
                    <View className="showAlbumBox-item-playBtn-left-item" onClick={() => handleCollect(item)}>
                      <IconHeart className="showAlbumBox-item-playBtn-left-item-icon" />
                      <Text className="showAlbumBox-item-playBtn-left-item-text">
                        {item.isCollect == 1 ? '已收藏' : '收藏'}
                      </Text>
                    </View>
                    <View className="showAlbumBox-item-playBtn-left-item" onClick={() => handleDownload(item)}>
                      <IconDownload className="showAlbumBox-item-playBtn-left-item-icon" />
                      <Text className="showAlbumBox-item-playBtn-left-item-text">
                        下载
                      </Text>
                    </View>
                  </View>
                  
                  <Button
                      inline
                      shape="round"
                      // icon={<IconGift />}
                      type="ghost"
                      onClick={() => handleSave(item)}
                    >
                      {item.userId == userInfo.userId ? '一键分享' : '一键转存'}
                    </Button>
                </View>
              </View>
            );
          })}

          <LoadMore
            style={{ paddingTop: 16, paddingBottom: 16 }}
            status={loadStatus}
            threshold={0}
            getData={getAlbumListData}
          />
        </View>
        </PullRefresh>
        
      </Tabs>

      <YkSwitchTabBar activeTab={0} />

      <BottomPopup
          // title="请选择操作"
          options={["拍照", "从相册选择"]}
          btnCloseText="取消"
          onConfirm={handleConfirm}
          onClose={handleClose}
          visible={isPopupVisible}
        />
        
      <PermissionPopup
          visible={isPermissionPopupVisible}
          type={authConfirmType.current}
          hintText={permissionHint.current}
          onClose={handlePermissionClose}
          onConfirm={handlePermissionConfirm}
        />

      <ImagePreview
        openIndex={previewIndex}
        close={() => setPreviewIndex(-1)}
        images={currentImages}
        onImageDoubleClick={(index) => console.log('双击图片', index)}
        getThumbBounds={(index) => {
          const currentItemRefs = Object.values(imageRefs.current)[0];
          return currentItemRefs?.[index]?.dom?.getBoundingClientRect();
        }}
      />
    </View>
  );
}
