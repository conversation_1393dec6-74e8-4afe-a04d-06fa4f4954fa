@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pages/index/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  
.indexBox {
  width: 100%;
  position: relative;
}

.gridModeBoxTitle {
  width: 100%;
  box-sizing: border-box;
  padding: 8px 18px;
  padding-top: 16px;
  font-size: 12px;
  font-weight: normal;
  color: var(--sub-info-font-color);
  .use-dark-mode-query({
    color: var(--dark-sub-info-font-color);
  });
}

.gridModeBoxContent {
  width: 100%;
  box-sizing: border-box;
  padding: 0 12px;

  &-grid {
    box-sizing: border-box;
    border-radius: 4px;
    padding: 16px 0;
    background-color: var(--container-background-color);
    .use-dark-mode-query({
      background-color: var(--dark-container-background-color);
    });

    &-item:active{
      opacity: 0.6;
    }

    &-icon {
      font-size: 18px;
    }

    &-icon [fill] {
      fill: var(--font-color);
      .use-dark-mode-query({
          fill: var(--dark-font-color);
      });
    }
    &-icon [stroke] {
      stroke: var(--font-color);
      .use-dark-mode-query({
        stroke: var(--dark-font-color);
      });
    }

    &-text {
      font-size: 12px;
      color: var(--font-color);

      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }
  }
}


.showAlbumBox {
  position: relative;
  width: 100%;
  box-sizing: border-box;
//   padding-top: 44px;

  &-item {
    position: relative;
    padding: 15px;
    box-sizing: border-box;
    width: 100%;

    &-title {
      width: 100%;
      margin-top: 8px;
      box-sizing: border-box;
      padding-left: 46px;
    }

    &-top {
      width: 100%;
      height: 40px;
      display: flex;
      &-image {
        width: 40px;
        height: 40px;
      }

      &-content {
        flex: 1;
        padding-left: 10px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        &-name {
          font-size: 13px;
          color: #576b95;
        }
        &-date {
          font-size: 12px;
          color: #999999;
        }
      }
    }

    &-center {
      width: 100%;
      margin-top: 8px;
      box-sizing: border-box;
      padding-left: 46px;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      /* 定义三列，每列宽度相等 */
      gap: 4px;
      /* 格子之间的间距 */
      &-item {
        width: 97px;
        height: 97px;
        background-color: #ccc;
      }
    }

    &-jiage {
      width: 100%;
      height: 44px;
      display: flex;
      box-sizing: border-box;
      padding-left: 46px;
      align-items: center;
      color: #eb483f;

      &-icon {
        font-size: 12px;
      }

      &-val {
        font-size: 18px;
        font-weight: bold;
      }
    }

    &-playMode {
      width: 100%;
      padding: 10px;
      box-sizing: border-box;
      padding-left: 46px;
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      &-link {
        color: #576b95;
        font-size: 12px;

        &:active {
          opacity: 0.8;
        }
      }
    }

    &-playBtn {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      padding-top: 10px;
      padding-left: 46px;

      &-left {
        display: flex;

        &-item {
          width: 40px;
          margin-right: 10px;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: center;

          &:active {
            opacity: 0.8;
          }

          &-icon {
            width: 18px;
            height: 18px;
            color: #999999;
          }

          &-text {
            font-size: 12px;
            color: #999999;
          }
        }
      }

      &--right {
        height: 40px;
      }
    }
  }
}

  .move-attrs-wrap-my{
    padding: 0;
    padding-left: 46px;
    margin-top: -20px;
    .use-dark-mode-query({
      border-bottom: 1px solid #333;
    });
  }

  .move-attrs-my {
    font-size: 13px;
    color: #8c8c8c;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    .move-attrs-arrow {
      font-size: 12px;
      margin-left: 2px;
      color: #c0c0c0;
    }
  }

  .card-attr-content {
    background: #f7f8fa;
    border-radius: 8px; // 根据图片调整圆角
    padding: 12px; // 调整内边距
    margin-top: 8px; // 调整与属性文字间距
    margin-right: 0px; // 移除右侧外边距
    box-shadow: none; // 移除阴影
    .use-dark-mode-query({
      background: @dark-container-background-color;
      box-shadow: none;
    });
  }
  .card-attr-content .attr-row {
      align-items: center; // 使属性 label 和 values 垂直居中
  }
  .card-attr-content .attr-row + .attr-row {
    border-top: 1px dashed #eee; // 使用虚线作为分隔线
    padding-top: 8px; // 调整上方内边距
    margin-top: 8px; // 调整上方外边距
    .use-dark-mode-query({
      border-top: 1px dashed #333;
    });
  }
  .card-attr-content .attr-label {
    font-size: 14px; // 根据图片调整字体大小
    color: #222;
    font-weight: bold; // 字体加粗
    min-width: 40px; // 调整最小宽度
    margin-top: 0px; // 移除顶部外边距
    .use-dark-mode-query({ color: var(--dark-font-color); });
  }
  .card-attr-content .attr-values {
    display: flex;
    flex-wrap: wrap;
    gap: 8px 8px; // 调整属性值之间的间距
  }
  .tag-badge {   // 标签样式
   
    font-size: 13px; // 根据图片调整字体大小
    color:   #165DFF;
    background: #E8F3FF;
    border-radius: 4px;     
    padding: 3px 8px;   // 移除内边距
    margin-bottom: 0px; // 移除底部外边距
    position: relative;
    transition: none;
    border: none;
    .use-dark-mode-query({ color: var(--dark-font-color); });

  }
  .card-attr-content .attr-value {
    font-size: 13px; // 根据图片调整字体大小
    color: #444;
    background: none;
    border: none;
    padding: 0; // 移除内边距
    margin-bottom: 0px; // 移除底部外边距
    position: relative;
    cursor: pointer;
    transition: none;
    .use-dark-mode-query({ color: var(--dark-font-color); });

    &.selected {
      color: #165DFF; // 选中状态字体颜色
      font-weight: bold; // 选中状态字体加粗
    }

    // 属性值之间的分隔线，根据图片样式调整
    &:not(:last-child)::after {
      content: '|'; // 使用竖线作为分隔符
      position: static; // 移除绝对定位
      transform: none; // 移除transform
      margin-left: 8px; // 调整与属性值间距
      height: auto; // 移除固定高度
      background: none; // 移除背景
      color: #e5e6eb; // 分隔符颜色
       .use-dark-mode-query({ color: #333; });
    }
     &:last-child::after { // 移除最后一个属性值后面的分隔符
         display: none;
    }

  }
}
