@import '@arco-design/mobile-react/style/mixin.less';


[id^="/pages/orderSetting/deliveryManager/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
}

.deliveryManagerPage {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  
  &.cover {
    overflow: hidden;
    position: fixed;
    height: 100%;
    width: 100%;
  }

  .loading-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99999;
    background-color: #ffffff;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });
  }

  .loading-icon {
    width: 40px;
    height: 40px;
    border: 2px solid #999999;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .container {
    display: flex;
    flex-direction: column;
    padding: 0 10px;

    &-title {
      margin-left: 10px;
      margin-top: 15px;
      font-size: 13px;
      color: #999999;
    }

    &-box {
      &-cellgroup {
        margin-bottom: 10px;
        overflow: hidden;
        background-color: #ffffff;
        border-radius: 10px;
        .use-var(background-color, background-color);
        .use-dark-mode-query({
          background-color: var(--dark-background-color) !important;
        });
      }
    }
  }

  .delivery-item-hint-right-text {
    color: #666666;
    font-size: 13px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 200px;
    display: inline-block;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.popupBottomShow {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom); 
  border-radius: 7px 7px 0 0;
  background-color: #f8f9fa;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
  display: flex;
  flex-direction: column;

  &-head {
    margin: 10px 0;
    display: flex;

    &-left {
      display: flex;
      align-items: center;

      &-icon {
        margin-left: 16px;
        width: 18px;
        height: 18px;
        .use-var(color, font-color);
        .use-dark-mode-query({
          color: @dark-font-color;
        });
      }
    }
    &-title {
      font-weight: bold;
      padding-left: 52px;
      text-align: center;
      flex: 1;
      font-size: 15px;
      color: #333333;
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: @dark-font-color;
      });
    }

    &-right {
      width: 52px;
      margin-right: 15px;
      font-size: 13px;
      .use-var(color, primary-color);
      .use-dark-mode-query({
        color: var(--dark-primary-color) !important;
      });
    }
  }

  &-content {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    background-color: #f7f7f7;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
      background-color: var(--dark-cell-background-color) !important;
    });
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    max-height: 250px;
    overflow: auto;
    

    &-textarea {
      border: none;
      min-height: 140px;
      margin: 0px;
      width: 100%;
      display: block;
      padding: 0px;
      font-size: 13px;
      color: #333333;
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: @dark-font-color;
      });
      background-color: var(--page-primary-background-color);
      .use-dark-mode-query({
        background-color: var(--dark-cell-background-color) !important;
      });
      word-break: break-all;
      white-space: pre-wrap;
    }
  }

  &-bottom {
    // margin: 7px 15px 15px 15px;
    padding: 16px;
    align-items: center;
    display: flex;
    justify-content: center;
    
    &-btn {
      width: 100%;
      height: 41px;
      border-radius: 6px;
      //background-color: #6cbe70;
      color: #ffffff;
      font-size: 16px;
      border: none;

      &-text {
        font-size: 16px;
        color: #ffffff;
      }
    }
    

  }
}
