import React, { useState, useEffect } from 'react';
import Taro, { useRouter } from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';
import { Toast, Cell, Button } from '@arco-design/mobile-react';
import './emsArea.less';
import YkNavBar from '@/components/ykNavBar';

import {IconCheck, IconAdd, IconCircleChecked, IconCircleUnchecked, IconDelete, IconEdit, IconCircleDisabled} from '@arco-design/mobile-react/esm/icon';


interface AreaItem {
  name: string;
}

interface ListItem {
  parent: string;
  name: string;
  status: number; // 0 未选中 1选中 2不可点击
}

const EmsArea: React.FC = () => {
  const router = useRouter();
  const [showPage, setShowPage] = useState(false);
  const [type, setType] = useState('');
  const [index, setIndex] = useState('');
  const [hasCheckList, setHasCheckList] = useState<string[]>([]);
  const [noCheckList, setNoCheckList] = useState<string[]>([]);
  const [toastVisible, setToastVisible] = useState(false);
  const [toastText, setToastText] = useState('');

  const [listP] = useState<AreaItem[]>([
    { name: '华东地区' },
    { name: '华北地区' },
    { name: '华中地区' },
    { name: '华南地区' },
    { name: '东北地区' },
    { name: '西北地区' },
    { name: '西南地区' },
    { name: '港澳台地区' }
  ]);

  const [list, setList] = useState<ListItem[]>([
    { parent: '华东地区', name: '上海市', status: 0 },
    { parent: '华东地区', name: '江苏省', status: 0 },
    { parent: '华东地区', name: '浙江省', status: 0 },
    { parent: '华东地区', name: '安徽省', status: 0 },
    { parent: '华东地区', name: '江西省', status: 0 },
    { parent: '华北地区', name: '北京市', status: 0 },
    { parent: '华北地区', name: '天津市', status: 0 },
    { parent: '华北地区', name: '山西省', status: 0 },
    { parent: '华北地区', name: '山东省', status: 0 },
    { parent: '华北地区', name: '河北省', status: 0 },
    { parent: '华北地区', name: '内蒙古自治区', status: 0 },
    { parent: '华中地区', name: '湖南省', status: 0 },
    { parent: '华中地区', name: '湖北省', status: 0 },
    { parent: '华中地区', name: '河南省', status: 0 },
    { parent: '华南地区', name: '广东省', status: 0 },
    { parent: '华南地区', name: '广西壮族自治区', status: 0 },
    { parent: '华南地区', name: '福建省', status: 0 },
    { parent: '华南地区', name: '海南省', status: 0 },
    { parent: '东北地区', name: '辽宁省', status: 0 },
    { parent: '东北地区', name: '吉林省', status: 0 },
    { parent: '东北地区', name: '黑龙江省', status: 0 },
    { parent: '西北地区', name: '陕西省', status: 0 },
    { parent: '西北地区', name: '新疆维吾尔自治区', status: 0 },
    { parent: '西北地区', name: '甘肃省', status: 0 },
    { parent: '西北地区', name: '宁夏回族自治区', status: 0 },
    { parent: '西北地区', name: '青海省', status: 0 },
    { parent: '西南地区', name: '重庆市', status: 0 },
    { parent: '西南地区', name: '云南省', status: 0 },
    { parent: '西南地区', name: '贵州省', status: 0 },
    { parent: '西南地区', name: '西藏自治区', status: 0 },
    { parent: '西南地区', name: '四川省', status: 0 },
    { parent: '港澳台地区', name: '香港特别行政区', status: 0 },
    { parent: '港澳台地区', name: '澳门特别行政区', status: 0 },
    { parent: '港澳台地区', name: '台湾省', status: 0 }
  ]);

  useEffect(() => {
    setTimeout(() => {
      setShowPage(true);
    }, 200);

    const { type, index, noCheckList, hasCheckList } = router.params;
    
    if (type) {
      setType(type);
    }
    
    if (index) {
      setIndex(index);
    }
    
    if (noCheckList) {
      const noCheckArr = JSON.parse(decodeURIComponent(noCheckList));
      setNoCheckList(noCheckArr);
      
      setList(prevList => {
        const newList = [...prevList];
        noCheckArr.forEach(item1 => {
          const matchedIndex = newList.findIndex(item2 => item2.name === item1);
          if (matchedIndex !== -1) {
            newList[matchedIndex].status = 2;
          }
        });
        return newList;
      });
    }
    
    if (hasCheckList) {
      const hasCheckArr = JSON.parse(decodeURIComponent(hasCheckList));
      setHasCheckList(hasCheckArr);
      
      setList(prevList => {
        const newList = [...prevList];
        hasCheckArr.forEach(item1 => {
          const matchedIndex = newList.findIndex(item2 => item2.name === item1);
          if (matchedIndex !== -1) {
            newList[matchedIndex].status = 1;
          }
        });
        return newList;
      });
    }
  }, []);

  // const getNavigateBack = () => {
  //   Taro.navigateBack({ delta: 1 });
  // };

  const next = () => {
    if (type === '2') {
      // 不配送地区
      Taro.eventCenter.trigger('editNoAddress', hasCheckList.join(','));
      Taro.navigateBack({ delta: 1 });
    } else if (type === '1') {
      // 编辑地区
      Taro.eventCenter.trigger('editAddress', index, hasCheckList.join(','));
      Taro.navigateBack({ delta: 1 });
    } else if (type === '3') {
      // 新增
      const item = {
        type: '固定运费',
        price: '8',
        region: hasCheckList.join(','),
        conditionMoney: '0'
      };
      Taro.navigateTo({url: `/pages/orderSetting/emsTemplate/emsRule?type=3&item=${JSON.stringify(item)}`});
    }
  };

  const getAllChange = () => {
    const areAllInNot = list.every(obj2 => noCheckList.some(obj1 => obj1 === obj2.name));
    if (areAllInNot) {
      return;
    }

    const newArray = [...noCheckList, ...hasCheckList];
    const areAllInArrays = list.every(obj2 => newArray.some(obj1 => obj1 === obj2.name));

    if (areAllInArrays) {
      // 全部取消选中
      setList(prevList => {
        const newList = [...prevList];
        newList.forEach(childItem => {
          if (childItem.status !== 2) {
            childItem.status = 0;
          }
        });
        return newList;
      });
      
      setHasCheckList(prevList => {
        return prevList.filter(item => 
          list.find(listItem => listItem.name === item && listItem.status === 2)
        );
      });
    } else {
      // 全部选中
      setList(prevList => {
        const newList = [...prevList];
        newList.forEach(childItem => {
          if (childItem.status !== 2) {
            childItem.status = 1;
          }
        });
        return newList;
      });
      
      setHasCheckList(prevList => {
        const newList = [...prevList];
        list.forEach(childItem => {
          if (childItem.status !== 2 && !newList.includes(childItem.name)) {
            newList.push(childItem.name);
          }
        });
        return newList;
      });
    }
  };

  const getAreaIcon = (item: AreaItem) => {
    const arr = list.filter(findItem => item.name === findItem.parent);

    const areAllInNot = arr.every(obj2 => noCheckList.some(obj1 => obj1 === obj2.name));
    const newArray = [...noCheckList, ...hasCheckList];
    const areAllInArrays = arr.every(obj2 => newArray.some(obj1 => obj1 === obj2.name));

    if (areAllInNot) {
      return 'disabled';
    } else {
      if (areAllInArrays) {
        return 'checked';
      } else {
        return 'unchecked';
      }
    }
  };

  const getAddressIcon = (item: ListItem) => {
    if (item.status === 2) {
      return 'disabled';
    } else if (item.status === 1) {
      return 'checked';
    } else {
      return 'unchecked';
    }
  };

  const checkArea = (item: AreaItem) => {
    const arr = list.filter(findItem => item.name === findItem.parent);

    const areAllInNot = arr.every(obj2 => noCheckList.some(obj1 => obj1 === obj2.name));
    if (areAllInNot) {
      return;
    }

    const newArray = [...noCheckList, ...hasCheckList];
    const areAllInArrays = arr.every(obj2 => newArray.some(obj1 => obj1 === obj2.name));

    if (areAllInArrays) {
      // 取消选择区域下所有项
      setList(prevList => {
        const newList = [...prevList];
        arr.forEach(childItem => {
          const idx = newList.findIndex(item => item.name === childItem.name);
          if (idx !== -1 && newList[idx].status !== 2) {
            newList[idx].status = 0;
          }
        });
        return newList;
      });
      
      setHasCheckList(prevList => {
        return prevList.filter(item => !arr.some(childItem => childItem.name === item));
      });
    } else {
      // 选择区域下所有项
      setList(prevList => {
        const newList = [...prevList];
        arr.forEach(childItem => {
          const idx = newList.findIndex(item => item.name === childItem.name);
          if (idx !== -1 && newList[idx].status !== 2) {
            newList[idx].status = 1;
          }
        });
        return newList;
      });
      
      setHasCheckList(prevList => {
        const newList = [...prevList];
        arr.forEach(childItem => {
          if (childItem.status !== 2 && !newList.includes(childItem.name)) {
            newList.push(childItem.name);
          }
        });
        return newList;
      });
    }
  };

  const checkAddress = (item: ListItem) => {
    if (item.status === 2) {
      return;
    }

    const idx = hasCheckList.findIndex(findItem => findItem === item.name);
    
    setList(prevList => {
      const newList = [...prevList];
      const itemIdx = newList.findIndex(listItem => listItem.name === item.name);
      if (itemIdx !== -1) {
        newList[itemIdx].status = idx !== -1 ? 0 : 1;
      }
      return newList;
    });
    
    if (idx !== -1) {
      setHasCheckList(prevList => {
        const newList = [...prevList];
        newList.splice(idx, 1);
        return newList;
      });
    } else {
      setHasCheckList(prevList => [...prevList, item.name]);
    }
  };

  const getAllCheckIcon = () => {
    const areAllInNot = list.every(obj2 => noCheckList.some(obj1 => obj1 === obj2.name));
    if (areAllInNot) {
      return 'disabled';
    }

    const newArray = [...noCheckList, ...hasCheckList];
    const areAllInArrays = list.every(obj2 => newArray.some(obj1 => obj1 === obj2.name));
    if (areAllInArrays) {
      return 'checked';
    }

    return 'unchecked';
  };

  const renderIcon = (type: string) => {
    switch (type) {
      case 'checked':
        return <IconCircleChecked  className='ems-area-icon-checked'/>;
      case 'unchecked':
        return <IconCircleUnchecked  className='ems-area-icon-unchecked'/>;
      case 'disabled':
        return <IconCircleChecked  className='ems-area-icon-disabled'/>;
      default:
        return <IconCircleUnchecked  className='ems-area-icon-unchecked'/>;
    }
  };

  return (
    <View className="ems-area">
      <YkNavBar title="选择地区"/> 
      
      <Toast
        visible={toastVisible}
        content={toastText}
        onClose={() => setToastVisible(false)}
      />
      
      <View className="address">
        {listP.map((item, idx) => (
          <View className="address-item" key={idx}>
            <View className="address-item-head" onClick={() => checkArea(item)}>
              {renderIcon(getAreaIcon(item))}
              <Text className="address-item-head-text">{item.name}</Text>
            </View>
            
            <View className="address-item-list">
              {list
                .filter(childItem => childItem.parent === item.name)
                .map((childItem, childIdx) => (
                  <View 
                    className="address-item-list-item" 
                    key={childIdx}
                    onClick={() => checkAddress(childItem)}
                  >
                    {renderIcon(getAddressIcon(childItem))}
                    <Text className="address-item-list-item-text">{childItem.name}</Text>
                  </View>
                ))
              }
            </View>
          </View>
        ))}
      </View>
      
      <View className="foot-holder"></View>
      
      <View className="footerbtn">
        <View className="footerbtn-all">
          <View className="footerbtn-all-icon" onClick={getAllChange}>
            {renderIcon(getAllCheckIcon())}
          </View>
          <View className="footerbtn-all-text">全选</View>
        </View>
        <View className="footerbtn-next">
          {(hasCheckList.length > 0 || type === '2') ? (
            <Button className="footerbtn-next-btn" onClick={next}>下一步</Button>
          ) : (
            <Button className="footerbtn-next-notbtn" disabled>下一步</Button>
          )}
        </View>
      </View>
    </View>
  );
};

export default EmsArea; 