import { View, Text } from "@tarojs/components";
import "./index.less";
import Taro from "@tarojs/taro";
import { useState, useEffect } from "react";
import { 
  Cell, 
  Dialog, 
  Loading, 
  Popup, 
  Toast, 
  Button,
  Image
} from "@arco-design/mobile-react";

import {IconCheck, IconAdd, IconCircleChecked, IconCircleUnchecked, IconDelete, IconEdit} from '@arco-design/mobile-react/esm/icon';

// 组件
import YkNavBar from "@/components/ykNavBar/index";

// API
import { 
  getFreightTemplateAll, 
  createFreightRulesBilling,
  updateFreightRulesBilling,
  deleteFreightTemplate, 
  updateFreightTemplate,
} from "@/utils/api/common/common_user";

// 前端数据模型
interface EmsRule {
  id: number;
  title: string;
  description: string;
  isCheck: boolean;
}

interface FreightTemplate {
  id: number;
  name: string;
  type: string;
  price: string;
  conditionMoney: string;
  isDefault: number | string;
  nonDeliveryArea?: string;
  rules?: FreightRule[];
}

interface FreightRule {
  id: number;
  region: string;
  type: string;
  price: string;
  conditionMoney: string;
}

// 后端数据传输对象（DTO）
interface FreightRuleDTO {
  id: number;
  userId: number;
  area: string;
  freightTemplateId: number;
  freightType: number;
  freightAmount: number;
  isFreeShippingConditions: number;
  freeShippingAmount: number;
  createTime: number;
}

interface FreightTemplateDTO {
  id: number;
  userId: number;
  name: string;
  nonDeliveryArea: string | null;
  isDefault: number;
  createTime: number;
  freightRulesRespVOList: FreightRuleDTO[];
}

interface FreightTemplateResponseDTO {
  appFreightRulesBillingRespVO: any;
  appFreightTemplateRespVOS: FreightTemplateDTO[];
}

interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

// 数据映射器
const mapFreightRuleFromDTO = (dto: FreightRuleDTO): FreightRule => {
  return {
    id: dto.id,
    region: dto.area,
    type: dto.freightType === 1 ? '包邮' : '固定运费',
    price: dto.freightAmount.toString(),
    conditionMoney: dto.freeShippingAmount.toString()
  };
};

const mapFreightTemplateFromDTO = (dto: FreightTemplateDTO): FreightTemplate => {
  // 找到全国区域的运费规则
  const defaultRule = dto.freightRulesRespVOList.find(rule => rule.area === "全国");
  
  // 将所有运费规则映射为FreightRule类型
  const allRules = dto.freightRulesRespVOList.map(mapFreightRuleFromDTO);

  return {
    id: dto.id,
    name: dto.name,
    type: defaultRule ? (defaultRule.freightType === 1 ? '包邮' : '固定运费') : '',
    price: defaultRule ? defaultRule.freightAmount.toString() : '',
    conditionMoney: defaultRule ? defaultRule.freeShippingAmount.toString() : '',
    isDefault: dto.isDefault,
    nonDeliveryArea: dto.nonDeliveryArea || undefined,
    rules: allRules
  };
};

export default function EmsTemplate() {
  const [loading, setLoading] = useState(true);
  const [templateList, setTemplateList] = useState<FreightTemplate[]>([]);
  const [rule, setRule] = useState('');
  const [templateId, setTemplateId] = useState<number>(0);
  const [emsPopupVisible, setEmsPopupVisible] = useState(false);
  const [toastConfig, setToastConfig] = useState({
    visible: false,
    content: ''
  });

  const [emsList, setEmsList] = useState<EmsRule[]>([
    {
      id: 1,
      title: '叠加运费',
      description: '不同运费模板的商品分别计费，并叠加',
      isCheck: false
    },
    {
      id: 2,
      title: '按最高商品运费计费',
      description: '不同运费模板的商品分别计费，并取运费最高的',
      isCheck: false
    }
  ]);

  const [freightRulesBillingId, setFreightRulesBillingId] = useState<number | null>(null);

  // 初始化数据
  useEffect(() => {
    fetchTemplateList();

    // 添加事件监听，监听从editEmsTemplate页面返回时的刷新事件
    Taro.eventCenter.on('refreshTemplate', () => {
      fetchTemplateList();
    });
    
    // 组件卸载时移除事件监听
    return () => {
      Taro.eventCenter.off('refreshTemplate');
    };
  }, []);

  // 获取运费模板列表
  const fetchTemplateList = () => {
    setLoading(true);
    const userInfo = Taro.getStorageSync('userInfo');
    const userId = userInfo?.userInfo?.id || userInfo?.id;

    getFreightTemplateAll({ userId })
      .then((res: ApiResponse<FreightTemplateResponseDTO>) => {
        if (res.code === 0) {
          const templateDTOs = res.data?.appFreightTemplateRespVOS || [];
          const newEmsList = [...emsList];

          // 记录运费计费规则ID
          if (res.data?.appFreightRulesBillingRespVO?.id) {
            setFreightRulesBillingId(res.data.appFreightRulesBillingRespVO.id);
          } else {
            setFreightRulesBillingId(null);
          }

          if (res.data?.appFreightRulesBillingRespVO?.type === 1) {
            newEmsList[0].isCheck = true;
            newEmsList[1].isCheck = false;
            setRule(newEmsList[0].title);
          } else {
            newEmsList[0].isCheck = false;
            newEmsList[1].isCheck = true;
            setRule(newEmsList[1].title);
          }

          setEmsList(newEmsList);
          // 使用 mapper 转换数据
          const mappedTemplates = templateDTOs.map(mapFreightTemplateFromDTO);
          setTemplateList(mappedTemplates);
        } else {
          showToast(res.msg);
        }
      })
      .catch((error) => {
        console.error('获取运费模板失败', error);
        showToast('获取运费模板失败');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 显示Toast
  const showToast = (content: string) => {
    setToastConfig({
      visible: true,
      content
    });
    setTimeout(() => {
      setToastConfig({
        visible: false,
        content: ''
      });
    }, 2000);
  };

  // 编辑/新建运费模板
  const handleEditEms = (item?: FreightTemplate) => {
    if (item) {
      Taro.navigateTo({url: `/pages/orderSetting/emsTemplate/editEmsTemplate?item=${JSON.stringify(item)}`});
    } else {
      Taro.navigateTo({url: `/pages/orderSetting/emsTemplate/editEmsTemplate`});
    }
  };

  // 打开删除确认弹窗
  const openDeleteConfirm = (item: FreightTemplate) => {
    if (templateList.length <= 1 || item.isDefault === 1 || item.isDefault === '1') {
      showToast('默认模板不可删除');
      return;
    }
    console.log("openDeleteConfirm------item.id: ", item.id);
    setTemplateId(item.id);
    
    // 使用Dialog.confirm替代
    let _window: any = window;
    _window.modalInstance = Dialog.confirm({
      title: '提示',
      children: '确认删除该运费模板吗？',
      platform: 'ios',
      okText: '删除',
      cancelText: '取消',
      onOk: () => {
        deleteFreightTemplate({ id: item.id })
          .then((res: ApiResponse<any>) => {
            if (res.code === 0) {
              showToast('模板已删除');
              fetchTemplateList();
            } else {
              showToast(res.msg);
            }
          })
          .catch((error) => {
            console.error('删除运费模板失败', error);
            showToast('删除运费模板失败');
          });
      }
    });
  };

  // 设置默认运费模板
  const handleSetDefault = (id: number) => {
    const userInfo = Taro.getStorageSync('userInfo');
    const userId = userInfo?.userInfo?.id || userInfo?.id;
    
    // 找到当前默认的模板
    const currentDefault = templateList.find(template => 
      template.isDefault === 1 || template.isDefault === '1'
    );

    // 如果当前默认模板存在且不是要设置的新默认模板，则将其设置为非默认
    const updatePromises: Promise<ApiResponse<any>>[] = [];
    if (currentDefault && currentDefault.id !== id) {
      updatePromises.push(
        updateFreightTemplate({
          id: currentDefault.id,
          userId: userId,
          isDefault: 0
        }) as Promise<ApiResponse<any>>
      );
    }

    // 设置新的默认模板
    updatePromises.push(
      updateFreightTemplate({
        id: id,
        userId: userId,
        isDefault: 1
      }) as Promise<ApiResponse<any>>
    );

    // 执行更新操作
    Promise.all(updatePromises)
      .then(() => {
        console.log('设置默认模板成功');
        // 更新本地状态
        const newTemplateList = templateList.map(template => ({
          ...template,
          isDefault: template.id === id ? 1 : 0
        }));
        setTemplateList(newTemplateList);
      })
      .catch((error) => {
        console.error('设置默认模板失败', error);
        showToast('设置默认模板失败');
      });
  };

  // 选择运费规则
  const handleSelectEmsRule = (item: EmsRule) => {
    const newEmsList = emsList.map(ems => ({
      ...ems,
      isCheck: ems.id === item.id
    }));
    
    setEmsList(newEmsList);
    setRule(item.title);
    setEmsPopupVisible(false);
    
    // 设置运费规则
    updateFreightRule(item.title === '叠加运费' ? '1' : '2');
  };

  // 更新运费规则
  const updateFreightRule = (ruleType: string) => {
    const userInfo = Taro.getStorageSync('userInfo');
    const userId = userInfo?.userInfo?.id || userInfo?.id;

    // 组装参数
    const params = {
      userId,
      type: Number(ruleType)
    };

    // 有id则更新，无id则新建
    if (freightRulesBillingId) {
      updateFreightRulesBilling({
        ...params,
        id: freightRulesBillingId
      })
        .then((res: any) => {
          if (res.code === 0) {

            //showToast('运费规则设置成功');
            //fetchTemplateList();
          } else {
            showToast(res.msg || '运费规则设置失败');
          }
        })
        .catch((error: any) => {
          console.error('运费规则设置失败', error);
          //showToast('运费规则设置失败');
        });
    } else {
      createFreightRulesBilling(params)
        .then((res: any) => {
          if (res.code === 0) {
            //showToast('运费规则设置成功');
            //fetchTemplateList();
          } else {
            showToast(res.msg || '运费规则设置失败');
          }
        })
        .catch((error: any) => {
          console.error('运费规则设置失败', error);
          showToast('运费规则设置失败');
        });
    }
  };

  // 前往配送管理页面
  const goToDeliveryManager = () => {
    Taro.navigateTo({url: '/pages/orderSetting/deliveryManager/index'});
  };

  // 返回上一页
  const handleBack = () => {
    Taro.navigateBack({
      delta: 1,
    });
  };

  // 渲染运费规则
  const renderFreightRule = (item: FreightTemplate | FreightRule) => {
    console.log("【renderFreightRule】 item: " + JSON.stringify(item));
    if (item.type === '包邮') {
      return '包邮';
    } else if (item.conditionMoney && Number(item.conditionMoney) > 0) {
      return `固定运费${item.price}元;  满${item.conditionMoney}元包邮`;
    } else if (item.price && Number(item.price) > 0) {
      return `固定运费${item.price}元`;
    } else {
      return `固定运费8元`;
    }
  };

  return (
    <View className="emsTemplatePage">
      <YkNavBar title="运费模版" rightContent={<View className="container-head-right" onClick={goToDeliveryManager}>
          <Text className="container-head-right-text">配送管理</Text>
        </View>} />
      
      {loading ? (
        <Loading />
      ) : (
        <>
        <Cell.Group bordered={false} className="ems-rule-group">
          <Cell
            label="运费规则"
            text={rule}
            showArrow
            onClick={() => setEmsPopupVisible(true)}
          />
        </Cell.Group>

        <View className="container">


          <View className="container-head">
            <Text className="container-head-left">运费模版</Text>
          </View>

          <View className="container-box">
            {Array.isArray(templateList) && templateList.map((item, index) => (
              <Cell.Group className="container-box-item" key={item.id}>
                <View>
                  <Cell 
                    className="container-box-item-cell"
                    label={<Text className="container-box-item-text">{item.name}</Text>} 
                    append={
                    <View className="container-box-item-append">
                      <View className="container-box-item-line">
                        <Text className="container-box-item-line-tv1">默认运费</Text>
                        <Text className="container-box-item-line-tv2">
                          {item.rules && item.rules.length > 0 && item.rules.find(rule => rule.region === '全国') 
                          ? renderFreightRule(item.rules.find(rule => rule.region === '全国') as FreightRule) 
                          : ''}
                        </Text>
                      </View>

                      {item.rules && Array.isArray(item.rules) && item.rules.length > 0 && (
                        <View className="container-box-item-area">
                          {item.rules.filter(rule => rule.region !== '全国').map((ruleItem, ruleIndex) => (
                            <View className="container-box-item-area-item" key={ruleIndex}>
                              <View className={`container-box-item-area-item-tv1 ${ruleIndex === 0 ? '' : 'hiddenStyle'}`}>
                                指定地区
                              </View>
                              <View className="container-box-item-area-item-tv2">
                                {`${ruleItem.region}; ${renderFreightRule(ruleItem)}`}
                              </View>
                            </View>
                          ))}
                        </View>
                      )}

                      {item.nonDeliveryArea && (
                        <View className="container-box-item-areaN">
                          <View className="container-box-item-areaN-tv1">暂不配送</View>
                          <View className="container-box-item-areaN-tv2">{item.nonDeliveryArea}</View>
                        </View>
                      )}
                    </View>
                  } />
                  {/* <Text className="container-box-item-text">{item.name}</Text> */}
                
                  {/* <View className="container-box-line"></View> */}
                  <Cell 
                    className="container-box-item-cell"
                    label={
                    <>                
                    <View className="container-box-item-line2">
                      <View className="container-box-item-line2-left">
                        {item.isDefault === 1 || item.isDefault === '1' ? (
                          <IconCircleChecked className="container-box-item-line2-left-check" onClick={() => handleSetDefault(item.id)} />
                        ) : (
                          <IconCircleUnchecked className="container-box-item-line2-left-uncheck" onClick={() => handleSetDefault(item.id)} />
                        )}
                        <Text className="container-box-item-line2-left-text">默认运费模版</Text>
                      </View>


                    </View>
                    </>
                  }>
                    <View className="container-box-item-line2-right">
                      <IconDelete
                        className="container-box-item-line2-right-del"
                        onClick={() => openDeleteConfirm(item)}
                      />
                      <View
                        className="container-box-item-line2-right-edit"
                        onClick={() => handleEditEms(item)}
                      >
                        <IconEdit className="container-box-item-line2-right-edit-img" />
                        <Text className="container-box-item-line2-right-edit-text">编辑</Text>
                      </View>
                    </View>
                  </Cell> 
              </View>
              </Cell.Group>
            ))}
          </View>
        </View>
        <View className="holder"></View>

        <View className="footerbtn">
          <Button className="footerbtn-btn" type="primary" onClick={() => handleEditEms()}>
            <IconAdd className="footerbtn-btn-icon" /> 
            <Text className="footerbtn-btn-text">新建运费模版</Text>
          </Button>
        </View>
      </>
      )}

      {/* 运费规则选择弹窗 */}
      <Popup
        visible={emsPopupVisible}
        close={() => setEmsPopupVisible(false)}
        direction="bottom"
        className="emsPop"
        contentStyle={{ borderRadius: '10px 10px 0 0' }}
      >
        {/* <View className="emsPop"> */}
          <View className="emsPop-content">
            <Cell.Group bordered={false} className="emsPop-content-group">
              {emsList.map((item) => (
                <Cell
                  key={item.id}
                  label={item.title}
                  desc={item.description}
                  className={item.isCheck ? 'emsPop__cell--checked' : ''}
                  showArrow={false}
                  onClick={() => handleSelectEmsRule(item)}
                >
                  {item.isCheck && (
                    <IconCheck className="emsPop__checkIcon" />
                  )}
                </Cell>
              ))}
            </Cell.Group>
            <View
              className="emsPop-content-cancel"
              onClick={() => setEmsPopupVisible(false)}
            >
              <Text className="emsPop-content-cancel-text">取消</Text>
            </View>
          </View>
        {/* </View> */}
      </Popup>

      {/* Toast提示 */}
      <Toast
        visible={toastConfig.visible}
        content={toastConfig.content}
      />
    </View>
  );
}