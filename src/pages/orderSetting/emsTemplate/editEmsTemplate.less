@import '@arco-design/mobile-react/style/mixin.less';

[id^="/pages/orderSetting/emsTemplate/editEmsTemplate"] {
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
}

.ems-template {
  min-height: 100vh;

  .ems-template-content {
    margin: 0px;
    padding: 10px;
    
    .name-group {
      margin: 10px 0px;
      border-radius: 4px;
      // background-color: #ffffff;
      overflow: hidden;
      .use-var(background-color, background-color);
      .use-dark-mode-query({
        background-color: @dark-cell-background-color !important;
      });
    }

    .container-group {
      margin: 10px 0px;
      padding: 16px;
      border-radius: 10px;
      // background-color: #ffffff;
      .use-var(background-color, background-color);
      .use-dark-mode-query({
        background-color: @dark-cell-background-color !important;
      });
      // background-color: var(--page-primary-background-color) !important;
      // .use-dark-mode-query({
      //   background-color: @dark-background-color !important;
      // });

      .header-cell {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 10px 0px;
        // .header-title {
        //   font-size: 30px;
        //   color: #333333;
        // }
        .header-cell-label {
            font-size: 16px;
            .use-var(color, font-color);
            .use-dark-mode-query({
              color: @dark-font-color !important;
            });
        }
        .header-btn {
          display: flex;
          align-items: center;

          &-text {
            font-size: 13px;
            .use-var(color, primary-color);
          }
        }

      }

      .default-group {
        padding: 0px;
        margin: 0;
        width: 100%;
        // background-color: #fafbfc;
        // .use-var(background-color, background-color);
        // .use-dark-mode-query({
        //   background-color: @dark-background-color !important;
        // });
        background-color: var(--page-primary-background-color) !important;
        .use-dark-mode-query({
          background-color: @dark-background-color !important;
        });
        border-radius: 4px;
      }
    }

    .rule-box {
      padding: 16px;
      // background-color: #ffffff;
      .use-var(background-color, background-color);
      .use-dark-mode-query({
        background-color: @dark-cell-background-color !important;
      });
      border-radius: 4px;
      position: relative;

      .delete-icon-box {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        top: 0;
        right: 0;
        width: 18px;
        height: 18px;
        background-color: rgba(0, 0, 0, 0.6);
        border-radius: 0 4px 0 4px;
        z-index: 100;
      }

      .delete-icon {
        width: 7px;
        height: 7px;
        color: #fff;
        font-weight: bold;
        // .use-var(color, font-color);
        // .use-dark-mode-query({
        //   color: @dark-font-color !important;
        // });
        z-index: 100;
      }
      
      .rule-group {
        position: relative;
        border-radius: 4px;
        background-color: var(--page-primary-background-color) !important;
        .use-dark-mode-query({
          background-color: @dark-background-color !important;
        });
      }
    }



    .no-address-group {
      margin: 10px 0px;
      background-color: #ffffff;
      border-radius: 14px;
    }

    .cell-content {
      display: flex;              /* 开启flex布局 */
      align-items: center;        /* 垂直居中 */
      justify-content: right;

      &-text {
        color: #666666;
        font-size: 15px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        max-width: 200px;
        display: inline-block;
      }
    }
    
    // .cell-content {
    //   display: flex;
    //   justify-content: right;
    //   align-items: center;
    //   width: 100%;

    //   // .cell-arrow {
    //   //   width: 10px;
    //   //   height: 18px;
    //   //   margin-left: 10px;
    //   // }
      
    //   // .cell-icons {
    //   //   display: flex;
    //   //   align-items: center;
    //   // }
    // }
  }

  .holder {
    min-height: 160px;
  }

  .footerbtn {
    .use-var(background-color, background-color);
    .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
  
    &-btn {
      width: 90%;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;

      &-text {
        margin-left: 10px;
        font-size: 15px;
      }
    }
  }
} 

.rule-cell {
  padding: 0px 16px;
  margin: 0px;
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
}