import React, { useState, useEffect } from 'react';
import Taro, { useRouter } from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';
import { Cell, Toast, Input, Button } from '@arco-design/mobile-react';
import { createAndFreightRules, updateFreightTemplate, createFreightRules, updateFreightRules, deleteFreightRules } from '@/utils/api/common/common_user';
import './editEmsTemplate.less';
import YkNavBar from '@/components/ykNavBar';
import { Console } from 'console';

import {IconCloseBold} from '@arco-design/mobile-react/esm/icon';

const EditEmsTemplate: React.FC = () => {
  const router = useRouter();
  const [showPage, setShowPage] = useState(false);
  const [item, setItem] = useState<any>({});
  const [templateName, setTemplateName] = useState('');
  const [templateId, setTemplateId] = useState('');
  const [list, setList] = useState<any[]>([]);
  const [toastVisible, setToastVisible] = useState(false);
  const [toastText, setToastText] = useState('');
  const isEdit = Boolean(router.params.item);

  useEffect(() => {
    setTimeout(() => {
      setShowPage(true);
    }, 200);

    const { item } = router.params;
    
    if (item) {
      // console.log("param item: " + item);
      // 先对编码的参数进行解码，再进行 JSON 解析
      const decodedItem = decodeURIComponent(item);
      const itemObj = JSON.parse(decodedItem);
      // console.log("itemObj: " + JSON.stringify(itemObj));
      setItem(itemObj);
      setList(itemObj.rules || []);
      setTemplateId(itemObj.id);
      setTemplateName(itemObj.name);
    } else {
      // 新建模板时，初始化默认的全国规则
      setList([{
        region: '全国',
        type: '固定运费',
        price: '8',
        conditionMoney: '0'
      }]);
    }
    
    // 添加事件监听
    setupEventListeners();
    
    return () => {
      // 移除事件监听
      removeEventListeners();
    };
  }, []);

  useEffect(() => {
    console.log("监测list变化【运费规则】更新: ", JSON.stringify(list));
  }, [list]);

  useEffect(() => {
    console.log("监测item变化【模版名称】更新: ", JSON.stringify(item));
  }, [item]);
  const setupEventListeners = () => {
    // Taro.eventCenter.on('editDefaultRule', (itemData) => {
    //   const itemTemp = JSON.parse(itemData);
    //   console.log("【editDefaultRule】 itemTemp: " + JSON.stringify(itemTemp));
    //   setItem(prev => ({
    //     ...prev,
    //     type: itemTemp.type,
    //     price: itemTemp.price,
    //     conditionMoney: itemTemp.conditionMoney
    //   }));
    // });

    Taro.eventCenter.on('editRule', (itemData) => {
      const itemTemp2 = JSON.parse(itemData);
      console.log("【editRule】 itemTemp: " + JSON.stringify(itemTemp2));

      setList(prevList => {
        return prevList.map(listItem => {
          if (listItem.id === itemTemp2.id) {
            return {
              ...listItem,
              type: itemTemp2.type,
              price: itemTemp2.price,
              conditionMoney: itemTemp2.conditionMoney
            };
          }
          return listItem;
        });
      });
    });

    Taro.eventCenter.on('addRule', (itemData) => {
      const itemTemp2 = JSON.parse(itemData);
      setList(prevList => [...prevList, itemTemp2]);
    });

    Taro.eventCenter.on('editAddress', (index, str) => {
      setList(prevList => {
        const newList = [...prevList];
        if (newList[index]) {
          newList[index].region = str;
        }
        return newList;
      });
    });

    Taro.eventCenter.on('editNoAddress', (str) => {
      setItem(prev => ({
        ...prev,
        nonDeliveryArea: str
      }));
    });
  };

  const removeEventListeners = () => {
    // Taro.eventCenter.off('editDefaultRule');
    Taro.eventCenter.off('editRule');
    Taro.eventCenter.off('addRule');
    Taro.eventCenter.off('editAddress');
    Taro.eventCenter.off('editNoAddress');
  };

  const getNavigateBack = () => {
    Taro.navigateBack({ delta: 1 });
  };

  const delEmsAddress = (index: number) => {
    console.log('删除指定地区运费规则 - 开始', { index, rule: list[index] });
    
    setList(prevList => {
      const newList = [...prevList];
      console.log('当前规则列表:', newList);
      
      if (newList[index]) {
        if (newList[index].id) {
          console.log('已有规则，标记为删除:', newList[index]);
          newList[index].type = '';  // 这里只是标记删除，实际删除在保存时处理
        } else {
          console.log('新增规则，直接移除:', newList[index]);
          newList.splice(index, 1);
        }
      }
      
      console.log('更新后的规则列表:', newList);
      return newList;
    });
  };

  const goToEditRule = (itemData?: any) => {
    let ruleData;
    
    if (itemData) {
      // 编辑现有规则（包括指定地区和全国默认规则）
      ruleData = itemData;
    } else {
      // 处理默认规则（全国）
      const defaultRule = list.find(rule => rule.region === '全国');
      ruleData = defaultRule || {
        ...(!isEdit ? {} : item),
        type: '固定运费',
        price: '8',
        region: '全国',
        conditionMoney: '0'
      };
    }

    Taro.navigateTo({
      url: `/pages/orderSetting/emsTemplate/emsRule?item=${JSON.stringify(ruleData)}`
    });
  };

  const goToEmsAddress = (addressType: number, index?: number) => {
    let noCheckList: string[] = [];
    let hasCheckList: string[] = [];
    
    index ? console.log("【goToEmsAddress】 index: " + index) : console.log("【goToEmsAddress】 index: undefined");
    console.log("【goToEmsAddress】 list: " + JSON.stringify(list));
    if (addressType === 1 && typeof index === 'number') { // 编辑指定配送的地区
      if (list.length > 0 && list[index] && list[index].region) {
        // 确保不会包含"全国"规则
        hasCheckList = list[index].region.split(',').filter(region => region !== '全国');
      }
      
      let filteredArray = [...list];
      if (typeof index === 'number') {
        filteredArray.splice(index, 1);
      }
      filteredArray = filteredArray.filter(rule => rule.type !== '' && rule.region !== '全国');
      
      if (item.nonDeliveryArea && item.nonDeliveryArea !== '') {
        noCheckList = item.nonDeliveryArea.split(',').concat(
          filteredArray
            .map(rule => rule.region)
            .join(',')
            .split(',')
        );
      } else {
        noCheckList = filteredArray
          .map(rule => rule.region)
          .join(',')
          .split(',');
      }
      console.log("【goToEmsAddress】 noCheckList: " + JSON.stringify(noCheckList));
      console.log("【goToEmsAddress】 hasCheckList: " + JSON.stringify(hasCheckList));
      Taro.navigateTo({url: `/pages/orderSetting/emsTemplate/emsArea?type=${addressType}&index=${index}&noCheckList=${JSON.stringify(noCheckList)}&hasCheckList=${JSON.stringify(hasCheckList)}`});
    } else if (addressType === 2) { // 编辑不配送地区
      if (list.length > 0) {
        noCheckList = list
          .filter(item => item.region !== '全国')
          .map(item => item.region)
          .join(',')
          .split(',');
      }

      if (item.nonDeliveryArea && item.nonDeliveryArea !== '') {
        hasCheckList = item.nonDeliveryArea.split(',');
      }
      console.log("【goToEmsAddress】 noCheckList: " + JSON.stringify(noCheckList));
      console.log("【goToEmsAddress】 hasCheckList: " + JSON.stringify(hasCheckList));
      Taro.navigateTo({url: `/pages/orderSetting/emsTemplate/emsArea?type=${addressType}&noCheckList=${JSON.stringify(noCheckList)}&hasCheckList=${JSON.stringify(hasCheckList)}`});
    } else { // 新增配送地区
      if (item.nonDeliveryArea && item.nonDeliveryArea !== '') {
        noCheckList = item.nonDeliveryArea.split(',').concat(
          list
            .filter(item => item.region !== '全国')
            .map(item => item.region)
            .join(',')
            .split(',')
        );
      } else {
        noCheckList = list
          .filter(item => item.region !== '全国')
          .map(item => item.region)
          .join(',')
          .split(',');
      }
      console.log("【goToEmsAddress】 noCheckList: " + JSON.stringify(noCheckList));
      console.log("【goToEmsAddress】 hasCheckList: " + JSON.stringify(hasCheckList));
      Taro.navigateTo({url: `/pages/orderSetting/emsTemplate/emsArea?type=${addressType}&noCheckList=${JSON.stringify(noCheckList)}&hasCheckList=${JSON.stringify(hasCheckList)}`});
    }
  };

  const save = () => {
    if (templateName === '') {
      setToastText('请输入模版名称');
      setToastVisible(true);
      return;
    }

    saveTemplateRequest();
  };

  const saveTemplateRequest = () => {
    const userInfo = Taro.getStorageSync('userInfo');
    const userId = userInfo?.userInfo?.id || userInfo?.id;
    
    if (!userId) {
      setToastText('获取用户信息失败');
      setToastVisible(true);
      return;
    }

    // 编辑模板
    if (isEdit) {
      console.log('开始更新模板');
      
      // 获取原始模板数据进行比对
      const originalItem = router.params.item ? JSON.parse(decodeURIComponent(router.params.item)) : null;
      console.log("【saveTemplateRequest】 originalItem: " + JSON.stringify(originalItem));
       
      // 检查模板基础信息是否有变化（名称、不配送地区）
      const templateBasicInfoChanged = 
        originalItem.name !== templateName || 
        originalItem.nonDeliveryArea !== (item.nonDeliveryArea || '');
      
      // 检查规则是否有变化
      const originalRules = originalItem?.rules || [];
      const rulesChanged = JSON.stringify(originalRules) !== JSON.stringify(list);
      
      // 如果模板基础信息和规则都没有变化，直接返回
      if (!templateBasicInfoChanged && !rulesChanged) {
        console.log('未检测到任何变更，跳过更新');
        setToastText('保存成功');
        setToastVisible(true);
        Taro.eventCenter.trigger('refreshTemplate', true);
        setTimeout(() => {
          Taro.navigateBack({ delta: 1 });
        }, 200);
        return;
      }

      // 更新规则的函数
      const updateRules = () => {
        console.log('=== 开始更新规则 ===');
        // 处理全国（默认）运费规则
        const defaultRule = {
          userId: userId,
          area: '全国',
          freightTemplateId: Number(templateId),
          freightType: item.type === '包邮' ? 1 : 2, // 修正：1包邮 2固定运费
          freightAmount: Number(item.price) || 8,
          isFreeShippingConditions: Number(item.conditionMoney) > 0 ? 1 : 2,
          freeShippingAmount: Number(item.conditionMoney) || 0
        };
        console.log('准备更新的默认规则数据:', defaultRule);

        // 找到原有的默认规则
        const existingDefaultRule = originalRules.find(r => r.area === '全国');
        console.log('找到的现有默认规则:', existingDefaultRule);
        
        // 比较规则是否发生实质性变化
        const isRuleChanged = (oldRule, newRule) => {
          const changed = oldRule.freightType !== newRule.freightType ||
                 Math.abs(oldRule.freightAmount - newRule.freightAmount) > 0.01 ||
                 oldRule.isFreeShippingConditions !== newRule.isFreeShippingConditions ||
                 Math.abs(oldRule.freeShippingAmount - newRule.freeShippingAmount) > 0.01;
          
          console.log('规则比较:', {
            oldRule,
            newRule,
            changes: {
              freightType: oldRule.freightType !== newRule.freightType,
              freightAmount: Math.abs(oldRule.freightAmount - newRule.freightAmount) > 0.01,
              isFreeShippingConditions: oldRule.isFreeShippingConditions !== newRule.isFreeShippingConditions,
              freeShippingAmount: Math.abs(oldRule.freeShippingAmount - newRule.freeShippingAmount) > 0.01
            },
            hasChanged: changed
          });
          
          return changed;
        };

        // 只在规则实际发生变化时才更新
        console.log('处理默认规则更新...');
        const defaultRulePromise = existingDefaultRule
          ? (isRuleChanged(existingDefaultRule, defaultRule)
              ? (console.log('默认规则需要更新'), updateFreightRules({ ...defaultRule, id: existingDefaultRule.id }))
              : (console.log('默认规则无变化，跳过更新'), Promise.resolve()))
          : (console.log('未找到默认规则，创建新规则'), createFreightRules(defaultRule));

        return defaultRulePromise
          .then(() => {
            console.log('=== 默认规则处理完成，开始处理其他规则 ===');
            // 找出需要删除的规则（排除全国规则）
            const toDeleteRules = originalRules.filter(originalRule => {
              if (originalRule.area === '全国') return false;
              const newRule = list.find(newRule => newRule.id === originalRule.id);
              const shouldDelete = !newRule || newRule.type === '';
              console.log('检查规则是否需要删除:', {
                rule: originalRule,
                shouldDelete,
                reason: shouldDelete ? (!newRule ? '规则不存在' : '规则类型为空') : '保留规则'
              });
              return shouldDelete;
            });
            console.log('需要删除的规则:', toDeleteRules);
            
            // 删除规则的 Promise 数组
            const deletePromises = toDeleteRules.map(rule => 
              rule.id ? deleteFreightRules({ id: rule.id }) : Promise.resolve()
            );

            // 找出需要新增和更新的规则（排除全国规则）
            const rulesToProcess = list.filter(rule => rule.type && rule.area !== '全国');
            console.log('需要处理的非默认规则:', rulesToProcess);

            const rulePromises = rulesToProcess.map(rule => {
                const ruleData = {
                  userId: userId,
                  area: rule.region || '',
                  freightTemplateId: Number(templateId),
                  freightType: rule.type === '包邮' ? 1 : 2,
                  freightAmount: Number(rule.price) || 8,
                  isFreeShippingConditions: Number(rule.conditionMoney) > 0 ? 1 : 2,
                  freeShippingAmount: Number(rule.conditionMoney) || 0
                };

                // 只有当规则发生变化时才更新
                const originalRule = originalRules.find(r => r.id === rule.id);
                console.log('处理规则:', {
                  rule,
                  ruleData,
                  originalRule,
                  action: rule.id && originalRule ? '可能更新' : '新建'
                });

                if (rule.id && originalRule) {
                  const needsUpdate = isRuleChanged(originalRule, ruleData);
                  console.log('规则更新判断:', {
                    ruleId: rule.id,
                    needsUpdate,
                  });
                  return needsUpdate
                    ? updateFreightRules({ ...ruleData, id: rule.id })
                    : Promise.resolve();
                } else {
                  console.log('创建新规则:', ruleData);
                  return createFreightRules(ruleData);
                }
              });

            return Promise.all([...deletePromises, ...rulePromises]);
          });
      };

      // 根据变化情况选择更新流程
      const updateProcess = templateBasicInfoChanged
        ? updateFreightTemplate({
            id: Number(templateId),
            userId: userId,
            name: templateName,
            nonDeliveryArea: item.nonDeliveryArea || '',
            isDefault: originalItem.isDefault
          }).then(() => {
            if (rulesChanged) {
              return updateRules().then(() => {});
            }
            return Promise.resolve();
          })
        : rulesChanged ? updateRules().then(() => {}) : Promise.resolve();

      updateProcess
        .then(() => {
          console.log('所有更新处理完成');
          setToastText('保存成功');
          setToastVisible(true);
          Taro.eventCenter.trigger('refreshTemplate', true);
          setTimeout(() => {
            Taro.navigateBack({ delta: 1 });
          }, 1000);
        })
        .catch(error => {
          console.error('更新失败:', error);
          setToastText('更新失败，请重试');
          setToastVisible(true);
        });
      return;
    }

    // 新增模板
    const rulesList = list
      .filter(item => item.region !== '全国')
      .map(item => {
        return {
          userId: userId,
          area: item.region || '',
          freightType: item.type === '包邮' ? 1 : 2, // 1包邮 2固定运费
          freightAmount: parseInt(item.price) || 8,
          isFreeShippingConditions: parseInt(item.conditionMoney) > 0 ? 1 : 2, // 1开启 2关闭
          freeShippingAmount: parseInt(item.conditionMoney) || 0
        };
      });

    // 添加默认规则（全国）
    rulesList.push({
      userId: userId,
      area: '全国',
      freightType: item.type === '包邮' ? 1 : 2, // 1包邮 2固定运费
      freightAmount: parseInt(item.price) || 8,
      isFreeShippingConditions: parseInt(item.conditionMoney) > 0 ? 1 : 2, // 1开启 2关闭
      freeShippingAmount: parseInt(item.conditionMoney) || 0
    });

    const formData = {
      appFreightTemplateSaveReqVO: {
        userId: userId,
        name: templateName,
        isDefault: 0,
        nonDeliveryArea: item.nonDeliveryArea || ''
      },
      appFreightRulesSaveReqVOList: rulesList
    };

    createAndFreightRules(formData)
      .then((res: any) => {
        if (res.code === 0) {
          setToastText('模版创建成功');
          setToastVisible(true);
          Taro.eventCenter.trigger('refreshTemplate', true);
          setTimeout(() => {
            Taro.navigateBack({ delta: 1 });
          }, 1000);
        } else {
          setToastText(res.msg || '创建失败');
          setToastVisible(true);
        }
      })
      .catch(error => {
        console.error('创建运费模板失败:', error);
        setToastText('创建失败，请重试');
        setToastVisible(true);
      });
  };

  const renderRuleText = (ruleItem) => {
    console.log("【renderRuleText】 ruleItem: " + JSON.stringify(ruleItem));
    
    if (!ruleItem) {
      return '';  // 默认显示
    }

    if (ruleItem.type === '包邮') {
      return '包邮';
    } else if (ruleItem.conditionMoney > 0 && ruleItem.conditionMoney !== '') {
      return `固定运费${ruleItem.price}元; 满${ruleItem.conditionMoney}元包邮`;
    } else if (ruleItem.price > 0 && ruleItem.price !== '') {
      return `固定运费${ruleItem.price}元`;
    } else {
      return `固定运费8元`;
    }
  };

  return (
    <View className="ems-template">
      <YkNavBar title={isEdit ? '编辑运费模板' : '新建运费模板'}/> 

      <Toast
        visible={toastVisible}
        content={toastText}
        onClose={() => setToastVisible(false)}
      />
      <View className="ems-template-content">
        <Cell.Group bordered={false} className="name-group">
          <Cell label="模版名称">
            <Input
              border="none"
              className="name-input"
              value={templateName}
              onChange={(_, value) => setTemplateName(value)}
              placeholder="请输入模版名称"
              maxLength={16}
            />
          </Cell>
        </Cell.Group>

        <View className="container-group">
          <View className="header-cell">
            <Text className="header-cell-label">运费规则</Text>
            <View className="header-flex">
              {/* <Text className="header-title">运费规则</Text> */}
              <View className="header-btn" onClick={() => goToEmsAddress(3)}>
              <span  className="header-btn-text">+ 新增指定地区</span>
              </View>
            </View>
          </View>
          <Cell.Group className="default-group" bordered={false}>
            <Cell className="rule-cell" label="所在地区">
              <Text>默认全国（指定区域外）</Text>
            </Cell>
            <Cell
              className="rule-cell"
              label="运费规则"
              onClick={() => {
                if (!isEdit) {
                  goToEditRule();
                } else {
                  const defaultRule = list.find(rule => rule.region === '全国');
                  console.log("【renderRuleText】 defaultRule: " + JSON.stringify(defaultRule));
                  goToEditRule(defaultRule);
                }
              }}
              showArrow
            >
              <View className="cell-content">
                <Text>{renderRuleText(list.find(rule => rule.region === '全国'))}</Text>
                {/* <Image 
                className="cell-arrow" 
                src={require('@/assets/images/common/right_arrow.png')} 
              /> */}
              </View>
            </Cell>
          </Cell.Group>
        </View>

        {list.filter(rule => rule.region !== '全国').map((listItem, idx) => (
            <View className="rule-box" key={idx}>
              {/* <Image
                className="delete-icon"
                src={require('@/assets/images/common/uploadPic_del.png')}
                onClick={(e) => {
                  e.stopPropagation();
                  delEmsAddress(idx);
                }}
              /> */}
              <View className="delete-icon-box">
              <IconCloseBold className="delete-icon" onClick={(e) => {
                    e.stopPropagation();
                    delEmsAddress(idx);
                  }} />
              </View>
              <Cell.Group bordered={false} className="rule-group" key={idx}>
                <Cell
                  className="rule-cell" 
                  label="所在地区" 
                  onClick={() => goToEmsAddress(1, idx)}
                  showArrow
              >
                <View className="cell-content">
                  <Text className='cell-content-text'>{listItem.region}</Text>
                </View>
              </Cell>
              <Cell 
                className="rule-cell"
                label="运费规则" 
                onClick={() => goToEditRule(listItem)}
                showArrow
              >
                <View className="cell-content">
                  <Text className='cell-content-text'>{renderRuleText(listItem)}</Text>

                    {/* <Image 
                      className="cell-arrow" 
                      src={require('@/assets/images/common/right_arrow.png')} 
                    /> */}
                </View>
              </Cell>
            </Cell.Group>
            </View>
          )
        )}

        <Cell.Group bordered={false} className="no-address-group">
          <Cell 
            label="不配送地区" 
            onClick={() => goToEmsAddress(2)}
            showArrow 
          >
            <View className="cell-content">
              <Text className='cell-content-text'>{item.nonDeliveryArea || ''}</Text>
            </View>
          </Cell>
        </Cell.Group>
      </View>

      <View className="holder"></View>

      <View className="footerbtn">
        <Button type="primary" className="footerbtn-btn" onClick={save}>
          <Text className="footerbtn-btn-text">保存</Text>
        </Button>
      </View>
    </View>
  );
};

export default EditEmsTemplate; 