import React, { useState, useEffect, useRef } from 'react';
import Taro, { useRouter } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { Cell, Toast, Switch, Input, Button } from '@arco-design/mobile-react';
import './emsRule.less';
import YkNavBar from '@/components/ykNavBar';

const EmsRule: React.FC = () => {
  const router = useRouter();
  const [showPage, setShowPage] = useState(false);
  const [curType, setCurType] = useState(2);
  const [area, setArea] = useState('');
  const [isConditional, setIsConditional] = useState(false);
  const [freight, setFreight] = useState('8');
  const [price, setPrice] = useState('');
  const [item, setItem] = useState<any>({});
  const [toastVisible, setToastVisible] = useState(false);
  const [toastText, setToastText] = useState('');
  const platformRef = useRef('H5');
  const isDefaultRule = item?.region === '全国';

  useEffect(() => {
    setTimeout(() => {
      setShowPage(true);
    }, 200);

    const { item } = router.params;
    
    if (item) {
      const itemObj = JSON.parse(decodeURIComponent(item));
      // console.log("itemObj: " + JSON.stringify(itemObj));
      setItem(itemObj);
      setArea(itemObj.region || '');

      if (itemObj.type === '包邮') {
        setCurType(1);
      } else {
        setCurType(2);
        setFreight(itemObj.price || '8');
        if (itemObj.conditionMoney && itemObj.conditionMoney !== '' && Number(itemObj.conditionMoney) > 0) {
          setIsConditional(true);
          setPrice(itemObj.conditionMoney);
        }
      }
    }

    const platformInfo = Taro.getSystemInfoSync().platform.toLowerCase();
    // console.log("[配送管理] 平台信息:", JSON.stringify(Taro.getSystemInfoSync()));

    if (platformInfo.includes('android')) {
      platformRef.current = 'android';
    } else if (platformInfo.includes('ios')) {
      platformRef.current = 'ios';
    } else {
      platformRef.current = 'ios';
    }

  }, []);

  const getNavigateBack = () => {
    Taro.navigateBack({ delta: 1 });
  };

  const changeType = (newType: number) => {
    setCurType(newType);
  };

  const toggleConditional = (checked: boolean) => {
    setIsConditional(checked);
  };

  const onPriceInput = (e: any, inputType: number) => {
    let sanitizedValue = e.target.value.replace(/[^\d.]/g, '').replace(/^(\d*\.\d{2}).*$/, '$1');

    let [integerPart, decimalPart] = sanitizedValue.split('.');
    if (integerPart && integerPart.length > 6) {
      integerPart = integerPart.slice(0, 6);
    }
    if (decimalPart && decimalPart.length > 2) {
      decimalPart = decimalPart.slice(0, 2);
    }

    const finalValue = decimalPart ? `${integerPart}.${decimalPart}` : integerPart;

    if (inputType === 1) {
      setFreight(finalValue);
    } else {
      setPrice(finalValue);
    }
  };

  const saveRule = () => {
    console.log("[运费规则] 保存开始");

    try {
      // 验证运费规则
      if (curType === 2) {
        if (freight === '') {
          setToastText('运费不能为空');
          setToastVisible(true);
          return;
        }

        if (Number(freight) < 0.01 || Number(freight) > 5000) {
          setToastText('可设置的运费：0.01-5000.00元');
          setToastVisible(true);
          return;
        }

        if (isConditional && (price === '' || Number(price) <= 0)) {
          setToastText('请输入有效的订单金额');
          setToastVisible(true);
          return;
        }
      }

      // 构建规则数据
      const newItem = {
        ...item,
        type: curType === 1 ? '包邮' : '固定运费',
        price: curType === 1 ? '0' : freight,
        conditionMoney: curType === 2 && isConditional ? price : '0'
      };

      // 根据情况触发事件
      const eventName = item.id ? 'editRule' : 'addRule';
      const delta = item.id ? 1 : 2;

      console.log(`[运费规则] 触发${eventName}事件，数据:`, JSON.stringify(newItem));
      Taro.eventCenter.trigger(eventName, JSON.stringify(newItem));
      Taro.navigateBack({ delta });

    } catch (error) {
      console.error("[运费规则] 保存失败:", error);
      setToastText('保存失败，请重试');
      setToastVisible(true);
    }
  };

  return (
    <View className="ems-rule">
      <YkNavBar title={isDefaultRule ? '默认运费规则' : '指定地区运费规则'} />

      <Toast
        visible={toastVisible}
        content={toastText}
        onClose={() => setToastVisible(false)}
      />

      <View className="rule-container">
        {/* 暂时注释掉区域 */}
        {/* {!isDefaultRule && (
          <Cell.Group bordered={false} className="area-group">
            <Cell label="区域">
              <Text>{area}</Text>
            </Cell>
          </Cell.Group>
        )} */}

        <Cell.Group className="rule-group" bordered={false}>
          <Cell className='rule-tab-cell'
            append={
              curType === 2 && (
                <View className="form-row">
                  <Text className="form-label">运费金额</Text>
                  <View className="input-container">
                    <Input
                      border="none"
                      className="price-input"
                      value={freight}
                      type="number"
                      maxLength={7}
                      inputStyle={{ textAlign: 'center' }}
                      onChange={(e) => onPriceInput(e, 1)}
                      clearable={false}
                    />
                    <Text className="unit">元</Text>
                  </View>
                </View>
              )
            }
          >
            <View className="tab-container">
              <View
                className={`tab-item ${curType === 1 ? 'tab-active' : ''}`}
                onClick={() => changeType(1)}
              >
                <Text>包邮</Text>
              </View>
              <View
                className={`tab-item ${curType === 2 ? 'tab-active' : ''}`}
                onClick={() => changeType(2)}
              >
                <Text>固定运费</Text>
              </View>
            </View>
          </Cell>
          {curType === 2 && (
            <Cell 
              label={<Text className="form-cell-label">条件包邮</Text>} 
              className='rule-tab-cell'
              append={isConditional && (
                <View className="form-row conditional-row">
                  <Text className="form-label">订单金额满</Text>
                  <View className="input-container">
                    <Input
                      border="none"
                      className="price-input"
                      value={price}
                      type="number"
                      onChange={(e) => onPriceInput(e, 2)}
                    />
                    <Text className="unit">元</Text>
                  </View>
                  <Text className="note">包邮（不含运费）</Text>
                </View>
              )}
            >
              <Switch
                platform={platformRef.current}
                checked={isConditional}
                onChange={toggleConditional}
              />
            </Cell>
          )}
        </Cell.Group>
      </View>

      <View className="holder"></View>

      <View className="footer">
        <Button type="primary" className="footer-save-btn" onClick={saveRule}>
          保存
        </Button>
      </View>
    </View>
  );
};

export default EmsRule; 