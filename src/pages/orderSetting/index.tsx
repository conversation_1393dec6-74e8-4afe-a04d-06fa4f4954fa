import { View, Text } from "@tarojs/components";
import "./index.less";
import { Cell } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

export default function OrderSetting() {
  // 前往配送方式页面
  const goToDeliveryManager = () => {
    Taro.navigateTo({
      url: "/pages/orderSetting/deliveryManager/index",
    });
  };

  // 前往运费模版页面
  const goToEmsTemplate = () => {
    Taro.navigateTo({
      url: "/pages/orderSetting/emsTemplate/index",
    });
  };

  // 返回上一页
  const handleBack = () => {
    console.log("OrderSetting PAGES -------: " + JSON.stringify(Taro.getCurrentPages()));
    Taro.navigateBack({
      delta: 1,
    });
  };

  return (
    <View className="orderSettingPage">
      <YkNavBar title="交易设置" onClickLeft={handleBack}/>

      <View className="container">
        <Text className="container-title">店铺</Text>

        <View className="container-box">
        <Cell.Group bordered={false}>
            <Cell
              label="配送方式"
              className="container-box-cell"
              showArrow
              onClick={goToDeliveryManager}
            />

            <Cell
              label="运费模版"
              className="container-box-cell"
              showArrow
              onClick={goToEmsTemplate}
            />
            </Cell.Group>
        </View>
      </View>
    </View>
  );
} 