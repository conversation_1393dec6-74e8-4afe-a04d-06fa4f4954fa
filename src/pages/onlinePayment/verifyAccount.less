@import '@arco-design/mobile-react/style/mixin.less';

.verify-account-page {
  min-height: 100vh;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .content {
    flex: 1;
    padding: 0 16px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
      margin-top: 50px;
      font-size: 15px;
      font-weight: bold;
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: @dark-font-color;
      });
      line-height: 1.4;
      text-align: center;


      .name {
        .use-var(color, primary-color);
        .use-dark-mode-query({
          color: @dark-primary-color;
        });
      }
    }

    .qrcode-container {
      margin-top: 30px;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;

      .qrcode-wrapper {
        flex: 1;
        .use-var(background-color, background-color);
        .use-dark-mode-query({
          background-color: @dark-background-color;
        });
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .action-buttons {
        margin-top: 15px;
        display: flex;
        justify-content: center;
        gap: 10px;

        .action-btn {
          font-size: 14px;
          .use-var(color, primary-color);
          .use-dark-mode-query({
            color: @dark-primary-color;
          });
          padding: 0;
          cursor: pointer;
          transition: opacity 0.2s;
          text-align: center;

          &:active {
            opacity: 0.8;
          }
        }
      }
    }
  }

  .bottom-button {
    width: 150px;
    padding: 16px;
    margin-bottom: 40px;
    .use-var(background-color, background-color);   
    .use-dark-mode-query({
      background-color: @dark-background-color;
    });

    // :global(.arco-button) {
    //   border-radius: 22px;
    //   height: 44px;
    //   font-size: 15px;
    //   font-weight: normal;
    // }
  }
}
