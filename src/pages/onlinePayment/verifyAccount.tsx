import React, { useState, useEffect } from 'react';
import { View, Text } from '@tarojs/components';
import { Button } from '@arco-design/mobile-react';
import { QRCodeSVG } from 'qrcode.react';
import Taro, { useRouter } from '@tarojs/taro';
import html2canvas from 'html2canvas';
import YkNavBar from '@/components/ykNavBar';
import BottomPopup from '@/components/BottomPopup';
import './verifyAccount.less';

interface VerifyAccountProps {
  url?: string;
}

const VerifyAccount: React.FC<VerifyAccountProps> = () => {
  const router = useRouter();
  const [qrcodeUrl, setQrcodeUrl] = useState<string>('');
  const [qrcodeImage, setQrcodeImage] = useState<string>('');
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const [isActionPopupVisible, setIsActionPopupVisible] = useState<boolean>(false);
  const userInfo = Taro.getStorageSync('userInfo') || {};

  useEffect(() => {
    const { url } = router.params;
    if (url) {
      try {
        // 确保URL被正确解码
        const decodedUrl = decodeURIComponent(url);
        console.log('[扫码验证] 收到验证URL:', decodedUrl);
        setQrcodeUrl(decodedUrl);
      } catch (error) {
        console.error('[扫码验证] URL解码失败:', error);
        Taro.showToast({
          title: 'URL格式错误',
          icon: 'error'
        });
      }
    } else {
      console.error('[扫码验证] 未收到验证URL');
      Taro.showToast({
        title: '缺少验证链接',
        icon: 'error'
      });
    }
  }, [router.params]);

  // 将二维码元素转换为图片
  const convertToImage = () => {
    Taro.showLoading({ title: '生成图片中...' });
    
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      const element = document.getElementById('qrcode-content');
      if (!element) {
        console.error('找不到二维码容器元素');
        Taro.hideLoading();
        return;
      }
      
      html2canvas(element, {
        useCORS: true,
        scale: 2,
        logging: false
      }).then(canvas => {
        const base64Image = canvas.toDataURL('image/png');
        setQrcodeImage(base64Image);
        Taro.hideLoading();
        setShowPreview(true);
      }).catch(error => {
        console.error('转换图片失败', error);
        Taro.hideLoading();
        Taro.showToast({
          title: '生成图片失败',
          icon: 'none'
        });
      });
    }
  };

  // 保存图片到相册
  const saveImageToAlbum = () => {
    if (!qrcodeImage) {
      convertToImage();
      return;
    }

    Taro.showLoading({ title: '正在保存...' });

    if (Taro.getEnv() === Taro.ENV_TYPE.WEB) {
      try {
        const link = document.createElement('a');
        link.download = `qrcode_${Date.now()}.png`;
        link.href = qrcodeImage;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        Taro.hideLoading();
        Taro.showToast({
          title: '图片已准备，请确认下载',
          icon: 'none',
          duration: 2000
        });
      } catch (error) {
        Taro.hideLoading();
        Taro.showToast({
          title: '保存失败，请长按图片手动保存',
          icon: 'none',
          duration: 2000
        });
      }
    } else {
      try {
        const base64Data = qrcodeImage.split(',')[1];
        const fsm = Taro.getFileSystemManager();
        const filePath = `${Taro.env.USER_DATA_PATH}/qrcode_${Date.now()}.png`;
        
        fsm.writeFile({
          filePath,
          data: base64Data,
          encoding: 'base64',
          success: () => {
            Taro.saveImageToPhotosAlbum({
              filePath: filePath,
              success: () => {
                Taro.hideLoading();
                Taro.showToast({
                  title: '保存成功',
                  icon: 'success'
                });
              },
              fail: () => {
                Taro.hideLoading();
                Taro.showToast({
                  title: '保存失败',
                  icon: 'none'
                });
              }
            });
          },
          fail: () => {
            Taro.hideLoading();
            Taro.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        Taro.hideLoading();
        Taro.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    }
  };

  // 处理底部弹出菜单操作
  const handleActionConfirm = (index: number) => {
    setIsActionPopupVisible(false);
    switch (index) {
      case 0: // 转发给法人
        Taro.showShareMenu({
          withShareTicket: true
        });
        Taro.showToast({
          title: '请点击右上角分享',
          icon: 'none',
          duration: 2000
        });
        break;
      case 1: // 保存图片
        saveImageToAlbum();
        break;
      default:
        break;
    }
  };

  return (
    <View className='verify-account-page'>
      <YkNavBar title='扫码验证' />
      
      <View className='content' id='qrcode-content'>
        <Text className='title'>
          请"<Text className='name'>{userInfo.name || '未知用户'}</Text>"微信扫码验证
        </Text>

        <View className='qrcode-container'>
          <View className='qrcode-wrapper'>
            {qrcodeUrl && (
              <QRCodeSVG
                value={qrcodeUrl}
                size={150}
                level='H'
                includeMargin={false}
              />
            )}
          </View>

          <View className='action-buttons'>
            <Text className='action-btn' onClick={() => handleActionConfirm(0)}>
              转发给法人
            </Text>
            <Text className='action-btn' onClick={() => handleActionConfirm(1)}>
              保存图片
            </Text>
          </View>
        </View>
      </View>

      <View className='bottom-button'>
        <Button type='primary' onClick={() => Taro.navigateBack()}>
          法人已完成
        </Button>
      </View>

      {/* @ts-ignore */}
      <BottomPopup
        visible={isActionPopupVisible}
        onClose={() => setIsActionPopupVisible(false)}
        onConfirm={handleActionConfirm}
        options={['转发给法人', '保存图片']}
      />
    </View>
  );
};

export default VerifyAccount;
