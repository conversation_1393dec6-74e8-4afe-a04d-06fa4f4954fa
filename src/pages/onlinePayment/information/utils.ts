import { BankCardInfo, FormErrors } from './types';

// 验证银行卡号
export const validateBankCardNumber = (number: string): boolean => {
  // 移除空格
  const cleanNumber = number.replace(/\s/g, '');
  // 银行卡号为16-19位数字
  return /^\d{16,19}$/.test(cleanNumber);
};

// 验证表单
export const validateForm = (info: BankCardInfo): FormErrors => {
  const errors: FormErrors = {};

  // 验证姓名/开户名称
  if (!info.bankCard.name) {
    errors.name = info.type === 'grzh' ? '请输入持卡人姓名' : '请输入开户名称';
  }

  // 验证银行卡号/账号
  if (!info.bankCard.number) {
    errors.number = info.type === 'grzh' ? '请输入银行卡号' : '请输入银行账号';
  } else if (info.type === 'grzh' && !validateBankCardNumber(info.bankCard.number)) {
    errors.number = '请输入正确的银行卡号';
  }

  // 验证支行名称
  if (!info.bankCard.branch) {
    errors.branch = '请输入开户支行名称';
  }

  return errors;
};

// 格式化银行卡号（每4位加空格）
export const formatBankCardNumber = (number: string): string => {
  return number.replace(/\s/g, '').replace(/(\d{4})(?=\d)/g, '$1 ');
};

// 清理银行卡号格式（移除空格）
export const cleanBankCardNumber = (number: string): string => {
  return number.replace(/\s/g, '');
};

/**
 * 调试模式相关工具函数
 */

// 调试场景枚举
export enum DebugScenario {
  NONE = '',
  BASIC = 'basic',                    // 基础调试
  API_CALLS = 'api',                  // API调用调试
  MOCK_DATA = 'mock',                 // 模拟数据测试
  SETTLEMENT_SUCCESS = 'settlement',   // 结算账户成功状态
  SETTLEMENT_PENDING = 'pending',      // 结算账户审核中状态
  SETTLEMENT_FAILED = 'failed',        // 结算账户失败状态
  WECHAT_SUCCESS = 'wechat_success',   // 微信审核成功
  WECHAT_PENDING = 'wechat_pending',   // 微信审核中
  WECHAT_FAILED = 'wechat_failed'      // 微信审核失败
}

// 获取URL参数的通用函数
const getUrlParam = (paramName: string): string | null => {
  if (typeof window === 'undefined') {
    return null;
  }

  // 在Taro H5环境中，URL参数可能在hash后面
  const searchUrl = window.location.search;
  const hashUrl = window.location.hash;

  // 尝试从search参数中获取
  let urlParams = new URLSearchParams(searchUrl);
  let param = urlParams.get(paramName);

  // 如果search中没有，尝试从hash中获取
  if (param === null && hashUrl.includes('?')) {
    const hashSearch = hashUrl.split('?')[1];
    urlParams = new URLSearchParams(hashSearch);
    param = urlParams.get(paramName);
  }

  return param;
};

// 检查URL中是否存在指定参数（无论是否有值）
const hasUrlParam = (paramName: string): boolean => {
  if (typeof window === 'undefined') {
    return false;
  }

  // 在Taro H5环境中，URL参数可能在hash后面
  const searchUrl = window.location.search;
  const hashUrl = window.location.hash;

  // 检查search参数中是否存在
  let urlParams = new URLSearchParams(searchUrl);
  if (urlParams.has(paramName)) {
    return true;
  }

  // 检查hash中是否存在
  if (hashUrl.includes('?')) {
    const hashSearch = hashUrl.split('?')[1];
    urlParams = new URLSearchParams(hashSearch);
    if (urlParams.has(paramName)) {
      return true;
    }
  }

  return false;
};

// 检查是否启用调试模式（只要有debug参数就启用，无论是否有值）
export const isDebugMode = (): boolean => {
  return hasUrlParam('debug');
};

// 获取调试场景
export const getDebugScenario = (): DebugScenario => {
  // 如果没有debug参数，返回NONE
  if (!hasUrlParam('debug')) {
    return DebugScenario.NONE;
  }

  const debugParam = getUrlParam('debug');

  // 如果debug参数存在但没有值（如 ?debug），返回基础调试模式
  if (debugParam === null || debugParam === '') {
    return DebugScenario.BASIC;
  }

  // 检查是否是有效的调试场景
  const validScenarios = Object.values(DebugScenario) as string[];
  if (validScenarios.includes(debugParam)) {
    return debugParam as DebugScenario;
  }

  // 如果不是预定义的场景，但有debug参数，返回基础调试模式
  return DebugScenario.BASIC;
};

// 调试日志函数
export const debugLog = (message: string, data?: any) => {
  if (!isDebugMode()) {
    return;
  }

  const currentScenario = getDebugScenario();

  // 尝试从 message 中解析场景信息
  let messageScenario: DebugScenario | null = null;
  const scenarioMatch = message.match(/^\[([^\]]+)\]/);
  if (scenarioMatch) {
    const scenarioName = scenarioMatch[1].toLowerCase();
    // 检查是否是有效的 DebugScenario
    const validScenarios = Object.values(DebugScenario) as string[];
    if (validScenarios.includes(scenarioName)) {
      messageScenario = scenarioName as DebugScenario;
    }
  }

  // 如果从 message 解析出了特定场景，只在该场景下输出日志
  if (messageScenario && messageScenario !== DebugScenario.BASIC && currentScenario !== messageScenario) {
    return;
  }

  const prefix = `[DEBUG${currentScenario !== DebugScenario.BASIC ? `-${currentScenario.toUpperCase()}` : ''}]`;

  if (data !== undefined) {
    console.log(`${prefix} ${message}`, data);
  } else {
    console.log(`${prefix} ${message}`);
  }
};

// 商户申请数据类型
interface MerchantApplicationData {
  id: number;
  userId: number;
  organizationType: number;
  merchantName: string;
  contactName: string;
  contactMobile: string;
  mobilePhone: string;
  customerServicePhone: string;
  bankAccountType: string;
  accountName: string;
  accountNumber: string;
  subMchid: string;
  [key: string]: any;
}

// 结算账号数据类型
interface SettlementAccountData {
  id: number;
  userId: number;
  applicationNo?: number;
  accountType: string;
  accountName: string;
  accountBank: string;
  bankName: string;
  bankBranchId: string;
  accountNumber: string;
  verifyResult?: string;
  verifyFailReason?: string;
  verifyFinishTime?: string;
  createTime?: string;
}

// 微信侧审核状态数据类型
interface WechatAccountApplymentData {
  account_name: string;
  account_type: string;
  account_bank: string;
  bank_name: string;
  bank_branch_id: string;
  account_number: string;
  verify_result: 'AUDIT_SUCCESS' | 'AUDITING' | 'AUDIT_FAIL';
  verify_fail_reason?: string;
  verify_finish_time?: string;
}

/**
 * 生成模拟的商户申请数据
 */
export const generateMockMerchantData = (scenario: DebugScenario): MerchantApplicationData => {
  debugLog(`[mock] 生成商户申请模拟数据，场景: ${scenario}`);

  const baseData: MerchantApplicationData = {
    id: 12345,
    userId: 1001,
    organizationType: 2, // 企业
    merchantName: '测试商户有限公司',
    contactName: '张三',
    contactMobile: '***********',
    mobilePhone: '***********',
    customerServicePhone: '************',
    bankAccountType: 'CORPORATE',
    accountName: '测试商户有限公司',
    accountNumber: '621226**********123',
    subMchid: '**********'
  };

  switch (scenario) {
    case DebugScenario.SETTLEMENT_SUCCESS:
    case DebugScenario.WECHAT_SUCCESS:
      return {
        ...baseData,
        merchantName: '成功状态测试商户',
        contactName: '成功测试'
      };

    case DebugScenario.SETTLEMENT_PENDING:
    case DebugScenario.WECHAT_PENDING:
      return {
        ...baseData,
        merchantName: '审核中状态测试商户',
        contactName: '审核测试'
      };

    case DebugScenario.SETTLEMENT_FAILED:
    case DebugScenario.WECHAT_FAILED:
      return {
        ...baseData,
        merchantName: '失败状态测试商户',
        contactName: '失败测试'
      };

    default:
      return baseData;
  }
};

/**
 * 生成模拟的结算账户数据
 */
export const generateMockSettlementData = (scenario: DebugScenario): SettlementAccountData[] => {
  debugLog(`[mock] 生成结算账户模拟数据，场景: ${scenario}`);

  const baseData: SettlementAccountData = {
    id: 54321,
    userId: 1001,
    applicationNo: 12345,
    accountType: 'CORPORATE',
    accountName: '测试商户有限公司',
    accountBank: 'ICBC',
    bankName: '中国工商银行',
    bankBranchId: '************',
    accountNumber: '621226**********123',
    createTime: '2024-01-15 10:30:00'
  };

  switch (scenario) {
    case DebugScenario.SETTLEMENT_SUCCESS:
      return [{
        ...baseData,
        verifyResult: 'AUDIT_SUCCESS',
        verifyFinishTime: '2024-01-16 14:20:00'
      }];

    case DebugScenario.SETTLEMENT_PENDING:
      return [{
        ...baseData,
        verifyResult: 'AUDITING'
      }];

    case DebugScenario.SETTLEMENT_FAILED:
      return [{
        ...baseData,
        verifyResult: 'AUDIT_FAIL',
        verifyFailReason: '银行卡号格式不正确，请检查后重新提交',
        verifyFinishTime: '2024-01-16 16:45:00'
      }];

    default:
      return [baseData];
  }
};

/**
 * 生成模拟的微信审核数据
 */
export const generateMockWechatData = (scenario: DebugScenario): WechatAccountApplymentData => {
  debugLog(`[mock] 生成微信审核模拟数据，场景: ${scenario}`);

  const baseData: WechatAccountApplymentData = {
    account_name: '测试商户有限公司',
    account_type: 'CORPORATE',
    account_bank: 'ICBC',
    bank_name: '中国工商银行',
    bank_branch_id: '************',
    account_number: '621226**********123'
  };

  switch (scenario) {
    case DebugScenario.WECHAT_SUCCESS:
      return {
        ...baseData,
        verify_result: 'AUDIT_SUCCESS',
        verify_finish_time: '2024-01-16T14:20:00+08:00'
      };

    case DebugScenario.WECHAT_PENDING:
      return {
        ...baseData,
        verify_result: 'AUDITING'
      };

    case DebugScenario.WECHAT_FAILED:
      return {
        ...baseData,
        verify_result: 'AUDIT_FAIL',
        verify_fail_reason: '开户银行信息与实际不符，请核实后重新提交'
      };

    default:
      return {
        ...baseData,
        verify_result: 'AUDITING'
      };
  }
};

/**
 * 模拟API响应
 */
export const mockApiResponse = <T>(data: T, scenario: DebugScenario) => {
  debugLog(`[mock] 模拟API响应，场景: ${scenario}`, data);

  return Promise.resolve({
    code: 0,
    data,
    msg: 'success'
  });
};