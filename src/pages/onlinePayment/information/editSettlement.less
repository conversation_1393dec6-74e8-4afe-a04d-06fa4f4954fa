@import '@arco-design/mobile-react/style/mixin.less';

[id^="/pages/onlinePayment/information/editSettlement"] {
  background-color: var(--page-primary-background-color) !important;

  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
}

.editSettlementPage {
  position: relative;
  width: 100%;
  min-height: 100vh;
  z-index: 1;

  .content {
    padding: 12px;

    // 账户类型
    .zhxx-cell-zhlx {
      &-content {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;

        .arco-radio-group {
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: 32px;
        }

        .arco-radio {
          padding: 0;
          margin-right: 0;
          
          &-text {
            font-size: 14px;
            .use-var(color, font-color);
            .use-dark-mode-query({
              color: var(--dark-font-color) !important;
            });
          }
        }
      }
    }

    // 持卡人/开户名称
    .zhxx-cell-ckr,
    .zhxx-cell-khmc {
      &-content {
        width: 100%;
        font-size: 14px;
      }
    }

    // 银行卡号
    .zhxx-cell-yhkh {
      &-content {
        width: 100%;
        font-size: 14px;
      }
    }

    // 所属支行
    .zhxx-cell-sszh {
      &-content {
        width: 100%;
        font-size: 14px;
      }
    }

    // 银行卡照片/开户证明
    .zhxx-cell-khzm {
      padding: 16px 12px !important;

      &-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &-hint {
          align-self: flex-start;
          font-size: 12px;
          margin-bottom: 12px;
          .use-var(color, sub-info-font-color);
          .use-dark-mode-query({
            color: var(--dark-sub-info-font-color) !important;
          });
        }

        &-wrapper {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          gap: 16px;

          &-left,
          &-right {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }

  .holder {
    width: 100%;
    height: 100px;
  }

  .footerbtn {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    padding: 10px 16px;
    box-sizing: border-box;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });

    &-btn {
      width: 100%;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;

      &.disabled {
        opacity: 0.6;
        cursor: not-allowed;

        &:hover,
        &:active {
          opacity: 0.6;
        }
      }

      &-text {
        font-size: 16px;
        font-weight: 500;
        color: #fff;
      }
    }
  }
}

.hasValue {
  .use-var(color, font-color);
  .use-dark-mode-query({
    color: var(--dark-font-color) !important;
  });
}

.noValue {
  .use-var(color, sub-info-font-color);
  .use-dark-mode-query({
    color: var(--dark-sub-info-font-color) !important;
  });
}

.yk-cell-group {
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  background-color: var(--container-background-color) !important;
  .use-dark-mode-query({
    background-color: var(--dark-container-background-color) !important;
  });

  .arco-cell {
    margin: 0 !important;
    padding: 0px 10px 5px 10px !important;
    min-height: 52px;

    &-label {
      font-size: 16px;
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: var(--dark-font-color) !important;
      });
    }

    .arco-input-wrap {
      margin: 0 !important;
      padding: 0 !important;
    }

    .arco-input {
      font-size: 14px;
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: var(--dark-font-color) !important;
      });

      &::placeholder {
        font-size: 14px;
        .use-var(color, sub-info-font-color);
        .use-dark-mode-query({
          color: var(--dark-sub-info-font-color) !important;
        });
      }
    }

    &.has-error {
      .arco-input {
        color: var(--danger-color) !important;
      }
    }
  }
}

.yk-imagebox-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &-hint {
    margin-top: 0px;
    margin-bottom: 12px;
    align-self: flex-start;
    font-size: 12px;
    .use-var(color, sub-info-font-color);
    .use-dark-mode-query({
      color: var(--dark-sub-info-font-color) !important;
    });
  }

  &-wrapper {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 16px;

    &-left,
    &-right {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }
}

