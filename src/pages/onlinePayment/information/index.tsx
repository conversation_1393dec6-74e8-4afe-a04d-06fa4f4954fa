import { View, Text } from '@tarojs/components';
import { useEffect, useState } from 'react';
import Taro from '@tarojs/taro';
import PageSwipeBack from '@/components/PageSwipeBack';
import YkNavBar from '@/components/ykNavBar';
import { IconCopy } from '@/components/YkIcons';
import { Toast, Button } from '@arco-design/mobile-react';
import YkNoticeBar from '@/components/YkNoticeBar';
import { getMerchantEntryApplicationPage, getSettlementAccountPage, getAccountApplyments } from '@/utils/api/common/common_wechat';
import { MerchantType } from '../types';
import {
  isDebugMode,
  getDebugScenario,
  debugLog,
  DebugScenario,
  generateMockMerchantData,
  generateMockSettlementData,
  generateMockWechatData
} from './utils';
import './index.less';
import { IconArrowIn } from '@arco-design/mobile-react/esm/icon';

// API响应类型定义
interface ApiResponse<T> {
  code: number;
  data: T;
  msg: string;
}

// 分页数据类型
interface PageData<T> {
  list: T[];
  total: number;
}

// 商户申请数据类型
interface MerchantApplicationData {
  id: number;
  userId: number;
  organizationType: number;
  merchantName: string;
  contactName: string;
  contactMobile: string;
  mobilePhone: string; // 超级管理员手机（手机号码）
  customerServicePhone: string; // 客服电话
  bankAccountType: string;
  accountName: string;
  accountNumber: string;
  subMchid: string;
  [key: string]: any;
}

// 结算账号数据类型
interface SettlementAccountData {
  id: number;
  userId: number;
  applicationNo?: number;
  accountType: string;
  accountName: string;
  accountBank: string;
  bankName: string;
  bankBranchId: string;
  accountNumber: string;
  verifyResult?: string;
  verifyFailReason?: string;
  verifyFinishTime?: string;
  createTime?: string;
}

// 微信侧审核状态数据类型
interface WechatAccountApplymentData {
  account_name: string;
  account_type: string;
  account_bank: string;
  bank_name: string;
  bank_branch_id: string;
  account_number: string;
  verify_result: 'AUDIT_SUCCESS' | 'AUDITING' | 'AUDIT_FAIL';
  verify_fail_reason?: string;
  verify_finish_time?: string;
}

interface MerchantInfo {
  merchantType: number;
  merchantName: string;
  legalName: string;
  phone: string; // 手机号码（从 mobilePhone 字段获取）
  servicePhone: string; // 客服电话（从 customerServicePhone 字段获取）
  accountType: string;
  accountHolder: string;
  bankAccount: string;
  merchantNo: string;
  merchantFullName: string;
  settlementCycle: string;
  receiveLimit: string;
  withdrawLimit: string;
  status?: 'pending' | 'approved' | 'rejected';
  rejectReason?: string;
}

// 解析用户信息的工具函数
const parseUserInfo = (userInfoStorage: any) => {
  if (!userInfoStorage) return null;

  try {
    return typeof userInfoStorage === 'string'
      ? JSON.parse(userInfoStorage)
      : userInfoStorage;
  } catch (error) {
    console.error('解析用户信息失败:', error);
    return null;
  }
};

// 商户类型映射
const getMerchantTypeText = (merchantType: number): string => {
  switch (merchantType) {
    case MerchantType.ENTERPRISE:
      return '企业';
    case MerchantType.INDIVIDUAL:
      return '个体工商户';
    case MerchantType.PERSONAL:
      return '个人';
    default:
      return '未知';
  }
};

// 格式化银行账号（隐藏中间部分）
const formatBankAccount = (accountNumber: string): string => {
  if (!accountNumber || accountNumber.length < 8) return accountNumber;
  const start = accountNumber.substring(0, 4);
  const end = accountNumber.substring(accountNumber.length - 4);
  return `${start}****${end}`;
};

const MerchantInformation: React.FC = () => {
  const [merchantInfo, setMerchantInfo] = useState<MerchantInfo>({
    merchantType: 2, // 默认为企业2
    merchantName: '',
    legalName: '',
    phone: '',
    servicePhone: '',
    accountType: '',
    accountHolder: '',
    bankAccount: '',
    merchantNo: '',
    merchantFullName: '',
    settlementCycle: '',
    receiveLimit: '',
    withdrawLimit: ''
  });
  const [loading, setLoading] = useState(true);
  const [wechatApplymentStatus, setWechatApplymentStatus] = useState<WechatAccountApplymentData | null>(null);
  // 存储商户进件申请ID，用于跳转到editContact页面
  const [merchantApplicationId, setMerchantApplicationId] = useState<number | null>(null);

  useEffect(() => {
    fetchMerchantInfo();
  }, []);

  // 调试组件
  const DebugPanel = ({ scenario, merchantInfo, wechatApplymentStatus, onLoadMockData }: {
    scenario: DebugScenario;
    merchantInfo: MerchantInfo;
    wechatApplymentStatus: WechatAccountApplymentData | null;
    onLoadMockData: (scenario: DebugScenario) => void;
  }) => {
    const [isExpanded, setIsExpanded] = useState(false);

    return (
      <View style={{
        position: 'fixed',
        bottom: '20px',
        right: '10px',
        background: 'rgba(0,0,0,0.9)',
        color: 'white',
        borderRadius: '8px',
        fontSize: '12px',
        zIndex: 1000,
        maxWidth: '280px',
        minWidth: '200px'
      }}>
        {/* 调试面板头部 */}
        <View
          style={{
            padding: '8px 12px',
            borderBottom: isExpanded ? '1px solid rgba(255,255,255,0.2)' : 'none',
            cursor: 'pointer',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <Text style={{ fontWeight: 'bold', fontSize: '13px' }}>
            🐛 调试模式: {scenario}
          </Text>
          <Text style={{ fontSize: '16px' }}>
            {isExpanded ? '▼' : '▶'}
          </Text>
        </View>

        {/* 展开的调试内容 */}
        {isExpanded && (
          <View style={{ padding: '12px' }}>
            {/* 状态信息 */}
            <View style={{ marginBottom: '12px' }}>
              <Text style={{ display: 'block', marginBottom: '4px', color: '#ccc' }}>
                商户类型: {getMerchantTypeText(merchantInfo.merchantType)}
              </Text>
              <Text style={{ display: 'block', marginBottom: '4px', color: '#ccc' }}>
                账户类型: {merchantInfo.accountType}
              </Text>
              <Text style={{ display: 'block', marginBottom: '4px', color: '#ccc' }}>
                审核状态: {merchantInfo.status === 'approved' ? '✅ 已通过' :
                         merchantInfo.status === 'rejected' ? '❌ 已拒绝' :
                         merchantInfo.status === 'pending' ? '⏳ 审核中' : '❓ 未知'}
              </Text>
              {wechatApplymentStatus && (
                <Text style={{ display: 'block', marginBottom: '4px', color: '#ccc' }}>
                  微信状态: {wechatApplymentStatus.verify_result === 'AUDIT_SUCCESS' ? '✅ 成功' :
                           wechatApplymentStatus.verify_result === 'AUDIT_FAIL' ? '❌ 失败' : '⏳ 审核中'}
                </Text>
              )}
            </View>

            {/* 测试按钮 */}
            <View style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '6px',
              fontSize: '11px'
            }}>
              <Button
                size="mini"
                style={{ fontSize: '10px', padding: '4px 8px' }}
                onClick={() => onLoadMockData(DebugScenario.SETTLEMENT_SUCCESS)}
              >
                ✅ 结算成功
              </Button>
              <Button
                size="mini"
                style={{ fontSize: '10px', padding: '4px 8px' }}
                onClick={() => onLoadMockData(DebugScenario.SETTLEMENT_PENDING)}
              >
                ⏳ 结算审核中
              </Button>
              <Button
                size="mini"
                style={{ fontSize: '10px', padding: '4px 8px' }}
                onClick={() => onLoadMockData(DebugScenario.SETTLEMENT_FAILED)}
              >
                ❌ 结算失败
              </Button>
              <Button
                size="mini"
                style={{ fontSize: '10px', padding: '4px 8px' }}
                onClick={() => onLoadMockData(DebugScenario.WECHAT_SUCCESS)}
              >
                ✅ 微信成功
              </Button>
              <Button
                size="mini"
                style={{ fontSize: '10px', padding: '4px 8px' }}
                onClick={() => onLoadMockData(DebugScenario.WECHAT_PENDING)}
              >
                ⏳ 微信审核中
              </Button>
              <Button
                size="mini"
                style={{ fontSize: '10px', padding: '4px 8px' }}
                onClick={() => onLoadMockData(DebugScenario.WECHAT_FAILED)}
              >
                ❌ 微信失败
              </Button>
            </View>
          </View>
        )}
      </View>
    );
  };

  // 加载模拟数据
  const loadMockData = async (scenario: DebugScenario) => {
    debugLog(`[mock] 开始加载模拟数据，场景: ${scenario}`);

    try {
      // 生成模拟的商户数据
      const mockMerchantData = generateMockMerchantData(scenario);

      // 生成模拟的结算账户数据
      const mockSettlementData = generateMockSettlementData(scenario);

      // 生成模拟的微信审核数据
      const mockWechatData = generateMockWechatData(scenario);

      // 设置模拟的商户申请ID
      setMerchantApplicationId(mockMerchantData.id || 999999);
      debugLog('[mock] 设置模拟商户进件申请ID:', mockMerchantData.id || 999999);

      // 设置商户信息
      setMerchantInfo({
        merchantType: mockMerchantData.organizationType || MerchantType.ENTERPRISE,
        merchantName: mockMerchantData.merchantName || '暂无',
        legalName: mockMerchantData.contactName || '暂无',
        phone: mockMerchantData.mobilePhone || mockMerchantData.contactMobile || '暂无',
        servicePhone: mockMerchantData.customerServicePhone || '暂无',
        merchantNo: mockMerchantData.subMchid || '暂无',
        merchantFullName: mockMerchantData.merchantName || '暂无',
        accountType: mockSettlementData[0]?.accountType === 'CORPORATE' ? '对公账户' : '个人账户',
        accountHolder: mockSettlementData[0]?.accountName || '暂无',
        bankAccount: formatBankAccount(mockSettlementData[0]?.accountNumber || ''),
        settlementCycle: '每日结算',
        receiveLimit: '无限制',
        withdrawLimit: '单笔最高50万元',
        status: mockSettlementData[0]?.verifyResult === 'AUDIT_SUCCESS' ? 'approved' :
                mockSettlementData[0]?.verifyResult === 'AUDIT_FAIL' ? 'rejected' : 'pending',
        rejectReason: mockSettlementData[0]?.verifyFailReason
      });

      // 设置微信审核状态
      setWechatApplymentStatus(mockWechatData);

      debugLog(`[mock] 模拟数据加载完成`, {
        merchantInfo: mockMerchantData,
        settlementData: mockSettlementData,
        wechatData: mockWechatData
      });

      Toast.success({ content: `已加载${scenario}场景测试数据`, duration: 2000 });

    } catch (error) {
      debugLog(`[mock] 加载模拟数据失败:`, error);
      Toast.error({ content: '加载模拟数据失败', duration: 2000 });
    } finally {
      setLoading(false);
    }
  };

  const fetchMerchantInfo = async () => {
    try {
      setLoading(true);

      // 调试模式处理
      if (isDebugMode()) {
        const scenario = getDebugScenario();
        debugLog(`[basic] 检测到调试模式，场景: ${scenario}`);

        // 如果是模拟数据场景，直接返回模拟数据
        if ([
          DebugScenario.SETTLEMENT_SUCCESS,
          DebugScenario.SETTLEMENT_PENDING,
          DebugScenario.SETTLEMENT_FAILED,
          DebugScenario.WECHAT_SUCCESS,
          DebugScenario.WECHAT_PENDING,
          DebugScenario.WECHAT_FAILED
        ].includes(scenario)) {
          await loadMockData(scenario);
          return;
        }
      }

      // 获取URL参数中的subMchid
      const router = Taro.getCurrentInstance().router;
      const urlSubMchid = router?.params?.subMchid;
      debugLog('[api] URL参数中的subMchid:', urlSubMchid);

      // 获取用户信息
      const userInfoStorage = Taro.getStorageSync('userInfo');
      const userInfo = parseUserInfo(userInfoStorage);

      if (!userInfo || !userInfo.id) {
        Toast.error('获取用户信息失败');
        setLoading(false);
        return;
      }

      debugLog('[api] 开始获取商户信息，用户ID:', userInfo.id);

      // 优先尝试获取结算账号信息（包含最新修改的结算信息数据）
      let settlementData: SettlementAccountData | null = null;
      try {
        debugLog('[api] 调用 getSettlementAccountPage API');
        const settlementResponse = await getSettlementAccountPage({
          pageNo: 1,
          pageSize: 10,
          userId: userInfo.id
        }) as ApiResponse<PageData<SettlementAccountData>>;

        debugLog('[api] 结算账号API响应:', settlementResponse);

        if (settlementResponse.code === 0 && settlementResponse.data &&
            settlementResponse.data.list && settlementResponse.data.list.length > 0) {
          // 获取最新的结算账号信息（按创建时间排序，取第一条）
          settlementData = settlementResponse.data.list[0];
          debugLog('[api] 找到结算账号信息:', settlementData);
        }
      } catch (error) {
        debugLog('[api] 获取结算账号信息失败，将使用商户申请信息:', error);
      }

      // 如果没有结算账号信息，则获取商户申请信息
      debugLog('[api] 调用 getMerchantEntryApplicationPage API');
      const response = await getMerchantEntryApplicationPage({
        pageNo: 1,
        pageSize: 10,
        userId: userInfo.id
      }) as ApiResponse<PageData<MerchantApplicationData>>;

      debugLog('[api] 商户申请API响应:', response);

      if (response.code === 0 && response.data && response.data.list && response.data.list.length > 0) {
        // 取第一条记录作为商户信息
        const merchantData = response.data.list[0];

        // 优先使用URL参数中的subMchid，如果没有则使用API返回的subMchid
        const finalSubMchid = urlSubMchid || merchantData.subMchid || '暂无';
        console.log('[商户信息] 最终使用的subMchid:', finalSubMchid);

        // 根据是否有结算账号数据来决定使用哪个数据源
        let accountInfo: {
          accountType: string;
          accountHolder: string;
          bankAccount: string;
        };
        let status: 'pending' | 'approved' | 'rejected' | undefined;
        let rejectReason: string | undefined;

        if (settlementData) {
          // 使用结算账号数据（最新修改的结算信息）
          console.log('[商户信息] 使用结算账号数据');
          accountInfo = {
            accountType: settlementData.accountType === '74' ? '对公账户' : '个人账户',
            accountHolder: settlementData.accountName || '暂无',
            bankAccount: formatBankAccount(settlementData.accountNumber || ''),
          };

          // 解析验证结果状态
          if (settlementData.verifyResult) {
            switch (settlementData.verifyResult.toLowerCase()) {
              case 'success':
              case 'approved':
                status = 'approved';
                break;
              case 'failed':
              case 'rejected':
                status = 'rejected';
                rejectReason = settlementData.verifyFailReason;
                break;
              default:
                status = 'pending';
            }
          }
        } else {
          // 使用商户申请数据（开通商户进件后的原始结算信息）
          console.log('[商户信息] 使用商户申请数据');
          accountInfo = {
            accountType: merchantData.bankAccountType === '74' ? '对公账户' : '个人账户',
            accountHolder: merchantData.accountName || '暂无',
            bankAccount: formatBankAccount(merchantData.accountNumber || ''),
          };
        }

        // 保存商户进件申请ID，用于跳转到editContact页面
        setMerchantApplicationId(merchantData.id);
        debugLog('[商户信息] 保存商户进件申请ID:', merchantData.id);

        // 将API数据转换为页面显示格式
        setMerchantInfo({
          merchantType: merchantData.organizationType || MerchantType.ENTERPRISE, // 使用API返回的枚举值，默认为企业
          merchantName: merchantData.merchantName || '暂无',
          legalName: merchantData.contactName || '暂无',
          phone: merchantData.mobilePhone || merchantData.contactMobile || '暂无', // 优先使用 mobilePhone
          servicePhone: merchantData.customerServicePhone || '暂无', // 客服电话
          ...accountInfo,
          merchantNo: finalSubMchid, // 优先使用URL参数中的二级商户号
          merchantFullName: merchantData.merchantName || '暂无',
          settlementCycle: 'D+1', // 默认值，后续可从API获取
          receiveLimit: '无上限', // 默认值，后续可从API获取
          withdrawLimit: '100万/天', // 默认值，后续可从API获取
          status,
          rejectReason
        });

        console.log('[商户信息] 商户信息设置成功，状态:', status);

        // 只有在有结算账号数据的情况下，才获取微信侧的审核状态
        if (settlementData) {
          console.log('[商户信息] 检测到结算账号数据，获取微信侧审核状态');
          await fetchWechatApplymentStatus();
        } else {
          console.log('[商户信息] 无结算账号数据，跳过微信审核状态查询');
        }
      } else if (response.code === 0 && (!response.data || !response.data.list || response.data.list.length === 0)) {
        // 没有找到商户信息
        console.log('[商户信息] 未找到商户信息');
        Toast.error('未找到商户信息，请先完成商户申请');
        // 可以考虑跳转到申请页面
        setTimeout(() => {
          Taro.navigateBack();
        }, 2000);
      } else {
        console.error('[商户信息] 获取商户信息失败:', response.msg);
        Toast.error(response.msg || '获取商户信息失败');
      }
    } catch (error) {
      console.error('[商户信息] 获取商户信息出错:', error);
      Toast.error('获取商户信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取微信侧审核状态
  const fetchWechatApplymentStatus = async () => {
    try {
      console.log('[微信审核状态] 开始获取微信侧审核状态');
      const response = await getAccountApplyments() as ApiResponse<WechatAccountApplymentData>;

      console.log('[微信审核状态] API响应:', response);

      if (response.code === 0 && response.data) {
        setWechatApplymentStatus(response.data);
        console.log('[微信审核状态] 微信审核状态设置成功:', response.data);
      } else {
        console.log('[微信审核状态] 未找到微信审核状态或接口返回错误:', response.msg);
      }
    } catch (error) {
      console.error('[微信审核状态] 获取微信审核状态失败:', error);
      // 不显示错误提示，因为这是可选的功能
    }
  };

  // 获取审核状态通知栏配置
  const getApplymentNoticeConfig = () => {
    if (!wechatApplymentStatus) return null;

    const { verify_result, verify_fail_reason } = wechatApplymentStatus;

    switch (verify_result) {
      case 'AUDIT_SUCCESS':
        return {
          type: 'success' as const,
          title: '审核通过',
          content: '您的结算账号修改申请已审核通过'
        };
      case 'AUDITING':
        return {
          type: 'warning' as const,
          title: '审核中',
          content: '您的结算账号修改申请正在审核中，请耐心等待'
        };
      case 'AUDIT_FAIL':
        return {
          type: 'warning' as const,
          title: '审核驳回',
          content: verify_fail_reason || '您的结算账号修改申请被驳回，请重新提交'
        };
      default:
        return null;
    }
  };

  const handleModifyPhone = () => {
    if (!merchantApplicationId) {
      Toast.error('缺少商户申请信息，请刷新页面重试');
      return;
    }
    // 传递原始完整手机号，在editContact页面中进行脱敏显示和验证
    Taro.navigateTo({
      url: `/pages/onlinePayment/information/editContact?id=${merchantApplicationId}&type=phone&phone=${merchantInfo.phone}`
    });
  };

  const handleModifyServicePhone = () => {
    if (!merchantApplicationId) {
      Toast.error('缺少商户申请信息，请刷新页面重试');
      return;
    }
    // 传递原始完整客服电话
    Taro.navigateTo({
      url: `/pages/onlinePayment/information/editContact?id=${merchantApplicationId}&type=servicePhone&phone=${merchantInfo.servicePhone}`
    });
  };

  const handleVerifyBank = () => {
    Taro.navigateTo({
      url: '/pages/onlinePayment/verifyAccount/index'
    });
  };

  const handleCopy = (text: string) => {
    Taro.setClipboardData({
      data: text,
      success: () => {
        Taro.showToast({
          title: '复制成功',
          icon: 'success',
          duration: 2000
        });
      },
      fail: () => {
        Taro.showToast({
          title: '复制失败',
          icon: 'error',
          duration: 2000
        });
      }
    });
  };

  const handleViewAgreement = () => {
    Taro.navigateTo({
      url: '/pages/onlinePayment/agreement/index'
    });
  };

  const handleEditSettlement = () => {
    // merchantInfo.merchantType 已经是 MerchantType 枚举值，直接使用
    Taro.navigateTo({
      url: `/pages/onlinePayment/information/editSettlement?merchantType=${merchantInfo.merchantType}`
    });
  };

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <PageSwipeBack>
        <View className='merchant-info-page'>
          <YkNavBar title='微信商户签约信息' />
          <View className='merchant-info-content'>
            <View className='loading-container'>
              <Text>正在加载商户信息...</Text>
            </View>
          </View>
        </View>
      </PageSwipeBack>
    );
  }

  return (
    <PageSwipeBack>
      <View className='merchant-info-page'>
        <YkNavBar title='微信商户签约信息' />
        <View className='merchant-info-content'>
          {/* 微信审核状态通知栏 */}
          {(() => {
            const noticeConfig = getApplymentNoticeConfig();
            if (!noticeConfig) return null;

            return (
              <View className='notice-container'>
                <YkNoticeBar
                  type={noticeConfig.type}
                  title={noticeConfig.title}
                  content={noticeConfig.content}
                  wrapable={true}
                />
              </View>
            );
          })()}

          {/* 基本信息 */}
          <View className='info-card'>
            <View className='info-section'>
              <View className='info-row'>
                <Text className='label'>商户类型</Text>
                <Text className='value'>{getMerchantTypeText(Number(merchantInfo.merchantType) || 2)}</Text>
              </View>
              <View className='info-row'>
                <Text className='label'>商户名称</Text>
                <Text className='value'>{merchantInfo.merchantName}</Text>
              </View>
              <View className='info-row'>
                <Text className='label'>法人姓名</Text>
                <Text className='value'>{merchantInfo.legalName}</Text>
              </View>
              <View className='info-row'>
                <Text className='label'>手机号码</Text>
                <Text className='value'>{merchantInfo.phone}</Text>
                <Text className='action' onClick={() => handleModifyPhone()}>修改</Text>
              </View>
              <View className='info-row'>
                <Text className='label'>客服电话</Text>
                <Text className='value'>{merchantInfo.servicePhone}</Text>
                <Text className='action' onClick={() => handleModifyServicePhone()}>修改</Text>
              </View>
            </View>
            <View className='divider' />
            {/* 银行信息 */}
            <View className='info-section bank-info'>
              <View className='info-row'>
                <Text className='label'>账户类型</Text>
                <Text className='value'>{merchantInfo.accountType}</Text>
              </View>
              <View className='info-row'>
                <Text className='label'>开户人</Text>
                <Text className='value'>{merchantInfo.accountHolder}</Text>
              </View>
              <View className='info-row'>
                <Text className='label'>银行账号</Text>
                <Text className='value'>{merchantInfo.bankAccount}</Text>
              </View>

              <View className='verify-btn' onClick={handleEditSettlement}>
                <Text>修改结算信息</Text>
              </View>
            </View>
            <View className='divider' />
            {/* 商户号/全称 */}
            <View className='info-section'>
              <View className='info-row'>
                <Text className='label'>商户号</Text>
                <Text className='value'>{merchantInfo.merchantNo}</Text>
                {/* 复制icon后续补充 */}
                <View className='copy-icon' onClick={() => handleCopy(merchantInfo.merchantNo)}><IconCopy /></View>
              </View>
              <View className='info-row'>
                <Text className='label'>商户全称</Text>
                <Text className='value'>{merchantInfo.merchantFullName}</Text>
                {/* 复制icon后续补充 */}
                <View className='copy-icon' onClick={() => handleCopy(merchantInfo.merchantFullName)}><IconCopy /></View>
              </View>
            </View>
          </View>
          {/* 结算信息与协议 */}
          <View className='info-card'>
            <View className='info-section'>
              <View className='info-row'>
                <Text className='label'>结算周期</Text>
                <Text className='value'>{merchantInfo.settlementCycle}</Text>
                <Text className='settle-desc'>支付后第一天可提现</Text>
              </View>
              <View className='info-row'>
                <Text className='label'>收款限额</Text>
                <Text className='value'>{merchantInfo.receiveLimit}</Text>
              </View>
              <View className='info-row'>
                <Text className='label'>提现限额</Text>
                <Text className='value'>{merchantInfo.withdrawLimit}</Text>
              </View>
            </View>
            <View className='divider' />
            <View className='agreement'>
              <Text className='agreement-label'>支付协议</Text>
              <View className='agreement-link' onClick={handleViewAgreement}>
                <Text>支付服务协议</Text>
                <View className='arrow' ><IconArrowIn /></View>
              </View>
            </View>
          </View>
        </View>

        {/* 调试组件 - 独立的调试功能，不影响实际页面代码 */}
        {isDebugMode() && (
          <DebugPanel
            scenario={getDebugScenario()}
            merchantInfo={merchantInfo}
            wechatApplymentStatus={wechatApplymentStatus}
            onLoadMockData={loadMockData}
          />
        )}
      </View>
    </PageSwipeBack>
  );
};

export default MerchantInformation;
