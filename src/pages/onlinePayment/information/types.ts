import type { ImagePickItem } from '@arco-design/mobile-react/cjs/image-picker/type';
import type { AccountType } from '../types';

// 银行卡信息
export interface BankCardInfo {
  type: AccountType;
  image: ImagePickItem | null;
  bankCard: {
    name: string;
    number: string;
    branch: string;
  };
  wechat: {
    mediaId?: string;
  };
}

// OCR识别结果
export interface BankCardOCRResult {
  words_result?: {
    bank_card_number?: {
      words: string;
    };
  };
}

// 表单验证错误
export interface FormErrors {
  name?: string;
  number?: string;
  branch?: string;
}