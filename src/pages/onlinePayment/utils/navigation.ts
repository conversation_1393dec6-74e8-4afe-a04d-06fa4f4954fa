import Taro from '@tarojs/taro'
import { Toast } from '@arco-design/mobile-react'
import { getEntryFormStatusByApplyIdOrBusinessApplyNo, getMerchantEntryApplicationPage } from '@/utils/api/common/common_wechat'
import { MerchantApplicationStatus } from '../types'

// 用户信息解析工具函数
const parseUserInfo = (userInfoStorage: any) => {
  if (!userInfoStorage) {
    return null;
  }

  // 方式1: 直接是用户对象
  if (userInfoStorage.id) {
    return userInfoStorage;
  }
  // 方式2: 包装在data字段中
  else if (userInfoStorage.data && userInfoStorage.data.id) {
    return userInfoStorage.data;
  }
  // 方式3: 包装在userInfo字段中
  else if (userInfoStorage.userInfo && userInfoStorage.userInfo.id) {
    return userInfoStorage.userInfo;
  }

  return null;
};

/**
 * 智能跳转到在线收款相关页面
 * 根据开通状态自动跳转到对应页面：
 * - 已开通：直接跳转到信息页面
 * - 未开通：跳转到步骤页面
 * - 未登录：跳转到登录页面
 */
export const navigateToOnlinePayment = async () => {
  console.log('[在线收款导航] 开始检查开通状态');
  
  // 获取用户信息
  const userInfoStorage = Taro.getStorageSync('userInfo');
  const userInfo = parseUserInfo(userInfoStorage);

  if (!userInfo || !userInfo.id) {
    console.error('[在线收款导航] 获取用户信息失败，用户未登录');
    Toast.error('账号未登录，请先登录');
    setTimeout(() => {
      Taro.navigateTo({
        url: '/pages/login/index'
      });
    }, 1500);
    return;
  }

  console.log('[在线收款导航] 用户信息获取成功，用户ID:', userInfo.id);

  try {
    // 先尝试查询微信支付商户进件申请状态
    console.log('[在线收款导航] 开始查询微信支付商户进件申请状态');
    const wechatResponse = await getEntryFormStatusByApplyIdOrBusinessApplyNo() as any;

    console.log('[在线收款导航] 微信支付商户进件申请状态查询结果:', wechatResponse);

    if (wechatResponse && wechatResponse.code === 0 && wechatResponse.data) {
      const wechatData = wechatResponse.data;
      
      // 检查是否已开通完成
      if (wechatData.applymentState === 'FINISH') {
        console.log('[在线收款导航] 检测到微信支付已开通完成，直接跳转到信息页面');
        
        // 构建跳转URL，如果有subMchid则添加参数
        let url = '/pages/onlinePayment/information/index';
        if (wechatData.subMchid) {
          url += `?subMchid=${encodeURIComponent(wechatData.subMchid)}`;
          console.log('[在线收款导航] 携带二级商户号参数:', wechatData.subMchid);
        }
        
        Taro.navigateTo({ url });
        return;
      }
    }

    // 如果微信接口没有返回完成状态，检查本地商户申请数据
    console.log('[在线收款导航] 微信支付未完成或查询失败，检查本地商户申请数据');
    const localResponse = await getMerchantEntryApplicationPage({
      pageNo: 1,
      pageSize: 10,
      userId: userInfo.id
    }) as any;

    console.log('[在线收款导航] 本地商户申请数据查询结果:', localResponse);

    if (localResponse && localResponse.code === 0 && localResponse.data && 
        localResponse.data.list && localResponse.data.list.length > 0) {
      const application = localResponse.data.list[0];
      
      // 检查是否已开通完成
      if (application.status === MerchantApplicationStatus.COMPLETED) {
        console.log('[在线收款导航] 检测到本地商户申请已完成，直接跳转到信息页面');
        
        // 构建跳转URL，如果有subMchid则添加参数
        let url = '/pages/onlinePayment/information/index';
        if (application.subMchid) {
          url += `?subMchid=${encodeURIComponent(application.subMchid)}`;
          console.log('[在线收款导航] 携带二级商户号参数:', application.subMchid);
        }
        
        Taro.navigateTo({ url });
        return;
      }
    }

    // 如果都没有完成，跳转到步骤页面
    console.log('[在线收款导航] 在线收款未完成，跳转到步骤页面');
    Taro.navigateTo({ url: "/pages/onlinePayment/index" });

  } catch (error) {
    console.error('[在线收款导航] 检查在线收款状态出错:', error);
    // 出错时直接跳转到步骤页面
    console.log('[在线收款导航] 出错时跳转到步骤页面');
    Taro.navigateTo({ url: "/pages/onlinePayment/index" });
  }
};

/**
 * 简单跳转到在线收款步骤页面（不做状态检查）
 */
export const navigateToOnlinePaymentSteps = () => {
  console.log('[在线收款导航] 直接跳转到步骤页面');
  Taro.navigateTo({ url: "/pages/onlinePayment/index" });
};

/**
 * 直接跳转到在线收款信息页面
 * @param subMchid 可选的二级商户号
 */
export const navigateToOnlinePaymentInfo = (subMchid?: string) => {
  console.log('[在线收款导航] 直接跳转到信息页面');
  let url = '/pages/onlinePayment/information/index';
  if (subMchid) {
    url += `?subMchid=${encodeURIComponent(subMchid)}`;
    console.log('[在线收款导航] 携带二级商户号参数:', subMchid);
  }
  Taro.navigateTo({ url });
};
