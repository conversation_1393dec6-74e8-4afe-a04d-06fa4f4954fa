import type { ImagePickItem } from '@arco-design/mobile-react/cjs/image-picker/type';
import type { MerchantType, MerchantTypeInfo, AccountType } from '../types';

// 日期选择器类型
export type DatePickerType = 'register' | 'idCardStart' | 'idCardEnd' | 'idCardEndPopup' | 'beneficiaryStart' | 'beneficiaryEnd';

export interface DatePickerConfig {
  visible: boolean;
  title: string;
  currentValue: string;
  onConfirm: (value: string) => void;
}

// 进件照片状态接口
export interface EntryImages {
  idCardFront: string;
  idCardBack: string;
  beneficiaryIdCardFront: string;
  beneficiaryIdCardBack: string;
  bankCard: string;
  businessLicense: string;
  accountOpeningLicense: string;
}

// 状态接口定义
export interface BusinessLicenseInfo {
  image: ImagePickItem | null;
  name: string;
  legalPerson: string;
  creditCode: string;
  registerDate: string;
  registerArea: string;
  businessAddress: string;
  wechat?: {
    mediaId?: string;
    [key: string]: any;
  };
}

export interface MerchantBasicInfo {
  shortName: string;
  industry: string;
  industryPickerVisible: boolean;
}

export interface IdentityInfo {
  images: {
    front: ImagePickItem | null;
    back: ImagePickItem | null;
  };
  basic: {
    name: string;
    idNumber: string;
    startDate: string;
    endDate: string;
    address: string;
  };
  wechat?: {
    frontMediaId?: string;
    backMediaId?: string;
    [key: string]: any;
  };
}

export interface WithdrawalAccountInfo {
  type: 'grzh' | 'dgzh';
  image: ImagePickItem | null;
  bankCard: {
    name: string;
    number: string;
    branch: string;
  };
  wechat?: {
    mediaId?: string;
    [key: string]: any;
  };
}

export interface ContactInfo {
  name: string;
  idNumber: string;
  phone: string;
  verificationCode: string;
  servicePhone: string;
}

// 完整的表单数据结构
export interface MerchantApplicationFormData {
  merchantType: MerchantType;
  businessLicenseInfo: BusinessLicenseInfo;
  merchantBasicInfo: MerchantBasicInfo;
  identityInfo: IdentityInfo;
  withdrawalAccountInfo: WithdrawalAccountInfo;
  beneficiaryInfo: IdentityInfo;
  contactInfo: ContactInfo;
  owner?: boolean; // 受益人类型：true=法人受益人，false=其他受益人
}

export interface UiState {
  noticeBar: {
    visible: boolean;
    content: string;
  };
  cityPicker: {
    visible: boolean;
    data: Array<{ value: string; label: string }>;
  };
  actionPopup: {
    visible: boolean;
  };
  popup: {
    visible: boolean;
  };
  datePicker: {
    type: DatePickerType | null;
    config: DatePickerConfig;
  };
} 

export interface MerchantEntryApplicationData {
  /** 业务申请编号 */
  outRequestNo?: string;
  /** 主体类型 */
  organizationType?: string;
  /** 证书类型 */
  certType?: string;
  /** 营业执照扫描件 */
  businessLicenseCopy?: string;
  /** 营业执照注册号 */
  businessLicenseNumber?: string;
  /** 商户名称 */
  merchantName?: string;
  /** 经营者/法定代表人姓名 */
  legalPerson?: string;
  /** 注册地址 */
  companyAddress?: string;
  /** 营业期限 */
  businessTime?: string;
  /** 金融机构类型 */
  financeType?: string;
  /** 金融机构许可证图片 */
  financeLicensePics?: string;
  /** 证件持有人类型 */
  idHolderType?: string;
  /** 经营者/法人证件类型 */
  idDocType?: string;
  /** 法定代表人说明函 */
  authorizeLetterCopy?: string;
  /** 身份证人像面照片 */
  idCardCopy?: string;
  /** 身份证国徽面照片 */
  idCardNational?: string;
  /** 身份证姓名 */
  idCardName?: string;
  /** 身份证号码 */
  idCardNumber?: string;
  /** 身份证开始时间 */
  idCardValidTimeBegin?: string;
  /** 身份证结束时间 */
  idCardValidTime?: string;
  /** 证件姓名 */
  idDocName?: string;
  /** 证件号码 */
  idDocNumber?: string;
  /** 证件正面照片 */
  idDocCopy?: string;
  /** 证件反面照片 */
  idDocCopyBack?: string;
  /** 证件开始日期 */
  docPeriodBegin?: string;
  /** 证件结束日期 */
  docPeriodEnd?: string;
  /** 经营者/法人是否为受益人 */
  owner?: boolean;
  /** 证件类型 */
  uboIdDocType?: string;
  /** 证件正面照片 */
  uboIdDocCopy?: string;
  /** 证件反面照片 */
  uboIdDocCopyBack?: string;
  /** 证件姓名 */
  uboIdDocName?: string;
  /** 证件号码 */
  uboIdDocNumber?: string;
  /** 证件居住地址 */
  uboIdDocAddress?: string;
  /** 证件有效期开始时间 */
  uboIdDocPeriodBegin?: string;
  /** 证件有效期结束时间 */
  uboIdDocPeriodEnd?: string;
  /** 账户类型 */
  bankAccountType?: string;
  /** 开户银行 */
  accountBank?: string;
  /** 开户名称 */
  accountName?: string;
  /** 开户银行省市编码 */
  bankAddressCode?: string;
  /** 开户银行联行号 */
  bankBranchId?: string;
  /** 开户银行全称（含支行） */
  bankName?: string;
  /** 银行账号 */
  accountNumber?: string;
  /** 超级管理员类型 */
  contactType?: string;
  /** 超级管理员姓名 */
  contactName?: string;
  /** 超级管理员证件类型 */
  contactIdDocType?: string;
  /** 超级管理员证件号码 */
  contactIdCardNumber?: string;
  /** 超级管理员证件正面照片 */
  contactIdDocCopy?: string;
  /** 超级管理员证件反面照片 */
  contactIdDocCopyBack?: string;
  /** 超级管理员证件有效期开始时间 */
  contactIdDocPeriodBegin?: string;
  /** 超级管理员证件有效期结束时间 */
  contactIdDocPeriodEnd?: string;
  /** 业务办理授权函 */
  businessAuthorizationLetter?: string;
  /** 超级管理员手机 */
  mobilePhone?: string;
  /** 店铺名称 */
  storeName?: string;
  /** 店铺链接 */
  storeUrl?: string;
  /** 店铺二维码 */
  storeQrCode?: string;
  /** 商家小程序APPID */
  miniProgramSubAppid?: string;
  /** 结算规则ID */
  settlementId?: number;
  /** 所属行业 */
  qualificationType?: string
  /** 商户简称 */
  merchantShortname?: string;
  /** 特殊资质 */
  qualifications?: string;
  /** 补充材料 */
  businessAdditionPics?: string;
  /** 补充说明 */
  businessAdditionDesc?: string;

 /** 客服电话 */
  customerServicePhone?: string;
} 