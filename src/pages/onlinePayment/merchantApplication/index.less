@import '@arco-design/mobile-react/style/mixin.less';
@import '@/utils/css/variables.less';

[id^="/pages/onlinePayment/merchantApplication/index"] {
  background-color: var(--page-primary-background-color) !important;

  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
}

.merchantApplicationPage {
  position: relative;
  width: 100%;
  min-height: 100vh;
  z-index: 1;

  // 加载状态
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;

    .loading-text {
      margin-top: 12px;
      font-size: 14px;
      .use-var(color, sub-font-color);
      .use-dark-mode-query({
        color: var(--dark-sub-font-color) !important;
      });
    }
  }

  // 状态提示区域
  .status-notice-container {
    margin: 0 !important;
    padding: 0 !important;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    gap: 20px;

    .status-notice-bar {
      padding: 6px 16px;
      margin: 0 !important;
      width: 100%;
      font-size: 14px;
      color: var(--danger-6) !important;
      background-color: var(--danger-1) !important;
      .use-dark-mode-query({
      color: var(--dark-danger-color) !important;
      background-color: var(--dark-warning-color) !important;
    });
    }

    // .status-notice-title {
    //   display: flex;
    //   flex-direction: column;
    //   gap: 4px;
    // }

    // .status-notice-header {

    // }
  }

  .content {
      padding: 10px;

      .group-header {
        margin: 16px 16px 8px 6px;
        &-title {
          font-size: 14px;
          .use-var(color, font-color);
          .use-dark-mode-query({
            color: var(--dark-font-color) !important;
          });
        }
      }

      // 商户类型
      .shlx-cell-type {
        &-content {
          margin-left: 16px;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;

          &-text {
            flex: 1;
            // width: 100%;
            font-size: 16px;
            .use-var(color, font-color);
            .use-dark-mode-query({
              color: var(--dark-font-color) !important;
            });
          }

          &-dropdown {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &.arco-select-item-tip {
              font-size: 14px !important;
            }

            &-type {
              font-size: 14px;
              color: #000;
            }

            &-hint {
              font-size: 12px;
              color: #999;
            }

            &-anchor {
              width: 1px;
              height: 1px;
              z-index: 999;
            }

            &-menu {
              max-height: 165px !important;
              position: fixed !important;
              width: 132px !important;
              max-width: 132px !important;
              border-radius: 4px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
              z-index: 1000;
              left: auto !important;
              right: 16px !important;
            }
          }
        }
      }
  
    // 营业执照信息
    .yyzzxx-cell-yyzz {
      &-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &-wrapper {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          gap: 15px;

          &-left, &-right {
            width: 100%;
            //width: 160px;
            //height: 90px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    .yyzzxx-cell-shmc {}

    .yyzzxx-cell-tyshxydm {}

    .yyzzxx-cell-zcrq {}

    .yyzzxx-cell-zcdq {}

    .yyzzxx-cell-jydz {
      &-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        width: 100%;
        font-size: 14px;
        margin: 0 !important;

        &-textarea {
          flex: 1;
          border: none;
          padding: 0 !important;
          margin: 0 !important;
          width: 100%;
          font-size: 14px;
        }
      }
    }

    .yyzzxx-cell-brcn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: auto !important;
      padding: 15px 0px !important;
      &-content {
        margin-top: 15px;
        margin-bottom: 15px;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        // display: flex;
        // flex-direction: row;
        // align-items: flex-start;
        // justify-content: flex-start;
        // gap: 15px;

        &-radio {
          display: flex;
          flex-direction: row;
          width: 100%;

          &-text {
            flex: 1;
            font-size: 11px;
            .use-var(color, sub-info-font-color);
            .use-dark-mode-query({
              color: var(--dark-sub-info-font-color) !important;
            });
          }
        }
      }
    }

    // 商户基本信息
    .shjbxx-cell-shjc {
      &-content {
        border: none;
        padding: 0 !important;
        margin: 0 !important;
        width: 100%;
        font-size: 14px;
      }
    }

    .shjbxx-cell-hylb {}
  
    // 身份信息
    .sfzxx-cell-sfz {
      &-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &-wrapper {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          gap: 15px;

          &-left,
          &-right {
            width: 100%;
            //width: 160px;
            //height: 90px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  
    .sfzxx-cell-xm {}

    .sfzxx-cell-sfzh {}

    .sfzxx-cell-yxqq {}

    .sfzxx-cell-yxqz {}

    .sfzxx-cell-zjzz {}


    // 提现账户信息
    .txzhxx-cell-zhlx {
      &-content {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 100%;

        .beneficiary-type {
          display: flex;
          gap: 20px;
        }

        .account-type-fixed {
          font-size: 14px;
          .use-var(color, font-color);

          .use-dark-mode-query({
            color: var(--dark-font-color) !important;
          });

          .account-type-note {
            margin-left: 8px;
            font-size: 12px;
            .use-var(color, sub-font-color);

            .use-dark-mode-query({
              color: var(--dark-sub-font-color) !important;
            });
          }
        }
      }
    }

    .txzhxx-cell-khmc {}
    .txzhxx-cell-ckr {
      &-content {
        width: 100%;
        font-size: 14px;
      }
    }

    .txzhxx-cell-yhkh {
      &-content {
        width: 100%;
        font-size: 14px;
      }
    }

    .txzhxx-cell-sszh {}

    .txzhxx-cell-yykzp {}

    .txzhxx-cell-khzm {
      padding: 15px 10px !important;

      &-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        &-hint {
          align-self: flex-start;
          /* 如果内容是文本，还可以加上 */
          // text-align: left;
          // width: 100%; // 如果想让文本充满整行
        }

        &-wrapper {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          gap: 15px;

          &-left,
          &-right {
            width: 100%;
            //width: 160px;
            //height: 90px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    // 联系人信息
    .lxxx-cell-phone {
      &-content {
        width: 100%;
        font-size: 14px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: 15px;

        &-input {
          flex: 1; // 让 input 自动占满剩余空间
          min-width: 0; // 防止内容撑开
          font-size: 15px;
        }
        &-button {
          // width: 100%;
          font-size: 15px;
          .use-var(color, primary-color);
          .use-dark-mode-query({
            color: var(--dark-primary-color) !important;
          });
          // background-color: var(--page-primary-background-color) !important;
          // .use-dark-mode-query({
          //   background-color: @dark-background-color !important;
          // });
          background-color: var(--container-background-color);
          .use-dark-mode-query({
            background-color: var(--dark-container-background-color);
          });
        }
      }
    }

    .lxxx-cell-yzm {
      &-content {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: 15px;
        &-input {
          flex: 1; // 让 input 自动占满剩余空间
          min-width: 0; // 防止内容撑开
          font-size: 14px;
        }
      }
    }

    .lxxx-cell-servicePhone {
      &-content {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: 15px;
        &-input {
          flex: 1; // 让 input 自动占满剩余空间
          min-width: 0; // 防止内容撑开
          font-size: 14px;
        }
      }
    }

    // 受益人信息
    .syrxx-cell-type {
      &-content {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }

    .syrxx-cell-sfz {
      &-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &-wrapper {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          gap: 15px;

          &-left,
          &-right {
            width: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    .syrxx-cell-xm {}

    .syrxx-cell-sfzh {}

    .syrxx-cell-yxqq {}

    .syrxx-cell-yxqz {}

    .syrxx-cell-zjzz {}

  }

  .holder {
    width: 100%;
    height: 100px;
  }

  .footerbtn {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });

    &-btn {
      margin: 0 16px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;

      .use-var(color, font-color);
      .use-dark-mode-query({
        color: var(--dark-primary-color) !important;
      });

      // 禁用状态样式
      &:disabled {
        .use-var(background-color, primary-disabled-color);
        .use-dark-mode-query({
          background-color: var(--dark-primary-disabled-color) !important;
        });
        opacity: 0.6;
        cursor: not-allowed;
      }

      // 表单无效时的样式（仍可点击但显示禁用外观）
      &.footerbtn-btn-disabled {
        .use-var(background-color, primary-disabled-color);
        .use-dark-mode-query({
          background-color: var(--dark-primary-disabled-color) !important;
        });
        opacity: 0.8; // 比真正禁用状态稍微明亮一些，表示仍可点击
      }

      &-text {
        padding: 6px 16px;
        font-size: 14px;
        color: #fff;
      }
    }
  }
}

.hasValue {
  .use-var(color, font-color);
  .use-dark-mode-query({
    color: var(--dark-font-color) !important;
  });
}

.noValue {
  .use-var(color, sub-info-font-color);
  .use-dark-mode-query({
    color: var(--dark-sub-info-font-color) !important;
  });
}

.cell-noticebar {
  width: 100%;

  &-content {
    width: 100%;
    color: #00B42A;
    background-color: #E8FFEA;
    margin-top: 12;
  }
}

.shlx-cell-type-content-dropdown .arco-select::after {
  content: none !important;
  height: 0 !important;
  border: none !important;
  background: none !important;
  display: none !important;
}

.yk-cell-group {
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 6px !important;
  overflow: hidden !important;
  background-color: var(--background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  .arco-cell {
    margin: 0 !important;
    padding: 0px 10px 5px 10px !important;
    // .use-var(background-color, background-color);
    // background-color: var(--page-primary-background-color) !important;
    // .use-dark-mode-query({
    //   background-color: @dark-background-color !important;
    // });
    .arco-input-wrap {
      margin: 0 !important;
      padding: 0 !important;
    }
  }
}

// .yk-cell {
//   margin: 0 !important;
//   padding: 0 !important;

//   // display: flex;
//   // flex-direction: column;
//   // align-items: center;
//   // justify-content: center;
//   // .arco-cell {
//   //   padding: 5px 10px !important;
//   // }
// }


.yk-imagebox-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  &-hint {
    margin-top: 0px;
    margin-bottom: 10px;
    align-self: flex-start;
    font-size: 11px;
    /* 如果内容是文本，还可以加上 */
    // text-align: left;
    // width: 100%; // 如果想让文本充满整行
  }

  &-wrapper {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 15px;

    &-left,
    &-right {
      width: 100%;
      //width: 160px;
      //height: 90px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }
}
