@import '@arco-design/mobile-react/style/mixin.less';

.sign-agreement-page {
  min-height: 100vh;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .content {
    margin-top: 0 !important;
    padding: 0 16px; 
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .title {
      margin-top: 50px;
      font-size: 15px;
      font-weight: bold;
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: @dark-font-color;
      });
      line-height: 21px;
      text-align: center;

      .name {
        .use-var(color, primary-color);
        .use-dark-mode-query({
          color: @dark-primary-color;
        });
      }
    }

    .qrcode-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 30px;


      .qrcode-wrapper {
        width: 150px;
        height: 150px;
        .use-var(background-color, background-color);
        .use-dark-mode-query({
          background-color: @dark-background-color;
        });
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .action-buttons {
        margin-top: 15px;
        display: flex;
        justify-content: center;

        .action-btn {
          font-size: 14px;
          .use-var(color, primary-color);
          .use-dark-mode-query({
            color: @dark-primary-color;
          });
          padding: 0;
          cursor: pointer;
          transition: opacity 0.2s;
          text-align: center;
          line-height: 20px;
          font-weight: 500;

          &:active {
            opacity: 0.8;
          }
        }
      }
    }
  }

  .bottom-button {
    width: 150px;
    padding: 16px;
    margin-bottom: 40px;
    .use-var(background-color, background-color);   
    .use-dark-mode-query({
      background-color: @dark-background-color;
    });
  }
}
