/**
 * 在线支付模块共用类型定义
 * 统一管理 merchantApplication 和 information 模块的共同类型
 */

// 商户类型枚举
export enum MerchantType {
  ENTERPRISE = 2,    // 企业
  INDIVIDUAL = 4,    // 个体工商户
  PERSONAL = 2401    // 个人
}

// 业务类型枚举
export enum BusinessType {
  APPLYMENT = 'APPLYMENT',     // 商户进件申请
  PAYMENT = 'PAYMENT',         // 支付订单
  REFUND = 'REFUND',           // 退款申请
  WITHDRAW = 'WITHDRAW',       // 提现申请
  TRANSFER = 'TRANSFER',       // 转账申请
  SETTLEMENT = 'SETTLEMENT',   // 结算申请
}

// 商户申请状态枚举
export enum MerchantApplicationStatus {
  PENDING_SUBMIT = 0,         // 待提交 - 显示"去提交"
  PENDING_REVIEW = 1,         // 已提交-待审核 - 显示"资料审核中：微信支付平台正在审核，请耐候"
  REVIEW_SUBMIT_REJECTED = 2, // 提交信息-审核不通过 - 显示驳回原因+"去修改"
  REVIEW_MODIFY_REJECTED = 3, // 修改信息-审核不通过 - 显示驳回原因+"去修改"
  PENDING_VERIFY = 4,         // 待验证 - 显示"去验证"
  PENDING_SIGN = 5,           // 待签约 - 显示"去签约"
  COMPLETED = 6               // 开通成功 - 显示完成状态
}

// 商户类型信息
export interface MerchantTypeInfo {
  typeLabel: string;     // 商户类型名称（企业/个体工商户/个人）
  managerLabel: string;  // 管理者身份（法人/经营者/个人）
}

// 账户类型
export type AccountType = 'grzh' | 'dgzh'; // 个人账户 | 对公账户

// 商户类型字典
export const MERCHANT_TYPE_DICT = {
  [MerchantType.ENTERPRISE]: {
    typeLabel: '企业',
    managerLabel: '法人'
  },
  [MerchantType.INDIVIDUAL]: {
    typeLabel: '个体工商户',
    managerLabel: '经营者'
  },
  [MerchantType.PERSONAL]: {
    typeLabel: '个人',
    managerLabel: '个人'
  }
} as const;

// 商户进件申请数据类型（从 merchantApplication/types.ts 移动到此处作为公共类型）
export interface MerchantEntryApplicationData {
  /** 编号 */
  id?: number;
  /** 用户编号 */
  userId?: number;
  /** 业务申请编号 */
  outRequestNo?: string;
  /** 主体类型 */
  organizationType?: string;
  /** 证书类型 */
  certType?: string;
  /** 营业执照扫描件 */
  businessLicenseCopy?: string;
  /** 营业执照注册号 */
  businessLicenseNumber?: string;
  /** 商户名称 */
  merchantName?: string;
  /** 经营者/法定代表人姓名 */
  legalPerson?: string;
  /** 注册地址 */
  companyAddress?: string;
  /** 营业期限 */
  businessTime?: string;
  /** 金融机构类型 */
  financeType?: string;
  /** 金融机构许可证图片 */
  financeLicensePics?: string;
  /** 证件持有人类型 */
  idHolderType?: string;
  /** 经营者/法人证件类型 */
  idDocType?: string;
  /** 法定代表人说明函 */
  authorizeLetterCopy?: string;
  /** 身份证人像面照片 */
  idCardCopy?: string;
  /** 身份证国徽面照片 */
  idCardNational?: string;
  /** 身份证姓名 */
  idCardName?: string;
  /** 身份证号码 */
  idCardNumber?: string;
  /** 身份证开始时间 */
  idCardValidTimeBegin?: string;
  /** 身份证结束时间 */
  idCardValidTime?: string;
  /** 证件姓名 */
  idDocName?: string;
  /** 证件号码 */
  idDocNumber?: string;
  /** 证件正面照片 */
  idDocCopy?: string;
  /** 证件反面照片 */
  idDocCopyBack?: string;
  /** 证件开始日期 */
  docPeriodBegin?: string;
  /** 证件结束日期 */
  docPeriodEnd?: string;
  /** 经营者/法人是否为受益人 */
  owner?: boolean;
  /** 证件类型 */
  uboIdDocType?: string;
  /** 证件正面照片 */
  uboIdDocCopy?: string;
  /** 证件反面照片 */
  uboIdDocCopyBack?: string;
  /** 证件姓名 */
  uboIdDocName?: string;
  /** 证件号码 */
  uboIdDocNumber?: string;
  /** 证件居住地址 */
  uboIdDocAddress?: string;
  /** 证件有效期开始时间 */
  uboIdDocPeriodBegin?: string;
  /** 证件有效期结束时间 */
  uboIdDocPeriodEnd?: string;
  /** 账户类型 */
  bankAccountType?: string;
  /** 开户银行 */
  accountBank?: string;
  /** 开户名称 */
  accountName?: string;
  /** 开户银行省市编码 */
  bankAddressCode?: string;
  /** 开户银行联行号 */
  bankBranchId?: string;
  /** 开户银行全称（含支行） */
  bankName?: string;
  /** 银行账号 */
  accountNumber?: string;
  /** 超级管理员类型 */
  contactType?: string;
  /** 超级管理员姓名 */
  contactName?: string;
  /** 超级管理员证件类型 */
  contactIdDocType?: string;
  /** 超级管理员证件号码 */
  contactIdCardNumber?: string;
  /** 超级管理员证件正面照片 */
  contactIdDocCopy?: string;
  /** 超级管理员证件反面照片 */
  contactIdDocCopyBack?: string;
  /** 超级管理员证件有效期开始时间 */
  contactIdDocPeriodBegin?: string;
  /** 超级管理员证件有效期结束时间 */
  contactIdDocPeriodEnd?: string;
  /** 业务办理授权函 */
  businessAuthorizationLetter?: string;
  /** 超级管理员手机 */
  mobilePhone?: string;
  /** 联系手机号 */
  contactMobile?: string;
  /** 店铺名称 */
  storeName?: string;
  /** 店铺链接 */
  storeUrl?: string;
  /** 店铺二维码 */
  storeQrCode?: string;
  /** 商家小程序APPID */
  miniProgramSubAppid?: string;
  /** 结算规则ID */
  settlementId?: number;
  /** 所属行业 */
  qualificationType?: string;
  /** 商户简称 */
  merchantShortname?: string;
  /** 特殊资质 */
  qualifications?: string;
  /** 补充材料 */
  businessAdditionPics?: string;
  /** 补充说明 */
  businessAdditionDesc?: string;
  /** 本地图片：营业执照 */
  businessLicenseCopyLocal?: string;
  /** 本地图片：银行卡 */
  bankPhotoLocal?: string;
  /** 本地图片：身份证正面 */
  idCardCopyLocal?: string;
  /** 本地图片：身份证反面 */
  idCardNationalLocal?: string;
  /** 二级商户号 */
  subMchid?: string;
  /** 其他扩展字段 */
  [key: string]: any;
}
