import { View, Text } from '@tarojs/components'
import { Button, Image, Input, Checkbox, Popup } from '@arco-design/mobile-react'
import './index.less'
import Taro from '@tarojs/taro'
import YkNavBar from '@/components/ykNavBar'
import { useState } from 'react';
import { login, getUserInfo } from '@/utils/api/common/common_user';
import { md5 } from 'js-md5'
export default function NewLogin() {
  const [visible, setVisible] = useState(false);
  const [check, setCheck] = useState(false);
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');

  const handlePwdLoginCheck = () => {
    if (!check) {
      setVisible(true);
      return
    }
    handlePwdLogin();
  }

  const handlePwdLogin = () => {
    let data: any = {
      type: 3
    }
    data.mobile = phone;
    data.password = password;
    // data.channel_name = 'H5';


    login(data).then((res: any) => {

      if (res.code == 0) {
        Taro.setStorageSync('userInfo', res.data)
        Taro.showToast({
          title: '登录成功',
          icon: 'none'
        })
        getUserInfo().then((res2: any) => {
          if (res2.code == 0) {
            //合并用户信息
            Taro.setStorageSync('userInfo', {
              ...res2.data,
              ...res.data
            })
            Taro.reLaunch({ url: '/pages/index/index' })
          }
        })
      } else {
        Taro.showToast({
          title: res.msg,
          icon: 'none'
        })
      }
    })

  }


  return (
    <View className='new-login'>
      <YkNavBar title=''/>
      <View className='logo-container'>
        <Image className='logo' src={require('../../assets/images/login/logo.png')} bottomOverlap={null} />
        <Text className='app-name'>账号密码登录</Text>
      </View>

      <View className='input-container'>
        <Input className='input-field' clearable placeholder='请输入手机号/用户名/账号ID' border='none' onChange={(_, value) => { setPhone(value) }} maxLength={30}/>
        <Input className='input-field' clearable placeholder='请输入密码' type='password' border='none' onChange={(_, value) => { setPassword(value) }} maxLength={30}/>
      </View>

      <View className='action-buttons'>
        <Button className='forgot-password' needActive={false} size='small' type='ghost'  onClick={() => {Taro.navigateTo({url: '/pages/resetPassword/index'})}} >忘记密码？</Button>
        <Button className='verification-code' needActive={false} size='small' type='ghost' onClick={() => {Taro.navigateTo({url: '/pages/codeLogin/index'})}}>验证码登录</Button>
      </View>

      <View className='login-buttons'>
        <Button className='login-button' onClick={handlePwdLoginCheck}>登录</Button>
      </View>

      <View className='agreement'>
      <Checkbox checked={check} onChange={() => { setCheck(!check) }} value={''}></Checkbox>
      <Text>已阅读并同意</Text>
        <Text className='link' onClick={() => {Taro.navigateTo({url: '/pages/webView/index?id=123&name=用户协议'})}}>《用户协议》</Text>
        <Text>和</Text>
        <Text className='link' onClick={() => {Taro.navigateTo({url: '/pages/webView/index?id=126&name=隐私政策'})}}>《隐私政策》</Text>
      </View>

      <Popup visible={visible} close={() => setVisible(false)} contentStyle={{ borderRadius: '10px 10px 0 0' }} direction={'bottom'}>
        <View className='cus-popup-content'>
          <Image src={require('../../assets/images/login/close.png')} className='popup-close' onClick={() => setVisible(false)} bottomOverlap={null} />
          <View className="popup-title">用户协议及隐私保护</View>
          <View className="popup-desc">
            <Text>为了更好地保障您的合法权益，请您阅读并同意以下协议</Text>
            <Text className='link' onClick={() => { Taro.navigateTo({ url: '/pages/webView/index?id=123&name=用户协议' }); setVisible(false) }}>《用户协议》</Text>
            <Text>和</Text>
            <Text className='link' onClick={() => { Taro.navigateTo({ url: '/pages/webView/index?id=126&name=隐私政策' }); setVisible(false) }}>《隐私政策》</Text>
            <Text>的全部内容。</Text>
          </View>
          <Button className="popup-confirm" onClick={() => {
            setCheck(true);
            handlePwdLogin();
            setVisible(false);
          }}>
            同意并继续
          </Button>
        </View>
      </Popup>
    </View>
  )
} 