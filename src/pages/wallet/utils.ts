import {
  createDebugger,
  DebugConfigBuilder,
  type DebugScenario as BaseDebugScenario_Type
} from '@/pages/debug';

// Wallet特定的调试场景
export enum WalletDebugScenario {
  PAYMENT_UNAVAILABLE = 'payment_unavailable', // 支付不可用
  BALANCE = 'balance',               // 有余额'
  NOBALANCE = 'nobalance',               // 有余额'
  EMPTY_WITHDRAWALS = 'empty_withdrawals', // 空提现列表
  FULL_WITHDRAWALS = 'full_withdrawals', // 完整提现列表
  WITHDRAWAL_PROCESSING = 'withdrawal_processing', // 提现中状态
  WITHDRAWAL_SUCCESS = 'withdrawal_success', // 提现成功状态
  WITHDRAWAL_FAILED = 'withdrawal_failed', // 提现失败状态
  WITHDRAWAL_MIXED = 'withdrawal_mixed', // 混合状态（包含各种状态的记录）
  EMPTY_TRANSACTIONS = 'empty_transactions', // 空交易列表
  FULL_TRANSACTIONS = 'full_transactions', // 完整交易列表
  // ... 其他场景
}

// 合并基础调试场景和Wallet特定场景
export type DebugScenario = BaseDebugScenario_Type | WalletDebugScenario;

// 创建Wallet调试器配置
const walletDebugConfig = new DebugConfigBuilder('Wallet')
  .addScenarios({
    [WalletDebugScenario.PAYMENT_UNAVAILABLE]: {
      displayName: '支付不可用',
      icon: '',
      description: '支付不可用',
      color: 'primary'
    },
    [WalletDebugScenario.BALANCE]: {
      displayName: '有余额',
      icon: '',
      description: '有余额',
      color: 'primary'
    },[WalletDebugScenario.NOBALANCE]: {
      displayName: '无余额',
      icon: '',
      description: '无余额',
      color: 'primary'
    },
    [WalletDebugScenario.EMPTY_WITHDRAWALS]: {
      displayName: '空提现列表',
      icon: '📭',
      description: '测试空提现记录状态',
      color: 'warning'
    },
    [WalletDebugScenario.FULL_WITHDRAWALS]: {
      displayName: '完整提现列表',
      icon: '📋',
      description: '测试完整提现记录列表',
      color: 'success'
    },
    [WalletDebugScenario.WITHDRAWAL_PROCESSING]: {
      displayName: '提现中状态',
      icon: '⏳',
      description: '测试提现中状态的记录',
      color: 'primary'
    },
    [WalletDebugScenario.WITHDRAWAL_SUCCESS]: {
      displayName: '提现成功状态',
      icon: '✅',
      description: '测试提现成功状态的记录',
      color: 'success'
    },
    [WalletDebugScenario.WITHDRAWAL_FAILED]: {
      displayName: '提现失败状态',
      icon: '❌',
      description: '测试提现失败状态的记录',
      color: 'danger'
    },
    [WalletDebugScenario.WITHDRAWAL_MIXED]: {
      displayName: '混合状态',
      icon: '🎭',
      description: '测试包含各种状态的提现记录',
      color: 'primary'
    },
  })
  .addMockDataGenerators({
    [WalletDebugScenario.PAYMENT_UNAVAILABLE]: () => generateMockUserData(WalletDebugScenario.PAYMENT_UNAVAILABLE),
    [WalletDebugScenario.BALANCE]: () => generateMockBalanceData(WalletDebugScenario.BALANCE),
    [WalletDebugScenario.NOBALANCE]: () => generateMockBalanceData(WalletDebugScenario.NOBALANCE),
    [WalletDebugScenario.EMPTY_WITHDRAWALS]: () => generateMockWithdrawalData(WalletDebugScenario.EMPTY_WITHDRAWALS),
    [WalletDebugScenario.FULL_WITHDRAWALS]: () => generateMockWithdrawalData(WalletDebugScenario.FULL_WITHDRAWALS),
    [WalletDebugScenario.WITHDRAWAL_PROCESSING]: () => generateMockWithdrawalData(WalletDebugScenario.WITHDRAWAL_PROCESSING),
    [WalletDebugScenario.WITHDRAWAL_SUCCESS]: () => generateMockWithdrawalData(WalletDebugScenario.WITHDRAWAL_SUCCESS),
    [WalletDebugScenario.WITHDRAWAL_FAILED]: () => generateMockWithdrawalData(WalletDebugScenario.WITHDRAWAL_FAILED),
    [WalletDebugScenario.WITHDRAWAL_MIXED]: () => generateMockWithdrawalData(WalletDebugScenario.WITHDRAWAL_MIXED),
  })
  .build();

// 创建Wallet调试器
export const walletDebugger = createDebugger(walletDebugConfig);

// Mock数据生成函数
function generateMockUserData(_scenario: WalletDebugScenario) {
  return {
    id: 1,
    name: '测试用户',
    // 根据场景返回不同的用户数据
  };
}

function generateMockBalanceData(scenario: WalletDebugScenario) {
  switch (scenario) {
    case WalletDebugScenario.BALANCE:
      return {
        availableAmount: 100.50,
        totalAmount: 150.00,
        pendingAmount: 49.50
      };
    case WalletDebugScenario.NOBALANCE:
      return {
        availableAmount: 0,
        totalAmount: 0,
        pendingAmount: 0
      };
    default:
      return {
        availableAmount: 100.50,
        totalAmount: 150.00,
        pendingAmount: 49.50
      };
  }
}

// 生成模拟提现记录数据
function generateMockWithdrawalData(scenario: WalletDebugScenario) {
  const baseRecord = {
    id: Date.now(),
    userId: 22118,
    outRequestNo: `WD${Date.now()}`,
    withdrawId: Math.floor(Math.random() * 10000),
    createTime: new Date().toISOString(),
    accountName: "福建朱雀科技有限公司",
    accountNumber: "6391****3333",
  };

  switch (scenario) {
    case WalletDebugScenario.EMPTY_WITHDRAWALS:
      return {
        code: 0,
        data: {
          list: [],
          total: 0
        },
        msg: ""
      };

    case WalletDebugScenario.WITHDRAWAL_PROCESSING:
      return {
        code: 0,
        data: {
          list: [
            {
              ...baseRecord,
              id: 1,
              amount: 100.00,
              status: "CREATE_SUCCESS",
              remark: "提现申请已提交",
              bankMemo: "处理中"
            },
            {
              ...baseRecord,
              id: 2,
              amount: 50.00,
              status: "INIT",
              remark: "等待处理",
              bankMemo: "初始化"
            }
          ],
          total: 2
        },
        msg: ""
      };

    case WalletDebugScenario.WITHDRAWAL_SUCCESS:
      return {
        code: 0,
        data: {
          list: [
            {
              ...baseRecord,
              id: 3,
              amount: 200.00,
              status: "SUCCESS",
              remark: "提现成功",
              bankMemo: "已到账",
              arrivalTime: new Date(Date.now() - 3600000).toISOString(), // 1小时前
              serialNumber: `SN${Date.now()}`
            }
          ],
          total: 1
        },
        msg: ""
      };

    case WalletDebugScenario.WITHDRAWAL_FAILED:
      return {
        code: 0,
        data: {
          list: [
            {
              ...baseRecord,
              id: 4,
              amount: 150.00,
              status: "FAIL",
              remark: "提现失败",
              bankMemo: "账户信息错误",
              failureReason: "账户余额不足，请重新提现"
            },
            {
              ...baseRecord,
              id: 5,
              amount: 75.00,
              status: "REFUND",
              remark: "已退款",
              bankMemo: "退款处理",
              failureReason: "银行卡信息有误"
            }
          ],
          total: 2
        },
        msg: ""
      };

    case WalletDebugScenario.WITHDRAWAL_MIXED:
      return {
        code: 0,
        data: {
          list: [
            {
              ...baseRecord,
              id: 6,
              amount: 300.00,
              status: "SUCCESS",
              remark: "提现成功",
              bankMemo: "已到账",
              arrivalTime: new Date(Date.now() - ********).toISOString(), // 1天前
              serialNumber: `SN${Date.now() - 1000}`
            },
            {
              ...baseRecord,
              id: 7,
              amount: 120.00,
              status: "CREATE_SUCCESS",
              remark: "提现申请已提交",
              bankMemo: "处理中"
            },
            {
              ...baseRecord,
              id: 8,
              amount: 80.00,
              status: "FAIL",
              remark: "提现失败",
              bankMemo: "处理失败",
              failureReason: "银行系统维护，请稍后重试"
            }
          ],
          total: 3
        },
        msg: ""
      };

    case WalletDebugScenario.FULL_WITHDRAWALS:
    default:
      return {
        code: 0,
        data: {
          list: Array.from({ length: 10 }, (_, index) => ({
            ...baseRecord,
            id: 100 + index,
            amount: Math.floor(Math.random() * 500) + 50,
            status: ["SUCCESS", "CREATE_SUCCESS", "FAIL"][index % 3],
            remark: `测试提现记录 ${index + 1}`,
            bankMemo: `备注 ${index + 1}`,
            ...(index % 3 === 0 ? {
              arrivalTime: new Date(Date.now() - index * 3600000).toISOString(),
              serialNumber: `SN${Date.now() - index * 1000}`
            } : {}),
            ...(index % 3 === 2 ? {
              failureReason: `测试失败原因 ${index + 1}`
            } : {})
          })),
          total: 10
        },
        msg: ""
      };
  }
}

// 导出调试器的方法
export const isDebugMode = walletDebugger.isDebugMode;
export const getDebugScenario = walletDebugger.getDebugScenario;
export const debugLog = walletDebugger.debugLog;
export const mockApiResponse = walletDebugger.mockApiResponse;