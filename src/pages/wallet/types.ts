// 我的钱包相关类型定义

/** 通用 API 响应结构 */
export interface CommonResult<T = any> {
  /** 错误码，0 表示成功 */
  code: number;
  /** 返回数据 */
  data: T;
  /** 错误提示信息 */
  msg: string;
}

/** 分页结果结构 */
export interface PageResult<T = any> {
  /** 数据列表 */
  list: T[];
  /** 总数量 */
  total: number;
}

/** 分页信息接口 */
export interface PaginationInfo {
  /** 当前页码 */
  page: number;
  /** 每页条数 */
  limit: number;
  /** 是否还有更多数据 */
  hasMore: boolean;
  /** 是否正在加载 */
  isLoading: boolean;
}

/** 我的钱包接口 */
export interface WalletInfo {
  id?: number;
  /** 扩展 */
  [key: string]: any;
}

export interface WithdrawalRecord {
  /** 编号 */
  id: number;
  /** 扩展 */
  [key: string]: any;
}

/** 获取提现记录请求接口 */
export interface GetWithdrawalListRequest {
  /** 页码 */
  pageNo?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 用户ID */
  userId?: number;
}

/** getVipOrderList API 响应类型 */
export type GetWithdrawalListResponse = CommonResult<PageResult<WithdrawalRecord>>;


/** 获取交易记录请求接口 */
export interface TransactionRecord {
  /** 编号 */
  id: number;
  /** 扩展 */
  [key: string]: any;
}

/** 获取提现记录请求接口 */
export interface GetWithdrawalListRequest {
  /** 页码 */
  pageNo?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 用户ID */
  userId?: number;
}

/** getVipOrderList API 响应类型 */
export type GetTransactionListResponse = CommonResult<PageResult<TransactionRecord>>;