@import '@arco-design/mobile-react/style/mixin.less';

// 页面根容器样式
[id^="/pages/wallet/transactions/detail/index"] {
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
}

// 页面主容器
.transactionDetailPage {
  position: relative;
  width: 100%;
  min-height: 100vh;
  
  // 内容区域
  .content {
    padding: 30px 0px 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
  }
  
  // 金额展示区域
  .amount-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    
    .transaction-type {
      font-size: 15px;
      font-weight: bold;
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: var(--dark-font-color) !important;
      });
    }
    
    .amount-text {
      font-size: 24px;
      font-weight: bold;
      opacity: 0.9;
      
      &.income {
        .use-var(color, success-color);
        .use-dark-mode-query({
          color: var(--dark-success-color) !important;
        });
      }
      
      &.expense {
        color: #EB483F;
        .use-dark-mode-query({
          color: #F76965 !important;
        });
      }
    }
  }
  
  // 详情信息区域
  .detail-section {
    width: 100%;

    .detail-group {
      display: flex;
      flex-direction: column;
      //align-items: center;
      justify-content: center;
      gap: 10px;
      .use-var(background-color, background-color);
      .use-dark-mode-query({
        background-color: var(--dark-background-color) !important;
      });
      // border-radius: 4px;
      //overflow: hidden;
      box-sizing: border-box;
      border-width: 0.5px 0px 0px 0px;
      border-style: solid;
      border-color: #E5E6EB;
      margin: 0 16px; // 添加左右边距，避免边框占满页面宽度
      padding: 15px 0px;


      
      .detail-item {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        gap: 15px;
        padding: 0px 16px;
        // border-bottom: 0.5px solid var(--line-color);
        .use-dark-mode-query({
          border-bottom-color: var(--dark-line-color) !important;
        });
        
        // &:first-child {
        //   border-bottom: none;
        // }
        
        
        // 标签样式
        .detail-item-label-text {
          font-size: 14px;
          font-weight: 500;
          .use-var(color, sub-info-font-color);
          .use-dark-mode-query({
            color: var(--dark-sub-info-font-color) !important;
          });
          min-width: 56px;
          margin-right: 15px;
        }
        
        // 值样式
        .detail-item-value-text {
          font-size: 14px;
          font-weight: 500;
          .use-var(color, font-color);
          .use-dark-mode-query({
            color: var(--dark-font-color) !important;
          });
          // text-align: right;
          flex: 1;
        }
        
        // 银行信息特殊样式
        .bank-info {
          display: flex;
          flex-direction: column;
          gap: 5px;
          align-items: flex-end;

          .bank-name {
            font-size: 14px;
            font-weight: 500;
            .use-var(color, font-color);
            .use-dark-mode-query({
              color: var(--dark-font-color) !important;
            });
          }

          .bank-card {
            font-size: 14px;
            font-weight: 500;
            .use-var(color, font-color);
            .use-dark-mode-query({
              color: var(--dark-font-color) !important;
            });
          }
        }

        // 账户信息特殊样式
        .account-info {
          display: flex;
          flex-direction: column;
          gap: 5px;
          align-items: flex-end;

          .account-name {
            font-size: 14px;
            font-weight: 500;
            .use-var(color, font-color);
            .use-dark-mode-query({
              color: var(--dark-font-color) !important;
            });
          }

          .account-number {
            font-size: 14px;
            font-weight: 500;
            .use-var(color, font-color);
            .use-dark-mode-query({
              color: var(--dark-font-color) !important;
            });
          }
        }

        // 多行信息通用样式
        .multiline-info {
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .multiline-text {
            font-size: 14px;
            font-weight: 500;
            .use-var(color, font-color);
            .use-dark-mode-query({
              color: var(--dark-font-color) !important;
            });
          }
        }

        // 自定义渲染器样式
        .custom-fee-info {
          display: flex;
          flex-direction: column;
          gap: 5px;
          align-items: flex-end;
        }
      }
    }
  }
  
  // 加载状态
  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
    
    text {
      font-size: 15px;
      .use-var(color, sub-info-font-color);
      .use-dark-mode-query({
        color: var(--dark-sub-info-font-color) !important;
      });
    }
  }
}
