@import '@arco-design/mobile-react/style/mixin.less';

[id^="/pages/wallet/withdrawal/index"] {
  background-color: @card-background-color !important;
  .use-dark-mode-query({
    background-color: @dark-card-background-color !important;
  });
}

.withdrawal {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: #F7F8FA; // 填充 Fill/填充fill-1
  .use-dark-mode-query({
    background-color: @dark-card-background-color;
  });

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50vh;
  }

  .withdrawal-content {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 84px); // 减去导航栏高度
  }

  // 收款账户信息
  .account-info {
    background-color: #FFFFFF; // 填充 Fill/Container 容器背景色
    .use-dark-mode-query({
        background-color: @dark-container-background-color;
    });
    padding: 20px 15px;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 15px;

    .account-info-header {
      display: flex;
      align-items: center;
      gap: 5px;

      .account-info-label {
        font-family: 'PingFangSC-Medium';
        font-size: 14px;
        line-height: 140%;
        color: #4E5969; // 文字 Text/文字-4-副标- Grey 8
        .use-dark-mode-query({
            color: @dark-sub-font-color;
        });
        opacity: 0.9;
      }

      .account-info-edit {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        cursor: pointer;


        color: #fff !important; // 使用 !important 强制覆盖
        .use-dark-mode-query({
            fill: @dark-sub-font-color !important; // 在暗黑模式下也使用 !important
        });

      }
    }

    .account-info-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .account-name {
        font-family: 'PingFangSC-Medium';
        font-size: 14px;
        line-height: 140%;
        color: #1D2129; // 文字 Text/文字-5-基础  Grey 10
        .use-dark-mode-query({
            color: @dark-font-color;
        });
        opacity: 0.9;
      }

      .account-number {
        font-family: 'PingFangSC-Medium';
        font-size: 14px;
        line-height: 140%;
        color: #4E5969; // 文字 Text/文字-4-副标- Grey 8
        .use-dark-mode-query({
            color: @dark-sub-font-color;
        });
        opacity: 0.9;
      }
    }
  }

  .account-info-loading {
    background-color: #FFFFFF;
    .use-dark-mode-query({
        background-color: @dark-container-background-color;
    });
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // 提现表单
  .withdrawal-form {
    background-color: #FFFFFF; // 填充 Fill/Container 容器背景色
    .use-dark-mode-query({
        background-color: @dark-container-background-color;
    });
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 20px;
    margin-top: 10px;

    .amount-input-section {
      padding: 20px 0;

      .amount-input-header {
        margin-bottom: 25px;

        .amount-input-label {
          font-family: 'PingFangSC-Medium';
          font-size: 14px;
          line-height: 140%;
          color: #1D2129; // 文字 Text/文字-5-基础  Grey 10
          .use-dark-mode-query({
            color: @dark-font-color;
          });
          opacity: 0.9;
        }
      }

      .amount-input-container {
        margin-bottom: 5px;

        .amount-input-wrapper {
          display: flex;
          align-items: center;
          border-bottom: 0.5px solid #E5E6EB; // 线条 Line/线条line-1-基础
          .use-dark-mode-query({
            border-color: @dark-line-color;
          });
          padding-bottom: 10px;

          .currency-symbol {
            font-family: 'PingFangSC-Medium';
            font-size: 16px;
            line-height: 140%;
            color: #1D2129; // 文字 Text/文字-5-基础  Grey 10
            .use-dark-mode-query({
                color: @dark-font-color;
            });
            margin-right: 16px;
          }

          .amount-input {
            flex: 1;
            border: none;
            outline: none;
            background: transparent;
            font-family: 'PingFangSC-Medium';
            font-size: 15px;
            line-height: 140%;
            color: #1D2129;
            .use-dark-mode-query({
                color: @dark-font-color;
            });

            &::placeholder {
              color: #C9CDD4; // 文字 Text/文字-2-禁用- Grey 4
              .use-dark-mode-query({
                color: @dark-disabled-color;
              });
            }
          }
        }
      }

      .error-message {
        margin-top: 4px;
        font-size: 12px;
        line-height: 16px;
        color: #F53F3F;
        .use-dark-mode-query({
            color: @dark-danger-color;
        });
      }

      .amount-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;

        .available-amount {
          font-family: 'PingFangSC-Medium';
          font-size: 14px;
          line-height: 140%;
          color: #4E5969; // 文字 Text/文字-4-副标- Grey 8
          .use-dark-mode-query({
            color: @dark-sub-font-color;
          });
          opacity: 0.9;
        }

        .withdraw-all {
          font-family: 'PingFangSC-Medium';
          font-size: 14px;
          line-height: 140%;
          color: #002C8C; // Primary/colorPrimaryText
          .use-dark-mode-query({
            color: @dark-primary-color;
          });
          cursor: pointer;
        }
      }
    }

    .withdrawal-submit {
      padding: 20px 0;

      .disabled-button {
        opacity: 0.6;
      }
    }

    .withdrawal-tips {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px 0;

      .tips-text {
        font-family: 'PingFangSC-Medium';
        font-size: 14px;
        line-height: 140%;
        color: #86909C; // 文字 Text/文字-3-附加信息-Grey 6
        .use-dark-mode-query({
            color: @dark-sub-info-font-color;
        });
        text-align: center;
      }
    }
  }

  // 确认提现弹窗样式
  .withdrawal-confirm-dialog {
    .dialog-content {
      text-align: center;
      padding: 20px 0;

      .dialog-amount {
        margin-bottom: 20px;

        .dialog-amount-text {
          font-family: 'PingFang SC';
          font-size: 24px;
          font-weight: bold;
          line-height: 140%;
          color: #1D2129; // 文字 Text/文字-5-基础  Grey 10
          .use-dark-mode-query({
            color: @dark-font-color;
          });
        }
      }

      .dialog-fee {
        border-top: 0.5px solid rgba(153, 153, 153, 0.2);
        .use-dark-mode-query({
            border-color: fade(@dark-line-color, 20%);
        });
        padding-top: 20px;

        .dialog-fee-row {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .dialog-fee-label {
            font-family: 'PingFangSC-Medium';
            font-size: 14px;
            line-height: 140%;
            color: #4E5969; // 文字 Text/文字-4-副标- Grey 8
            .use-dark-mode-query({
                color: @dark-sub-font-color;
            });
            opacity: 0.9;
          }

          .dialog-fee-value {
            font-family: 'PingFangSC-Medium';
            font-size: 14px;
            line-height: 140%;
            color: #1D2129; // 文字 Text/文字-5-基础  Grey 10
            .use-dark-mode-query({
                color: @dark-font-color;
            });
            opacity: 0.9;
          }
        }
      }
    }
  }
}
