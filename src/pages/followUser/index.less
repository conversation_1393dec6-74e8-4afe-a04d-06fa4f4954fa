.follow-user {
  width: 100%;
  height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .nav-bar {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
    
    .nav-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }
  }
  
  .avatar-container {
    margin-top: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .avatar {
      width: 120px;
      height: 120px;
      border-radius: 60px;
      margin-bottom: 20px;
      border: 2px solid #eee;
    }
    
    .nickname {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
    }
    
    .user-info {
      font-size: 14px;
      color: #666;
      text-align: center;
      margin-top: 5px;
    }
  }
  
  .button-container {
    margin-top: 60px;
    width: 80%;
    
    .follow-button {
      width: 100%;
      height: 44px;
      background: linear-gradient(to right, #FF6B6B, #FF8E8E);
      border-radius: 22px;
      font-size: 16px;
      color: #fff;
      font-weight: bold;
      border: none;
      
      &.blue {
        background: linear-gradient(to right, #4776E6, #8E54E9);
      }
    }
  }
} 

// 微信重定向视图样式
.follow-user-wx {
  width: 100%;
  height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .avatar-container {
    margin-top: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .avatar {
      width: 120px;
      height: 120px;
      border-radius: 60px;
      margin-bottom: 20px;
    }
    
    .nickname {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
    }
    
    .user-stats {
      display: flex;
      flex-direction: row;
      justify-content: center;
      margin: 5px 0 10px;
      
      .stat-item {
        font-size: 14px;
        color: #666;
        margin: 0 10px;
        
        &:first-child {
          position: relative;
          
          &:after {
            content: '';
            position: absolute;
            right: -10px;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 12px;
            background-color: #ddd;
          }
        }
      }
    }
    
    .hint-text {
      font-size: 14px;
      color: #666;
      margin-top: 10px;
      text-align: center;
    }
  }
  
  .button-container {
    margin-top: 60px;
    width: 80%;
    
    .follow-button.green {
      width: 100%;
      height: 44px;
      background: linear-gradient(to right, #4CAF50, #8BC34A);
      border-radius: 22px;
      font-size: 16px;
      color: #fff;
      font-weight: bold;
      border: none;
    }
  }
} 