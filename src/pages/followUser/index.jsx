import { View, Text } from '@tarojs/components'
import { Button, Image } from '@arco-design/mobile-react'
import './index.less'
import Taro from '@tarojs/taro'

import { socialLogin, getUserInfo, followUser, getFollowUserInfo, getSocailAuthRedirect } from '@/utils/api/common/common_user'
import { useState, useEffect } from 'react'
import YkNavBar from '@/components/ykNavBar'
import md5 from 'js-md5'
import {URL_BASE} from '@/utils/api/urls';

export default function FollowUser() {
  const [userInfo, setUserInfo] = useState({});
  const [friendId, setFriendId] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [friendInfo, setFriendInfo] = useState({});
  const [showLandingView, setShowLandingView] = useState(true);

  useEffect(() => {
    // 从URL获取user_id参数, 获取被关注者的信息
    const params = Taro.getCurrentInstance().router.params;
    console.log("[Taro解析的params]: " + JSON.stringify(params));

    // 添加完整的URL日志，检查当前URL中是否包含code
    const currentUrl = window.location.href;
    console.log("[当前完整URL]: " + currentUrl);
     
    if (params && params.uid) {
      setFriendId(params.uid);
      getFollowUserInfo({ userId: params.uid }).then(res => {
        if (res.code === 0) {
          setFriendInfo(res.data);
          setIsLoading(false);
          console.log("[friendInfo]: " + JSON.stringify(res.data));
        }
      }).catch(err => {
        console.error('获取关注用户信息失败:', err);
        setIsLoading(false);
      });
    } else {
      // 没有uid时，直接进行微信授权跳转
      console.error('请求参数中没有uid！');
    }
    
    if (params && params.redirect === 'wx') {
      // 如果redirect=wx，则直接进行微信授权跳转      
      console.log("[检测到redirect=wx参数，准备跳转微信授权]");
      redirectToWxAuth(params.uid);
      return;
    }
    
    if (params && params.uid && params.code && params.state) {
      setShowLandingView(false)
      // 如果有code参数，说明是从微信授权回来的，执行关注操作
      console.log("[检测到code参数，准备进行微信登录] code:"+params.code+" state:"+params.state);
      handleWechatLogin(params.code,params.state);
    }

  }, []);

  // 根据showLandingView状态决定显示哪个视图
  useEffect(() => {
    console.log("[useEffect] 执行");
    if (showLandingView) {
      // 添加按钮到LandingView
      renderLandingView();
    } else {
      // 添加按钮到LandingView
      renderFollowSuccessView();
    }
  }, [showLandingView]);

  // 重定向到微信授权页面
  const redirectToWxAuth = (userId) => {
    const strState = md5(new Date().getTime().toString());
    const baseUrl = Taro.getStorageSync('baseUrl') || URL_BASE;
    
    // 简化重定向URI，我们的路由拦截器会处理URL格式修正
    //const redirectUri = `${baseUrl}/#/pages/followUser/index?uid=${userId}`;
    const redirectUri = `${baseUrl}/#/pages/followUser/index?uid=${userId}`;
    console.log("[重定向URI模板]: " + redirectUri);
    
    // 正确编码重定向URI
    const encodedRedirectUri = encodeURIComponent(redirectUri);
    
    const wxAuthUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxcdb8c9ecd7cda4b5&redirect_uri=${encodedRedirectUri}&response_type=code&scope=snsapi_userinfo&state=${strState}#wechat_redirect`;
    
    console.log("[跳转到微信授权页]: " + wxAuthUrl);
    
    // 跳转到微信授权页面
    window.location.href = wxAuthUrl;
  };

  // 微信登录处理
  const handleWechatLogin = (code,state) => {
    let data = {
      type: 31,
      code: code,
      state: state
    }
    console.log("[开始处理微信登录]: " + JSON.stringify(data));

    socialLogin(data).then((res) => {
      console.log("[微信登录返回]: " + JSON.stringify(res));
      if (res.code === 0) {
        Taro.setStorageSync('userInfo', res.data);
        console.log("[微信登录成功，存储用户信息]");

        getUserInfo().then((res2) => {
          console.log("[获取详细用户信息]: " + JSON.stringify(res2));
          if (res2.code === 0) {
            //合并用户信息
            Taro.setStorageSync('userInfo', {
              ...res2.data,
              ...res.data
            });
            setUserInfo(res2.data);
            setIsLoading(false);
            console.log("[合并用户信息完成]");
            
            // 从URL获取user_id参数, 获取被关注者的信息
            const params = Taro.getCurrentInstance().router.params;
            console.log("[Taro解析的params]: " + JSON.stringify(params));
            console.log("[friendId :>>>>>>>>>]friendId " + friendId + "friendInfo " + JSON.stringify(friendInfo));
            // 登录成功后自动执行关注
            if (params && params.uid) {
              console.log("[有friendId，准备关注用户]: " + params.uid);
              console.log("[当前用户信息]: " + JSON.stringify(userInfo));
              let followUserData = {
                userId: res2.data.id,
                followId: params.uid
              }
              console.log("[followUserData]: " + JSON.stringify(followUserData))
              followUser(followUserData).then((res3) => {
                console.log("[关注接口返回]： " + JSON.stringify(res3))
                if(res3.code === 0) {
                  console.log("[关注成功] " )
                } else {
                  console.error("[关注失败]: " + res.msg);
                }
              })
            } else {
              // 如果没有friendId，返回首页
              console.log("[没有friendId，准备返回首页]");
              //Taro.reLaunch({ url: '/pages/index/index' });
            }
          }
        });
      } else {
        console.error("[微信登录失败]: " + res.msg);
        Taro.showToast({
          title: res.msg || '登录失败',
          icon: 'none'
        });
        setIsLoading(false);
      }
    }).catch(err => {
      console.error('[社交登录异常]:', err);
      Taro.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
      setIsLoading(false);
    });
  };

  // 跳转到应用市场 todo
  const goToAppStore = () => {
    // 获取当前系统信息
    Taro.getSystemInfo({
      success: function(res) {
        const system = res.platform;
        
        if (system === 'android') {
          // Android平台跳转到应用宝或其他应用市场
          // 可以使用应用宝的URL Scheme或者其他应用市场的链接
          window.location.href = 'market://details?id=com.yourcompany.appname';
          
          // 如果上面的链接无法打开，可以尝试直接跳转到应用宝网页版
          setTimeout(() => {
            window.location.href = 'https://a.app.qq.com/o/simple.jsp?pkgname=com.yourcompany.appname';
          }, 500);
        } else if (system === 'ios') {
          // iOS平台跳转到App Store
          window.location.href = 'itms-apps://itunes.apple.com/app/idYOUR_APP_ID';
          
          // 如果上面的链接无法打开，可以尝试直接跳转到App Store网页版
          setTimeout(() => {
            window.location.href = 'https://apps.apple.com/app/idYOUR_APP_ID';
          }, 500);
        } else {
          // 其他平台，可以提供一个通用的下载页面
          window.location.href = 'https://www.yourwebsite.com/download';
        }
      }
    });
  };

  // 渲染微信重定向视图（图片中的视图）
  const renderLandingView = () => {
    // 格式化用户ID，如果为数字则补零至5位数
    const formattedUserId = friendInfo.userId ? 
      (typeof friendInfo.userId === 'number' ? 
        `用户${friendInfo.userId.toString().padStart(5, '0')}` : 
        `用户${friendInfo.userId}`) : 
      '未知账号';
      
    return (
      <View className='follow-user-wx'>
        <View className='avatar-container'>
          <Image 
            className='avatar' 
            src={friendInfo.avatar || require('../../assets/images/login/logo.png')} 
            bottomOverlap={null}
          />
          <Text className='nickname'>{formattedUserId}</Text>
          <Text className='hint-text'>点击"使用完整服务"关注查看我的相册</Text>
        </View>
      </View>
    );
  };

  // 渲染正常视图，展示关注成功页面
  const renderFollowSuccessView = () => {
    const useDays = friendInfo.useDays || 0;
    const postCount = friendInfo.postCount || 0;
    
    return (
      <View className='follow-user'>
        <View className='nav-bar'>
          <Text className='nav-title'>关注好友</Text>
        </View>
        
        <View className='avatar-container'>
          <Image 
            className='avatar' 
            src={friendInfo.headImg || require('../../assets/images/login/logo.png')} 
            bottomOverlap={null}
          />
          <Text className='nickname'>关注成功</Text>
          <Text className='user-info'>Ta已经使用{useDays}天，累计发布{postCount}条图文</Text>
        </View>

        <View className='button-container'>
          <Button
            className='follow-button blue'
            onClick={() => Taro.reLaunch({ url: '/pages/index/index' })}
            loading={isLoading}
          >
            立即前往
          </Button>
        </View>
      </View>
    );
  };

  // 根据showLandingView状态决定显示哪个视图
  return showLandingView ? renderLandingView() : renderFollowSuccessView();
} 