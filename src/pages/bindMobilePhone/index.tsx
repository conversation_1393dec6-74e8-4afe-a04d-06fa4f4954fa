import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Input, Button, CountDown, Dialog } from "@arco-design/mobile-react";
import React from "react";
import Taro from "@tarojs/taro";
import {
  bindingPhone,
  getSmsCode,
  replacePhone,
} from "@/utils/api/common/common_user";
import { toast } from "@/utils/yk-common";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

export default function bindMobilePhone() {
 
  // 用户信息
  const [userInfo, setUserInfo] = React.useState<any>(false); // 类型：any[]
  // 旧手机号
  const [oldPhone, setOldPhone] = React.useState("");
  // 手机号
  const [phone, setPhone] = React.useState("");
  // 旧验证码
  const [oldCode, setOldCode] = React.useState("");
  //验证码倒计时
  const [oldCountDown, setOldCountDown] = React.useState(60);
  // 是否开启倒计时
  const [oldAutoStart, setOldAutoStart] = React.useState(false);
  // 验证码
  const [code, setCode] = React.useState("");
  //验证码倒计时
  const [countDown, setCountDown] = React.useState(60);
  // 是否开启倒计时
  const [autoStart, setAutoStart] = React.useState(false);
  // 是否已绑定手机号
  const [isBindPhone, setIsBindPhone] = React.useState(false); // 类型：boolean[]

  // 倒计时结束
  const onFinishCountDown = () => {
    setAutoStart(false);
    setCountDown(60);
  };
  const onFinishOldCountDown = () => {
    setOldAutoStart(false);
    setOldCountDown(60);
  };
  

  // 获取验证码
  const getCode = (phoneVal) => {
    if (phoneVal.length !== 11) {
      Taro.showToast({
        title: "请输入正确的手机号",
        icon: "none",
      });
      return;
    }
    if (
      userInfo.unionid == "" ||
      !userInfo.unionid ||
      userInfo.unionid == undefined
    ) {
      Taro.showToast({
        title: "请先绑定微信号",
        icon: "none",
      });
      return;
    }
    setAutoStart(true);
    let data = {
      mobile: phoneVal,
      scene: 5, //5:绑定手机号
    };
    getSmsCode(data)
      .then((res: any) => {
        if (res.code == 0) {
          toast("success", {
            content: "验证码发送成功",
            duration: 2000,
          });
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        toast("error", {
          content: err,
          duration: 2000,
        });
      });
  };

  // 初次绑定手机号
  const handleConfirmBind = () => {
    if (phone.length !== 11) {
      Taro.showToast({
        title: "请输入正确的手机号",
        icon: "none",
      });
      return;
    }
    if (code.length < 4) {
      Taro.showToast({
        title: "请输入正确的验证码",
        icon: "none",
      });
      return;
    }
    if (
      userInfo.unionid == "" ||
      !userInfo.unionid ||
      userInfo.unionid == undefined
    ) {
      Taro.showToast({
        title: "请先绑定微信号",
        icon: "none",
      });
      return;
    }
    let data = {
      phone: phone,
      code: code,
      unionid: userInfo.unionid,
    };
    bindingPhone(data)
      .then((res: any) => {
        if (res.code == 0) {
          toast("success", {
            content: "绑定成功",
            duration: 2000,
          });
          setCode("");
          setPhone("");
          setAutoStart(false);
          setCountDown(60);
          let _window: any = window;
          _window.modalInstance = Dialog.alert({
            children: `已成功绑定手机号：${phone}`,
            platform: "ios",
            okText: "我知道了",
          });
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        toast("error", {
          content: err,
          duration: 2000,
        });
      });
  };

  // 更换绑定手机号
  const handleChangeBind = () => {
    if (oldPhone.length !== 11 || oldPhone != userInfo.mobile) {
      Taro.showToast({
        title: "请输入正确的旧手机号",
        icon: "none",
      });
      return;
    }
    if (phone.length !== 11) {
      Taro.showToast({
        title: "请输入正确的手机号",
        icon: "none",
      });
      return;
    }
    if (phone == oldPhone) {
      Taro.showToast({
        title: "新手机号不能与旧手机号相同",
        icon: "none",
      });
    }
    if (oldCode.length < 4) {
      Taro.showToast({
        title: "请输入旧手机号的正确的验证码",
        icon: "none",
      });
      return;
    }
    if (code.length < 4) {
      Taro.showToast({
        title: "请输入正确的验证码",
        icon: "none",
      });
      return;
    }
    if (
      userInfo.unionid == "" ||
      !userInfo.unionid ||
      userInfo.unionid == undefined
    ) {
      Taro.showToast({
        title: "请先绑定微信号",
        icon: "none",
      });
      return;
    }
    let data = {
      phone: oldPhone,
      code: oldCode,
      new_phone: phone,
      new_code: code,
    };
    replacePhone(data).then((res: any) => {
      if (res.code == 0) {
        setCode("");
        setPhone("");
        setAutoStart(false);
        setCountDown(60);
        setOldCode("");
        setOldPhone("");
        setOldAutoStart(false);
        setOldCountDown(60);
        let _window: any = window;
        _window.modalInstance = Dialog.alert({
          children: `已成功更换绑定手机号：${phone}`,
          platform: "ios",
          okText: "我知道了",
        });
      } else {
        toast("error", {
          content: res.msg,
          duration: 2000,
        });
      }
    }).catch((err) => {
      toast("error", {
        content: err,
        duration: 2000,
      });
    });
  };

  useLoad(() => {
    // 获取用户信息
    let info: any = Taro.getStorageSync("userInfo").userInfo;
    setUserInfo(info);
    // setIsBindPhone(info.phone != "" && info.phone != undefined);
  });

  return (
    <View className="bindMobilePageContent">
      <YkNavBar title="绑定手机号" />
      <View className="pageDescribe">
        <Text className="pageDescribe-title">绑定手机号</Text>
        <Text className="pageDescribe-desc">
          绑定手机号后即可使用手机号登录
        </Text>
      </View>
      <View className="inputBox">
        {isBindPhone && (
          <>
            <Input
              label="旧手机号"
              placeholder="请输入旧手机号"
              clearable
              border="none"
              onChange={(_, value) => setOldPhone(value)}
              onClear={() => setOldPhone("")}
              value={oldPhone}
            />
            <Input
              label="验证码"
              placeholder="请输入验证码"
              validator={(val) => val.length <= 8}
              className="demo-input-btn-input"
              clearable
              value={oldCode}
              onChange={(_, value) => setOldCode(value)}
              onClear={() => setOldCode("")}
              border="none"
              suffix={
                !autoStart ? (
                  <Button
                    inline
                    size="mini"
                    type="ghost"
                    onClick={() => getCode(oldPhone)}
                  >
                    获取验证码
                  </Button>
                ) : (
                  <CountDown
                    millisecond
                    format="ss"
                    time={{
                      days: 0,
                      hours: 0,
                      minutes: 0,
                      seconds: oldCountDown,
                      milliseconds: 0,
                    }}
                    autoStart={oldAutoStart}
                    onFinish={onFinishOldCountDown}
                    renderChild={(timeData) => (
                      <Button
                        inline
                        size="mini"
                        type="ghost"
                        disabled
                      >
                        重新获取({timeData.seconds}s)
                      </Button>
                    )}
                  />
                )
              }
            />
          </>
        )}
        <Input
          label="手机号"
          placeholder="请输入手机号"
          clearable
          border="none"
          onChange={(_, value) => setPhone(value)}
          onClear={() => setPhone("")}
          value={phone}
        />
        <Input
          label="验证码"
          placeholder="请输入验证码"
          validator={(val) => val.length <= 8}
          className="demo-input-btn-input"
          clearable
          value={code}
          onChange={(_, value) => setCode(value)}
          onClear={() => setCode("")}
          border="none"
          suffix={
            !autoStart ? (
              <Button
                inline
                size="mini"
                type="ghost"
                onClick={() => getCode(phone)}
              >
                获取验证码
              </Button>
            ) : (
              <CountDown
                millisecond
                format="ss"
                time={{
                  days: 0,
                  hours: 0,
                  minutes: 0,
                  seconds: countDown,
                  milliseconds: 0,
                }}
                autoStart={autoStart}
                onFinish={onFinishCountDown}
                renderChild={(timeData) => (
                  <Button
                    inline
                    size="mini"
                    type="ghost"
                    disabled
                  >
                    重新获取({timeData.seconds}s)
                  </Button>
                )}
              />
            )
          }
        />
      </View>
      <View className="confirmBtnBox">
        {isBindPhone ? (
          <Button
            needActive
            onClick={() => handleChangeBind()}
            disabled={phone.length < 11 || code.length < 4}
          >
            更换绑定
          </Button>
        ) : (
          <Button
            needActive
            onClick={() => handleConfirmBind()}
            disabled={ phone.length < 11 || code.length < 4}
          >
            绑定
          </Button>
        )}
      </View>
    </View>
  );
}
