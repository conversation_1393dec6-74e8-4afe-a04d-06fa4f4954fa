import React, { useState, useEffect, useRef } from 'react';
import { View, Input, Button, Text } from '@tarojs/components';
import { Dialog, SearchBar, Popover } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import Taro from "@tarojs/taro";
import {  getFormat, getColor, createFormat, createColor, delFormat, editFormat, delColor, editColor  } from "@/utils/api/common/common_user";
import './index.less';


export default function SelectProductColor() {
  const [search, setSearch] = useState('');
  const [formats, setFormats] = useState<any[]>([]);
  const [colors, setColors] = useState<any[]>([]);
  const [selectedFormats, setSelectedFormats] = useState<any[]>([]);
  const [selectedColors, setSelectedColors] = useState<any[]>([]);
  const [popoverFormat, setPopoverFormat] = useState<{[key: string]: boolean}>({});
  const [editValue, setEditValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  

    const getFormatList = async () => {
      const data = {
        pageNo: 1,
        pageSize: 100, 
        userId: 299,
      }
      const res: any = await getFormat(data);
      if (res.code == 0) {
        if (res.data.list.length > 0) {   
          setFormats(res.data.list);
        } else {
          setFormats([]);
        }
      }
    }

    const getColorList = async () => {
      const data = {
        pageNo: 1,
        pageSize: 100, 
        userId: 299,
      }
      const res: any = await getColor(data);
      if (res.code == 0) {
        if (res.data.list.length > 0) {
          setColors(res.data.list);
        } else {
          setColors([]);
        }
      }
    }

  // 获取规格
  useEffect(() => {
    getColorList()
    getFormatList()

  }, []);


  // 规格选择
  const toggleFormat = (item: any) => {
    setSelectedFormats(prev =>
      prev.some(f => f.id === item.id) ? prev.filter(f => f.id !== item.id) : [...prev, item]
    );
  };

  // 颜色选择
  const toggleColor = (item: any) => {
    setSelectedColors(prev =>
      prev.some(c => c.id === item.id) ? prev.filter(c => c.id !== item.id) : [...prev, item]
    );
  };

  // 添加规格
  const addFormat = () => {
    const value = prompt('请输入新规格');
    if (!value) return;
    // 判断是否已存在
    if (formats.some(f => f.name === value)) {
      return;
    }
    const data={
      userId: 299,
      name: value
    }

    createFormat(data).then((res: any) => {  
      if(res.code == 0) {
        const format = {
          id: res.data,
          name: value,
        };
        setFormats([...formats, format]);
      }
    })    

  };

  // 添加颜色
  const addColor = () => {
    const value = prompt('请输入新颜色');
    if (!value) return;
    // 判断是否已存在
    if (colors.some(c => c.name === value)) {
      return;
    }
    const data={
      userId: 299,
      name: value
    }
    createColor(data).then((res: any) => {  
      if(res.code == 0) {
        const color = {
          id: res.data,
          name: value,
        };
        setColors([...colors, color]);
      }
    })   

  };

  const editorFormat=(id,type)=>{
    // 找到当前规格的名称作为默认值
    // const currentFormat = formats.find(f => f.id === id);
    // setEditValue(currentFormat?.name || '');
    let title = '';
    let placeholder = '';
    let showToastTitle = '';
    if(type=='format'){
       title='修改规格'
       placeholder='请输入规格'
       showToastTitle='请输入规格名称'
    }else{
       title='修改颜色'
       placeholder='请输入颜色'
       showToastTitle='请输入颜色名称'
    }

    Dialog.confirm({
      platform: 'ios',
      className: 'dialog-input-demo',
      title: title,
      contentAlign: 'left',
      children: (<>
          <input 
            ref={inputRef}
            className="dialog-input-demo-input" 
            placeholder={placeholder} 
            onChange={(e) => {setEditValue(e.target.value)}}
          />
      </>),
      footerType: 'button',
      okText: '取消',
      cancelText: '确定',
            
      onCancel: async () => {
        // 直接从 inputRef 获取当前值
        const currentValue = inputRef.current?.value || editValue;
        
        if (!currentValue.trim()) {
          Taro.showToast({
            title: showToastTitle,
            icon: 'none',
            duration: 2000
          });
          return;
        }
        
        Taro.showLoading({
          title: '编辑中...',
          mask: true
        });
        try {
          const data = {
            id: id,
            userId: 299,
            name: currentValue
          }
          if(type=='format'){      
            editFormat(data).then((res: any) => {  
              if(res.code == 0) {  
                setTimeout(() => {
                  Taro.hideLoading();
                  Taro.showToast({
                    title: '编辑成功',
                    icon: 'none',
                    duration: 2000
                  });
                }, 1000);
                // 更新本地数据
                setFormats(prev => prev.map(f => 
                  f.id === id ? { ...f, name: currentValue } : f
                ));
                setEditValue('');
              }
            })
          }else{
            editColor(data).then((res: any) => {  
              if(res.code == 0) {  
                setTimeout(() => {
                  Taro.hideLoading();
                  Taro.showToast({
                    title: '编辑成功',
                    icon: 'none',
                    duration: 2000
                  });
                }, 1000);
                // 更新本地数据
                setColors(prev => prev.map(f => 
                  f.id === id ? { ...f, name: currentValue } : f
                ));
                setEditValue('');
              }
            })          
          }
          
       } catch (error) {
         Taro.hideLoading();
         Taro.showToast({
           title: '编辑失败，请重试',
           icon: 'none',
           duration: 2000
         });
         console.error('编辑失败:', error);
       }
      },
    });

  }

  const deleteFormat=(id,type)=>{

    let title = '';
    title=type=='format' ? '规格' : '颜色'
    Dialog.confirm({
      platform: 'ios',
      className: 'dialog-input-demo',
      title: '温馨提示',
      contentAlign: 'left',
      children: (<>
          <div className="dialog-input-demo-hint">删除{title}后不可恢复，确认删除？</div>
      </>),
      onOk: async () => {
        Taro.showLoading({
          title: '删除中...',
          mask: true
        });
        try {
          if(type=='format'){
            delFormat({"id":id}).then((res: any) => {  
              if(res.code == 0) {  
              setTimeout(() => {
                Taro.hideLoading();
                Taro.showToast({
                  title: '删除成功',
                  icon: 'none',
                  duration: 2000
                });
              }, 1000);
                setFormats(formats.filter(f => f.id !== id));
              }
            })
          }else{
            delColor({"id":id}).then((res: any) => {  
              if(res.code == 0) {  
              setTimeout(() => {
                Taro.hideLoading();
                Taro.showToast({
                  title: '删除成功',
                  icon: 'none',
                  duration: 2000
                });
              }, 1000);
                setColors(colors.filter(f => f.id !== id));
              }
            })
          }
       } catch (error) {
         Taro.hideLoading();
         Taro.showToast({
           title: '删除失败，请重试',
           icon: 'none',
           duration: 2000
         });
         console.error('删除失败:', error);
       }
      },
      onCancel: () => {
        // 用户点击取消时执行
        console.log('用户取消了操作');
      },
    })  

  }
  // 搜索过滤

  const filteredFormats = formats.filter(f => f.name.includes(search));
  const filteredColors = colors.filter(c => c.name.includes(search));

  return (
    <View className="product-color-page"
      onClick={()=>{
      const hasTrue = Object.values(popoverFormat).some(Boolean);
          if (hasTrue) {
            // 把所有的 popoverFormat 都设为 false
            setPopoverFormat(
              Object.keys(popoverFormat).reduce((acc, key) => {
                acc[key] = false;
                return acc;
              }, {})
            );
          }    
    }} >
      <YkNavBar  title="设置规格" />
      <SearchBar shape="round" placeholder="搜索" clearable={false}   />
      <View className="section" >
        <View className="section-title">
          选择规格
          <Text className="section-tip">[长按] 支持修改和删除</Text>
        </View>
        <View className="tag-list">
          <View className="tag add-tag" onClick={addFormat}>＋添加</View>
          {filteredFormats.map(item => (
                <Popover
                    visible={popoverFormat[item.id] || false}
                    className="popover-with-margin"
                    content={
                      <View
                        className='popover'
                        onClick={()=>{
                          setPopoverFormat(prev => ({...prev, [item.id]: false}));
                        }}
                      >
                        <span className='content' >
                          <span onClick={()=>{editorFormat(item.id,'format')}}  style={{marginRight:"8px",cursor:"pointer"}}>编辑</span>  |  <span onClick={()=>{deleteFormat(item.id,'format')}} style={{marginLeft:"8px",cursor:"pointer"}}>删除</span>
                        </span>                      
                      </View>
                    }
                    direction="topLeft"
                >
                    <View
                      className={`tag${selectedFormats.some(f => f.id === item.id) ? ' selected' : ''}`}
                      key={item.id}
                      onClick={() => {
                        toggleFormat(item);
                        // setPopoverFormat(prev => {
                        //   const newState = Object.keys(prev).reduce((acc, key) => {
                        //     acc[key] = false;
                        //     return acc;
                        //   }, {} as {[key: string]: boolean});
                        //   newState[item.id] = true;
                        //   return newState;
                        // });
                      }}
                      onLongPress={()=>{
                        setPopoverFormat(prev => {
                          const newState = Object.keys(prev).reduce((acc, key) => {
                            acc[key] = false;
                            return acc;
                          }, {} as {[key: string]: boolean});
                          newState[item.id] = true;
                          return newState;
                        });
                      }} 
                    >
                      {item.name}
                    </View>
                </Popover>
          ))}
        </View>
      </View>

      <View className="section">
        <View className="section-title">选择颜色</View>
        <View className="tag-list">
          <View className="tag add-tag" onClick={addColor}>＋添加</View>
          {filteredColors.map(item => (
                <Popover
                    visible={popoverFormat[item.id] || false}
                    content={
                      <View
                        className='popover'
                        onClick={()=>{
                          setPopoverFormat(prev => ({...prev, [item.id]: false}));
                        }}
                      >
                        <span className='content' >
                          <span onClick={()=>{editorFormat(item.id,'color')}}  style={{marginRight:"8px",cursor:"pointer"}}>编辑</span>  |  <span onClick={()=>{deleteFormat(item.id,'color')}} style={{marginLeft:"8px",cursor:"pointer"}}>删除</span>
                        </span>
                        
                      </View>
                    }
                    direction="topCenter"       
                >
                    <View
                      className={`tag${selectedColors.some(c => c.id === item.id) ? ' selected' : ''}`}
                      key={item.id}
                      onClick={() => {
                        toggleColor(item);
                      }}
                      onLongPress={()=>{
                        setPopoverFormat(prev => {
                          const newState = Object.keys(prev).reduce((acc, key) => {
                            acc[key] = false;
                            return acc;
                          }, {} as {[key: string]: boolean});
                          newState[item.id] = true;
                          return newState;
                        });
                      }} 
                    >
                      {item.name}
                    </View>
                </Popover>
          ))}
        </View>
      </View>

      <Button className="confirm-btn" 
      onClick={()=>{
        Taro.setStorageSync('selectedFormats', selectedFormats);
        Taro.setStorageSync('selectedColors', selectedColors);
        Taro.navigateBack();
      }}>确定</Button>
    </View>
  );
}