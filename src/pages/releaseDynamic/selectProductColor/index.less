.product-color-page {
  min-height: 100vh;
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
  padding-bottom: 70px;
  .header {
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    padding: 16px 0 12px 0;
    background: #fff;
  }
  .search-input {
    width: 90%;
    margin: 0 auto 0 auto;
    display: block;
    border-radius: 8px;
    border: none;
    background: #f2f3f5;
    padding: 10px 16px;
    font-size: 15px;
  }
  .section {
    margin-top: 10px;
    background: #fff;
    padding: 0 16px 16px 16px;
    border-radius: 8px;
    .section-title {
      font-size: 15px;
      font-weight: 500;
      margin: 16px 0 12px 0;
      display: flex;
      align-items: center;
      .section-tip {
        font-size: 12px;
        color: #bcbcbc;
        margin-left: 10px;
      }
    }
    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      .tag {
        font-size: 14px;
        color: #222;
        background: #f2f3f5;
        border-radius: 8px;
        padding: 0 16px;
        height: 24px;
        line-height: 24px;
        margin-bottom: 8px;
        cursor: pointer;
        user-select: none;
        &.selected {
          background: #165dff;
          color: #fff;
        }
        &.add-tag {
          border: 1px dashed #165dff;
          color: #165dff;
          background: #fff;
        }
      }
    }
  }
  .confirm-btn {
    width: 90%;
    margin: 32px auto 0 auto;
    background: #165dff;
    color: #fff;
    font-size: 18px;
    border-radius: 8px;
    height: 44px;
    line-height: 44px;
    border: none;
    display: block;
  }
}


.popover{
  width: 102px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0px;
  z-index: 2;
}

.content{
  /* 自动布局子元素 */
    width: 78px;
    height: 20px;
    z-index: 0;
    /* 14/Medium */
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: normal;
    line-height: 140%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    /* 文字 Text/文字text-1 白色 */
    color: var(--text-1); /* #FFFFFF */
}




