import React, { useState, useEffect } from 'react';
import './inedx.less';
import { View } from '@tarojs/components';
import YkNavBar from '@/components/ykNavBar'
import {  getTreeCatalog } from "@/utils/api/common/common_user";
import { Tag } from "@arco-design/mobile-react";
import Taro,{ useLoad } from "@tarojs/taro";




export default function SelectTag() {
  // 只允许单选
  const [selectedTags, setSelectedTags] = useState<Array<{id: string, name: string}>>([]);
  
  const [catalog, setCatalog]= useState<any[]>([]); 


  //获取目录列表
  const getTagListData = async (type=1) => {
    const data = {
      userId: 299,
    }
    const tags: any = await getTreeCatalog(data);
    if (tags.code == 0) {
      if (tags.data.length > 0) {
        // 过滤掉 children 为空的项
        const filtered = tags.data.filter((item: any) => Array.isArray(item.children) && item.children.length > 0);
        setCatalog(filtered);
      } else {
        setCatalog([]);
      }
    }
  }
  useEffect(() => {
    getTagListData();
  }, []); // 添加 sortType 作为依赖



  return (
    <View className="select-tag-page">
      <YkNavBar title="标签" />
      <View className="tags-row">
        {selectedTags.map((item, index) => (
          <Tag
            size="small"
            key={item.id}
            filleted 
            type="solid" 
            closeable
            onClose={() => {
              setSelectedTags(prev => prev.filter((_, i) => i !== index));
            }}
            style={{fontSize:'12px',height:'25px'}} 
            bgColor={ "#165dff" }
          >
            {item.name}
          </Tag>
        ))}
      </View>
      <View className="add-tag">添加标签</View>
      <View className="list-content">List Content</View>
      <View className="select-title">选择标签</View>
      <View className="tag-group-list">
        {catalog.map((group, i) => (
          <View className="tag-group" key={group.id}>
            <View className="group-title">{group.name}</View>
            <View className="tags-row">
              {group.children.map(tag => {
                const isSelected = selectedTags.some(t => t.id === tag.id);
                return (
                  <View
                    key={tag.name}
                    onClick={() => {
                      setSelectedTags(prev =>
                        isSelected
                          ? prev.filter(t => t.id !== tag.id)
                          : [...prev, { id: tag.id, name: tag.name }]
                      );
                    }}
                  >
                    <Tag 
                      size="small"
                      filleted 
                      type="hollow" 
                      halfBorder={false}
                      style={{fontSize:'12px',height:'25px'}} 
                      bgColor="#fff"
                      borderColor={isSelected ? "#165dff" : "#86909C"}
                      color={isSelected ? "#165dff" : "#86909C"}
                    >
                      {tag.name}
                    </Tag>
                  </View>
                );
              })}
            </View>
          </View>
        ))}
      </View>
      <View className="footer-btns">
        <button className="btn btn-grey">目录/标签管理</button>
        <button className="btn btn-primary" onClick={()=>{
          Taro.setStorageSync('selectedArticles', selectedTags);
          Taro.navigateBack();
        }}>完成</button>
      </View>
    </View>
  );
}
