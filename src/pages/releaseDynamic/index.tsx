import { View, Text } from '@tarojs/components'
import { Button, Image, Input, Popup, Textarea, ImagePicker } from '@arco-design/mobile-react'
import './index.less'
import Taro,{useDidShow} from '@tarojs/taro'
import YkNavBar from '@/components/ykNavBar'
import { useState, useRef, useEffect } from 'react'
import { getFreightTemplateList, releaseAlbum, dynamicDetails, dynamicForward, uploadSingleFile, updateDynamic } from '@/utils/api/common/common_user'
import { userInfo } from 'os'

export default function ReleaseDynamic() {
  const [showPage, setShowPage] = useState(false)
  const [dynamicTitle, setDynamicTitle] = useState('')
  const [price, setPrice] = useState('')
  const [tag, setTag] = useState('通过标签给图文分类')
  const [formatColor, setFormatColor] = useState('')
  const [ems, setEms] = useState('无')
  const [shortText, setShortText] = useState('')
  const [imageList, setImageList] = useState<any[]>([])
  const [emsVisible, setEmsVisible] = useState(false)
  const [type, setType] = useState(1)
  const [checkTagList, setCheckTagList] = useState<any[]>([])
  const [checkFormatList, setCheckFormatList] = useState<any[]>([])
  const [checkColorList, setCheckColorList] = useState<any[]>([])
  const [sourceName, setSourceName] = useState('')
  const [sourceId, setSourceId] = useState('')
  const [dynamicId, setDynamicId] = useState('')
  const [isShowFooter, setIsShowFooter] = useState(true)
  const [paddingBottom, setPaddingBottom] = useState('0rpx')
  const [tags, setTags] = useState<any[]>([])
  const [formats, setFormats] = useState<any[]>([])
  const [colors, setColors] = useState<any[]>([])
  

  useDidShow(() => {
    const selectedArticles = Taro.getStorageSync('selectedArticles')
    const selectedFormats = Taro.getStorageSync('selectedFormats')
    const selectedColors = Taro.getStorageSync('selectedColors')
    if (selectedArticles && selectedArticles.length > 0) {
      setTags(selectedArticles)
    }
    if (selectedFormats && selectedFormats.length > 0) {
      setFormats(selectedFormats)
    }
    if (selectedColors && selectedColors.length > 0) {
      setColors(selectedColors)
    }
  });

  useEffect(() => {
    // 初始化页面
    // setTimeout(() => {
    //   setShowPage(true)
    // }, 200)
    // 获取页面参数
    Taro.setStorageSync('selectedArticles',[])
    Taro.setStorageSync('selectedFormats',[])
    Taro.setStorageSync('selectedColors',[])
    setTags([])
    setFormats([])
    setColors([])
    const params = Taro.getCurrentInstance().router?.params
    console.log('params', params)

    if (params?.type) {
      setType(Number(params.type))
      if (params.type === '2' || params.type === '4') {
        const releaseDynamicList = Taro.getStorageSync('releaseDynamicList')
        dynamicDetails({ id: releaseDynamicList.id }).then((res: any) => {
          if (res.code === 0) {
            setDynamicTitle(res.data.content)
            setDynamicId(res.data.id)
            setSourceId(res.data.userId)
            setSourceName(res.data.nickname)
            setPrice(res.data.price || '')
            // setEmsTemplateId(res.data.freightTemplateId)
            setTag(res.data.labelAndCatalogueNames	 || '通过标签给图文分类')
            setFormatColor( [res.data.productSpecificationsNames,res.data.productColorNames].join(',').replace(/^,|,$/g, '') || '无'	)
            // setFormat(res.data.size + ',' + res.data.color)
            setImageList(res.data.pictures.split(',').map(item => ({ url: item })))
          }
        })
      }
    }

    // 获取运费模板列表
    // getFreightTemplateList().then((res: any) => {
    //   if (res.code === 0) {
    //     setEmsList(res.data)
    //     if (!emsTemplateId) {
    //       const defaultTemplate = res.data.find(item => item.is_default === '1')
    //       if (defaultTemplate) {
    //         setEmsTemplateId(defaultTemplate.id)
    //       }
    //     }
    //   }
    // })
  }, [])

//   const recordPositionalParameter = (list, type) => {
//     for (let i = 0; i < list.length; i++) {
//       const x = (165 + (i % 4 === 0 ? 0 : 10)) * (i % 4)
//       const y = (165 + (i < 4 ? 0 : 10)) * Math.floor(i / 4)
//       list[i].xy = [Taro.pxTransform(x), Taro.pxTransform(y)]
//     }
//     setImageList(list)
//     if (type === 1) {
//       setTimeout(() => {
//         getdownloadFile()
//       }, 800)
//     }
//   }

//   const getdownloadFile = () => {
//     const list = [...imageList]
//     for (let i = 0; i < list.length; i++) {
//       Taro.downloadFile({
//         url: list[i].path,
//         success: (res) => {
//           if (res.statusCode === 200) {
//             list[i].path = res.tempFilePath
//             setImageList(list)
//           }
//         }
//       })
//     }
//   }

  const handlePriceInput = (e,value) => {
    // 移除非数字字符和多余的小数点
    let sanitizedValue = value.replace(/[^\d.]/g, '').replace(/^(\d*\.\d{2}).*$/, '$1')
    
    // 将输入值限制在 6 位整数，两位小数点
    let [integerPart, decimalPart] = sanitizedValue.split('.')
    if (integerPart && integerPart.length > 6) {
      integerPart = integerPart.slice(0, 6)
    }
    if (decimalPart && decimalPart.length > 2) {
      decimalPart = decimalPart.slice(0, 2)
    }
    
    setPrice(decimalPart ? `${integerPart}.${decimalPart}` : integerPart)
  }

  const handleSave = () => {
    if (dynamicTitle.length === 0 && imageList.length === 0) {
      Taro.showToast({
        title: "图片跟描述至少填写一个",
        icon: "none",
      });
      return
    }

    if (price !== '' && Number(price) > 999999.99) {
      Taro.showToast({
        title: "最多输入六位整数（包括两位小数）",
        icon: "none",
      });
      return
    }

    const formData: any = {
      content: dynamicTitle,
      price: price === '' ? '0' : price,
    //   freight_template_id: emsTemplateId,
    //   goods_short_name: shortText || dynamicTitle
    }

    if(tags.length > 0) {
      formData.labelAndCatalogueIds = tags.map(item => item.id).join(',')
    }
    if(formats.length > 0) {
      formData.productSpecificationsIds = formats.map(item => item.id).join(',')
    }
    if(colors.length > 0) {
      formData.productColorIds = colors.map(item => item.id).join(',')
    }

    if (type === 2 || type === 4) {
      formData.id = dynamicId
    }
    const userInfo = Taro.getStorageSync('userInfo')

    formData.userId = userInfo.id
    if (type === 4) {
      formData.userId = sourceId
    }

    if (checkFormatList.length > 0) {
      formData.size = checkFormatList.map(item => item.name).join(',')
    }

    if (checkColorList.length > 0) {
      formData.color = checkColorList.map(item => item.name).join(',')
    }

    if (checkTagList.length > 0) {
      const tagIdArray = checkTagList.filter(item => item.tId !== '-1').map(item => item.tId)
      if (tagIdArray.length > 0) {
        formData.label_ids = tagIdArray.join(',')
      }

      const tagNameArray = checkTagList.filter(item => item.tId === '-1').map(item => item.name)
      if (tagNameArray.length > 0) {
        formData.label_names = tagNameArray.join(',')
      }
    }
    if(imageList.length>0){
      let pictures=''
      imageList.map(item => {
             const  url = item.data ?  item.data : item.url 
             pictures=pictures+url+',';
      })
      formData.pictures = pictures.replace(/^,+|,+$/g, '');
    }else{
      formData.pictures=''
    }
    
    
    // loadingRef.current?.show()
    
    if (type === 4) {
    //   dynamicForward(formData, files).then((res) => {
    //     // loadingRef.current?.hide()
    //     if (res.code === 0) {
    //       Taro.showToast({
    //         title: "转发成功",
    //         icon: "none",
    //       });
    //       setTimeout(() => {
    //         Taro.navigateBack({
    //           delta: 1
    //         })
    //       }, 1500)
    //     } else {
    //       Taro.showToast({
    //         title: res.message,
    //         icon: "none",
    //       });
    //     }
    //   })
    } else if (type == 2) {
      updateDynamic(formData).then((res: any) => {
        // loadingRef.current?.hide()
        if (res.code === 0) {
          Taro.showToast({
            title: "编辑成功",
            icon: "none",
          });
          if (type === 2 || type === 3) {
            Taro.removeStorageSync('releaseDynamicList')
            setTimeout(() => {
              Taro.navigateBack({
                delta: 1
              })
            }, 1500)
          } else {
            setTimeout(() => {
              Taro.switchTab({
                url: '/pages/album/index'
              })
            }, 1500)
          }
        } else {
            Taro.showToast({
                title: res.message,
                icon: "none",
              });
        }
      })

    } else {
      releaseAlbum(formData).then((res: any) => {
        // loadingRef.current?.hide()
        if (res.code === 0) {
          Taro.showToast({
            title: "发布成功",
            icon: "none",
          });
          if (type === 2 || type === 3) {
            Taro.removeStorageSync('releaseDynamicList')
            setTimeout(() => {
              Taro.navigateBack({
                delta: 1
              })
            }, 1500)
          } else {
            setTimeout(() => {
              Taro.switchTab({
                url: '/pages/album/index'
              })
            }, 1500)
          }
        } else {
            Taro.showToast({
                title: res.message,
                icon: "none",
              });
        }
      })
    }
  }

  const handleChooseImage = () => {
    const count = 9 - imageList.length
    Taro.chooseImage({
      count,
      sizeType: ['original', 'compressed'],
      sourceType: ['album'],
      success: (res) => {
        setImageList([...imageList, ...res.tempFiles])
        // recordPositionalParameter([...imageList, ...res.tempFiles])
      }
    })
  }

  const handleDeleteImage = (index) => {
    const newList = [...imageList]
    newList.splice(index, 1)
    setImageList(newList)
    // recordPositionalParameter(newList)
  }

  const handleNavigateBack = () => {
    Taro.removeStorageSync('releaseDynamicList')
    // popupRef.current?.show()
  }

  const selectTag = () => {
    Taro.navigateTo({
      url: '/pages/releaseDynamic/selectTag/index'
    })
  }

  const selectProductColor = () => {
    Taro.navigateTo({
      url: '/pages/releaseDynamic/selectProductColor/index'
    })
  }

  const imgUpload = async (file) => {
    const res:any = await uploadSingleFile(file.file)
    if(res.code === 0) {
      file.data = res.data
      return res.data
    } else {
      return null
    }
  }
  

  return (
    <View className='release-dynamic'>
      <YkNavBar title={type === 2 ? '编辑动态' : type === 4 ? '添加图文' : '发布动态'} />
      
      <View className='container'>
        <Textarea
          className='textarea'
          value={dynamicTitle}
          onChange={(_,value) => setDynamicTitle(value)}
          placeholder='发布我的新宝贝...'
          maxLength={3000}
        />

        <View className='image-tips'>
          <Text>长按图片后，拖动图片可调整顺序</Text>
        </View>

        <View className='imagelist'>
          <ImagePicker 
            images={imageList} 
            onChange={setImageList} 
            upload={imgUpload}
            // upload={uploadSingleFile}
            limit={9}
          />
        </View>

        <View className='price-section'>
          <Text className='label'>售价</Text>
          <View className='input-wrapper'>
            {price !== '' && <Text className='currency'>￥</Text>}
            <Input
              className='price-input'
              value={price}
              onChange={handlePriceInput}
              type='number'
              placeholder='请输入售价'
              border="none"
            />
          </View>
        </View>

        <View className='divider' />

        <View className='tag-section' onClick={() => {selectTag()}}>
          <Text className='label'>标签</Text>
          <View className='right'>
            <Text className={tag === '' || tag === '通过标签给图文分类' ? 'placeholder' : 'value'}>
              {
                tags.length > 0 ? (
                  tags.map(item => item.name).join(', ')
                ) : tag
              }
            </Text>
            {/* <Image className='arrow' src={require('@/static/image/common/right_arrow.png')} /> */}
          </View>
        </View>

        <View className='divider' />

        <View className='format-section' onClick={() => {selectProductColor()}}>
          <Text className='label'>规格</Text>
          <View className='right'>
            <Text className={formatColor === '' || formatColor === '无' ? 'placeholder' : 'value'}>
            {
                formats.length > 0 ? (
                  [...formats, ...colors].map(item => item.name).join(', ')
                ) : formatColor
              }
      
            </Text>
            {/* <Image className='arrow' src={require('@/static/image/common/right_arrow.png')} /> */}
          </View>
        </View>

        <View className='divider' />

        <View className='short-text-section'>
          <Text className='label'>商品简称</Text>
          <View className='right'>
            <Input
            border="none"
              className='input'
              value={shortText}
              onChange={(_,value) => setShortText(value)}
              placeholder='请输入简称'
              maxLength={16}
            />
            {dynamicTitle !== '' && (
              <Text className='get-from-title' onClick={() => {/* 从标题获取 */}}>
                从标题获取
              </Text>
            )}
          </View>
        </View>

        <View className='divider' />

        <View className='source-section'>
          <Text className='label'>来源(仅自己可见)</Text>
          <Text className='value'>{type === 4 ? sourceName : '我的相册'}</Text>
        </View>

        <View className='divider' />

        <View className='ems-section' onClick={() => setEmsVisible(true)}>
          <Text className='label'>运费模版</Text>
          <View className='right'>
            <Text className={ems === '' || ems === '无' ? 'placeholder' : 'value'}>
              {ems}
            </Text>
            {/* <Image className='arrow' src={require('@/static/image/common/right_arrow.png')} /> */}
          </View>
        </View>
      </View>

      {isShowFooter && (
        <View className='footer' style={{ paddingBottom }}>
          <Button
            className={`save-button ${dynamicTitle.length > 0 || imageList.length > 0 ? 'active' : ''}`}
            onClick={handleSave}
          >
            {type === 4 ? '转发至相册' : '保存'}
          </Button>
        </View>
      )}

      {/* <Popup
        visible={emsVisible}
        onClose={() => setEmsVisible(false)}
        direction='bottom'
        contentStyle={{ borderRadius: '10px 10px 0 0' }}
      >
        <View className='ems-popup'>
          <View className='title'>运费模板</View>
          <View className='content'>
            {emsList.map((item) => (
              <View
                key={item.id}
                className='item'
                onClick={() => {
                  setEms(item.template_name)
                  setEmsTemplateId(item.id)
                  setEmsVisible(false)
                }}
              >
                <View className='left'>
                  <Text className={item.id === emsTemplateId ? 'name active' : 'name'}>
                    {item.template_name}
                  </Text>
                  <Text className={item.id === emsTemplateId ? 'desc active' : 'desc'}>
                    {item.type === '包邮' ? '默认规格包邮' : `默认规格固定运费${item.price}元`}
                  </Text>
                </View>
                {item.id === emsTemplateId && (
                  <Image
                    className='check'
                    src={require('@/static/image/common/check_ems_icon.png')}
                  />
                )}
              </View>
            ))}
          </View>
          <Button className='cancel' onClick={() => setEmsVisible(false)}>
            取消
          </Button>
        </View>
      </Popup> */}

      {/* <Toast ref={toastRef} /> */}
      {/* <Loading ref={loadingRef} /> */}
      {/* <Popup
        ref={popupRef}
        title='放弃发布'
        content='确定要放弃发布动态吗？'
        confirmText='放弃'
        cancelText='继续编辑'
        onConfirm={() => {
          Taro.navigateBack({
            delta: 1
          })
        }}
      /> */}
    </View>
  )
} 