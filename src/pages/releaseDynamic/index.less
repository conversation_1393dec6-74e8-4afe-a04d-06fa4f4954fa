@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pages/releaseDynamic/index"] {
  background-color: #f8f9fa !important;
  .release-dynamic {
    min-height: 100vh;
    background-color: #f8f9fa;
    padding-bottom: 50px;
  
    .container {
      padding: 10px 0;
    }
  
    .textarea {
      width: 100%;
      .rem(height, 150);
      padding: 10px 15px;
      background-color: #ffffff;
      .rem(font-size, 12);
    }
  
    .image-tips {
      // margin-top: 20px;
      padding: 0 15px;
      padding-top: 20px;
      .rem(height, 20);
      line-height: 20px;
      .rem(font-size, 12);
      color: #999999;
      background-color: #ffffff;
    }
  
    .image-list {
      padding: 10px 15px;
      background-color: #ffffff;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 10px;
  
      .image-item {
        position: relative;
        .rem(width, 82);
        .rem(height, 82);
        border-radius: 8px;
        background-color: #f6f6f6;
  
        &.add {
          display: flex;
          align-items: center;
          justify-content: center;
        }
  
        .delete-icon {
          position: absolute;
          top: 0;
          right: 0;
          .rem(width, 30);
          .rem(height, 30);
        }
      }
    }
  
    .price-section,
    .tag-section,
    .format-section,
    .short-text-section,
    .source-section,
    .ems-section {
      .rem(height, 50);
      background-color: #ffffff;
      padding: 0 9px;
      display: flex;
      align-items: center;
      justify-content: space-between;
  
      .label {
        .rem(width, 120);
        .rem(font-size, 15);
        color: #000000;
      }
  
      .input-wrapper {
        flex: 1;
        margin-left: 9px;
        display: flex;
        align-items: center;
  
        .currency {
          .rem(font-size, 11);
          color: #000000;
        }
  
        .price-input {
          flex: 1;
          // .rem(height, 21);
          color: #000000;
          .rem(font-size, 14);
          align-items: center;
          height: 100%;
        }
      }
  
      .right {
        flex: 1;
        margin-left: 9px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
  
        .placeholder {
          color: rgba(153, 153, 153, 0.6);
          .rem(font-size, 14);
        }
  
        .value {
          color: #000000;
          .rem(font-size, 14);
        }
  
        .arrow {
          margin-left: 16px;
          .rem(width, 12);
          .rem(height, 22);
        }

        .input {
          .rem(font-size, 14);
          height: 100%;
          display: flex;
          align-items: center;
        }
      }
    }
  
    .short-text-section {
      .right {
        .input {
          flex: 1;
          .rem(height, 21);
          color: #000000;
          .rem(font-size, 14);
        }
  
        .get-from-title {
          margin-left: 18px;
          color: #355a95;
          .rem(font-size, 14);
        }
      }
    }
  
    .divider {
      margin: 0 18px;
      border-bottom: 1px solid rgba(153, 153, 153, 0.2);
    }

    .value {
      .rem(font-size, 14);
    }
  
    .footer {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      .rem(height, 50);
      background-color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
  
      .save-button {
        .rem(width, 345);
        .rem(height, 36);
        border-radius: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        .rem(font-size, 13);
        font-weight: bold;
        background-color: rgba(108, 190, 112, 0.3);
  
        &.active {
          background-color: rgba(108, 190, 112, 1);
        }
      }
    }
  
    .ems-popup {
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
      border-radius: 14px 14px 0 0;
      background-color: #ffffff;
  
      .title {
        padding: 15px;
        text-align: center;
        .rem(font-size, 15);
        color: #333333;
      }
  
      .content {
        max-height: 66vh;
        overflow-y: auto;
  
        .item {
          padding: 15px 25px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid rgba(248, 248, 248, 0.8);
  
          .left {
            .name {
              .rem(font-size, 15);
              color: #333333;
  
              &.active {
                color: #55ba38;
              }
            }
  
            .desc {
              margin-top: 8px;
              .rem(font-size, 11);
              color: #999999;
  
              &.active {
                color: #55ba38;
              }
            }
          }
  
          .check {
            .rem(width, 30);
            .rem(height, 20);
          }
        }
      }
  
      .cancel {
        width: 100%;
        .rem(height, 50);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333333;
        .rem(font-size, 15);
      }
    }
  } 
}
