@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pages/albumManage/index"] {
	// background-color: red;

// .batch-on-off-shelf {
// 	height: 100vh;
// 	overflow-y: auto;
//   }
  
//   .footer_content_z {
// 	position: relative;
// 	width: 100%;
// 	height: 128rpx;
//   }
  
//   .topBg {
// 	background-color: #f3f4f6;
//   }
  
//   .hiddenStyle {
// 	display: none;
//   }
  
//   .content-swiper {
// 	width: 100%;
//   }
  
//   .boxContent {
// 	position: relative;
// 	width: 100%;
// 	height: calc(100vh - 168rpx - 88rpx);
// 	overflow-y: auto;
//   }
  
//   .notmorelist {
// 	position: relative;
// 	width: 100%;
// 	height: 80rpx;
// 	display: flex;
// 	align-items: center;
// 	justify-content: center;
// 	color: #999999;
// 	font-size: 24rpx;
//   }
  
//   .not_content {
// 	position: relative;
// 	width: 100%;
// 	height: 50vh;
// 	display: flex;
// 	align-items: center;
// 	justify-content: center;
// 	flex-direction: column;
// 	font-size: 30rpx;
// 	font-weight: normal;
// 	color: #999999;
  
// 	&-image {
// 	  width: 200rpx;
// 	  height: 200rpx;
// 	  display: block;
// 	  margin-bottom: 30rpx;
// 	}
//   }
  
  .dline {
	position: relative;
	width: 100%;
	padding: 15px 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid #f1f1f1;
	// background-color: red;
  
	&-left {
	  margin-left: 15px;
	  display: flex;
	  align-items: center;
	  justify-content: center;
  
	  &-change {
		width: 18px;
		height: 18px;
		display: block;
	  }
	}
  
	&-right {
	// background: red;
	// width: 100%;
	//   width: calc(100% - 58px);
	  margin-right: 15px;
	  margin-left: 10px;
	  flex: 1;
	  height: 100%;
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  flex-direction: column;
  
	  &-top {
		// width: 307px;
		padding: 5px;
		font-size: 13px;
		color: #333333;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
		display: inline-block;
	  }
  
	  &-bottom {
		.rem(margin-top, 5);
		width: 100%;
		.rem(height, 55);
		display: grid;
		grid-template-columns: repeat(auto-fill, 55px);
		grid-gap: 0rpx 5px;
  
		.imageItem {
		  position: relative;
		//   width: 55px;
		  .rem(width, 55);
		  .rem(height, 55);
  
		  &-image {
			.rem(width, 55);
			.rem(height, 55);
			display: block;
		  }
  
		  &-mask {
			position: absolute;
			top: 0;
			left: 0;
			.rem(width, 55);
			.rem(height, 55);
			background-color: rgba(0, 0, 0, 0.4);
			.rem(font-size, 15);
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
		  }
		}
	  }
	}
  }
  
  .footerBtnBox {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 345px;
	height: 50px;
	padding: 0 15px;
	background-color: #ffffff;
	z-index: 3090;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-top: 1px solid #f7f7f7;
  
	&-change {
	  flex: 1;
	  display: flex;
	  align-items: center;
  
	  &-image {
		width: 20px;
		height: 20px;
		display: block;
		margin-right: 10px;
	  }
  
	  &-c {
		display: flex;
		align-items: center;
		justify-content: center;
		&-img {
		  width: 8px;
		  height: 12px;
		  display: block;
		  margin-left: 10px;
		}
  
		&-text {
		  font-size: 13px;
		  color: #333333;
		}
	  }
	}
  
	&-btn {
	  width: 130px;
	  height: 36px;
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  border-radius: 6px;
	  background-color: #6cbe70;
	  color: #ffffff;
	  font-size: 13px;
	}
  
	&-notbtn {
	  width: 130px;
	  height: 36px;
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  border-radius: 6px;
	  background-color: rgba(108, 190, 112, 0.3);
	  color: #ffffff;
	  font-size: 13px;
	}
  }
  
  .popup {
	position: relative;
	width: 100%;
  
	&-bottomBox {
	  width: 220rpx;
	  overflow: hidden;
	  background-color: #ffffff;
	  border-radius: 12rpx;
	  margin: 0 30rpx;
	  margin-bottom: 114rpx;
  
	  &-line {
		text-align: center;
		padding: 0 30rpx;
		width: calc(100% - 60rpx);
		height: 76rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 26rpx;
		color: #333333;
	  }
  
	  &-divider {
		margin: 0 30rpx;
		border-bottom: 1rpx solid rgba(153, 153, 153, 0.2);
	  }
	}
  }
  
  .num-input {
	width: 100%;
	height: 40px;
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 0 10px;
	box-sizing: border-box;
  }
}