import React, { useState, useEffect, useRef } from 'react';
import { 
  NavBar, 
  Tabs, 
  Loading, 
  Popup,
  Dialog,
  Checkbox,
  Image,
  Cell,
  Button 
} from '@arco-design/mobile-react';
import { getShopHomeDynamicList, dynamicBatchOperate } from '@/utils/api/common/common_user';
import './index.less';
import YkNavBar from '@/components/ykNavBar';

import Taro from '@tarojs/taro';

interface DynamicItem {
  id: string;
  pictures?: string;
  content?: string;
}

interface PageInfo {
  page: number;
  limit: number;
  isLoad: boolean;
  pageTurn: boolean;
}

interface TabItem {
  type: number;
  name: string;
  pages: PageInfo;
}

const BatchOnOffShelf: React.FC = () => {
  const [showPage, setShowPage] = useState(false);
  const [tabList] = useState<TabItem[]>([
    {
      type: 1,
      name: '已上架',
      pages: {
        page: 1,
        limit: 20,
        isLoad: false,
        pageTurn: true
      }
    },
    {
      type: 2,
      name: '已下架',
      pages: {
        page: 1,
        limit: 20,
        isLoad: false,
        pageTurn: true
      }
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [current, setCurrent] = useState(0);
  const [delLoading, setDelLoading] = useState(false);
  const [dynamicList, setDynamicList] = useState<DynamicItem[]>([]);
  const [delIdlist, setDelIdlist] = useState<string[]>([]);
  const [allchange, setAllchange] = useState(false);
  const [dynamicList2, setDynamicList2] = useState<DynamicItem[]>([]);
  const [delIdlist2, setDelIdlist2] = useState<string[]>([]);
  const [allchange2, setAllchange2] = useState(false);
  const [isMaskClick, setIsMaskClick] = useState(false);
  const [paddingBottom, setPaddingBottom] = useState('0rpx');
  const [popMarginBottom, setPopMarginBottom] = useState('100rpx');
  const [popupVisible, setPopupVisible] = useState(false);
  const [onOffPopupVisible, setOnOffPopupVisible] = useState(false);
  const [numPopupVisible, setNumPopupVisible] = useState(false);
  const [customNum, setCustomNum] = useState('');
  
  const lookImageListRef = useRef<any>(null);

  const loginUserInfo = Taro.getStorageSync('userInfo')
  useEffect(() => {
    // 获取用户信息

    // 检测平台类型设置底部边距
    const userAgent = navigator.userAgent;
    const isIos = userAgent.indexOf(`ios_`) > -1;
    const isAndroid = userAgent.indexOf(`android_`) > -1;
    
    if (isIos) {
      setPaddingBottom('68rpx');
      setPopMarginBottom('168rpx');
    } else if (isAndroid) {
      setPaddingBottom('0rpx');
      setPopMarginBottom('100rpx');
    }

    getDynamicList();
  }, []);

  const getDynamicList = (tabIndex?: number) => {
    let pages, currDynamicList;
    const currentIndex = tabIndex !== undefined ? tabIndex : current;

    pages = tabList[currentIndex].pages;
    currDynamicList = currentIndex === 0 ? dynamicList : dynamicList2;
    
    let data = {
      pageNo: pages.page,
      pageSize: pages.limit,
      userId: loginUserInfo.userId,
      isListed: tabList[currentIndex].type,
    };
    
    // if (isLoading || !pages.pageTurn) return;
    
    setIsLoading(true);
    getShopHomeDynamicList(data).then((res: any) => {
      setIsLoading(false);
      if (res.code === 0) {
        tabList[currentIndex].pages.isLoad = true;
        let newList;
        console.log(res.data)
        if (pages.page === 1) {
          newList = res.data.list;
        } else {
          if (res.data.list.length > 0) {
            newList = [...currDynamicList, ...res.data.list];
          } else {
            pages.page = pages.page - 1;
            newList = currDynamicList;
          }
        }
        
        if (res.data.list.length < pages.limit) {
          pages.pageTurn = false;
        } else {
          pages.pageTurn = true;
          pages.page = pages.page + 1;
        }
        
        if (currentIndex === 0) {
          setDynamicList(newList);
        } else {
          setDynamicList2(newList);
        }
        
        isNewAllChange(currentIndex);
        setTimeout(() => {
          setShowPage(true);
        }, 500);
      } else {
        Taro.showToast({
          title: res.message,
          icon: 'none'
        })
      }
    });
  };

  const handleTabChange = (tab: any, index: number) => {
    setCurrent(index);
    let isLoad = true;
    
    if (index === 0) {
      if (!tabList[index].pages.isLoad) {
        tabList[index].pages = {
          page: 1,
          limit: 20,
          isLoad: false,
          pageTurn: true
        };
        setDelIdlist([]);
        isLoad = false;
      }
    } else {
      if (!tabList[index].pages.isLoad) {
        tabList[index].pages = {
          page: 1,
          limit: 20,
          isLoad: false,
          pageTurn: true
        };
        setDelIdlist2([]);
        isLoad = false;
      }
    }
    
    if (!isLoad) {
      getDynamicList(index);
    }
  };

  const openLookImageList = (item: DynamicItem, index: number) => {
    if (lookImageListRef.current) {
      lookImageListRef.current.open(item, index, false, true);
    }
  };

  const onReachBottom = () => {
    getDynamicList();
  };

  const onRefresh = () => {
    tabList[current].pages = {
      page: 1,
      limit: 20,
      isLoad: false,
      pageTurn: true
    };

    if (current === 0) {
      setDelIdlist([]);
    } else {
      setDelIdlist2([]);
    }

    getDynamicList();
  };

  const refreshAll = () => {
    tabList[0].pages = {
      page: 1,
      limit: 20,
      isLoad: false,
      pageTurn: true
    };

    tabList[1].pages = {
      page: 1,
      limit: 20,
      isLoad: false,
      pageTurn: true
    };

    setDelIdlist([]);
    setDelIdlist2([]);

    getDynamicList();
  };

  const getRightsamllImage = (item: DynamicItem) => {
    let list: string[] = [];
    if (item.pictures) {
      list = item.pictures.split(',');
    }
    return list;
  };

  const getDelIdlist = (item: DynamicItem) => {
    const currentIdList = current === 0 ? [...delIdlist] : [...delIdlist2];
    const currentDynamicList = current === 0 ? dynamicList : dynamicList2;
    
    const index = currentIdList.indexOf(item.id);
    if (index === -1) {
      currentIdList.push(item.id);
    } else {
      currentIdList.splice(index, 1);
    }
    
    if (current === 0) {
      setDelIdlist(currentIdList);
      setAllchange(currentIdList.length === currentDynamicList.length);
    } else {
      setDelIdlist2(currentIdList);
      setAllchange2(currentIdList.length === currentDynamicList.length);
    }
  };

  const selectNum = (num: number) => {
    const currentDynamicList = current === 0 ? dynamicList : dynamicList2;
    let newIds: string[] = [];
    
    const selectCount = Math.min(num, currentDynamicList.length);
    for (let i = 0; i < selectCount; i++) {
      newIds.push(currentDynamicList[i].id);
    }
    
    if (current === 0) {
      setDelIdlist(newIds);
      setAllchange(newIds.length === currentDynamicList.length);
    } else {
      setDelIdlist2(newIds);
      setAllchange2(newIds.length === currentDynamicList.length);
    }
  };

  const getAllChange = () => {
    setPopupVisible(false);
    
    const currentIdList = current === 0 ? [...delIdlist] : [...delIdlist2];
    const currentDynamicList = current === 0 ? dynamicList : dynamicList2;
    
    if (currentIdList.length < currentDynamicList.length) {
      const newList: string[] = [];
      currentDynamicList.forEach(item => {
        newList.push(item.id);
      });
      
      if (current === 0) {
        setDelIdlist(newList);
        setAllchange(true);
      } else {
        setDelIdlist2(newList);
        setAllchange2(true);
      }
    } else {
      if (current === 0) {
        setDelIdlist([]);
        setAllchange(false);
      } else {
        setDelIdlist2([]);
        setAllchange2(false);
      }
    }
  };

  const isNewAllChange = (tabIndex?: number) => {
    const currentIndex = tabIndex !== undefined ? tabIndex : current;
    const currentIdList = currentIndex === 0 ? delIdlist : delIdlist2;
    const currentDynamicList = currentIndex === 0 ? dynamicList : dynamicList2;
    
    if (currentIdList.length === 0) {
      if (currentIndex === 0) {
        setAllchange(false);
      } else {
        setAllchange2(false);
      }
      return;
    }
    
    const allSelected = currentDynamicList.every(item => 
      currentIdList.includes(item.id)
    );
    
    if (currentIndex === 0) {
      setAllchange(allSelected);
    } else {
      setAllchange2(allSelected);
    }
  };


  const handleBatchOperation = () => {
    if (delLoading) return;
    
    const currentIdList = current === 0 ? delIdlist : delIdlist2;
    const ids = currentIdList.join(',');
    const data = {
      status: current === 0 ? '0' : '1',
      ids
    };
    
    setDelLoading(true);
    setOnOffPopupVisible(false);
    
    dynamicBatchOperate(data).then((res: any) => {
      setTimeout(() => {
        setDelLoading(false);
        if (res.code === 0) {
          Taro.showToast({
            title: current === 0 ? '下架成功' : '上架成功',
            icon: 'none'
          })
          refreshAll();
        } else {
          Taro.showToast({
            title: res.message,
            icon: 'none'
          })
        }
      }, 300);
    });
  };

  const handleNumConfirm = () => {
    const num = parseInt(customNum);
    if (!isNaN(num) && num > 0) {
      selectNum(num);
    }
    setNumPopupVisible(false);
  };

  const renderDynamicItem = (item: DynamicItem, itemIndex: number, isList1: boolean) => {
    const currentIdList = isList1 ? delIdlist : delIdlist2;
    const images = getRightsamllImage(item);
    
    return (
      <Cell key={item.id} className="dline">
        <div className="dline-left" onClick={() => getDelIdlist(item)}>
          <Checkbox
          value={1}
            checked={currentIdList.includes(item.id)}
            className="dline-left-change"
          />
        </div>
        <div className="dline-right">
          <div className={`dline-right-top ${item.content ? '' : 'topBg'}`}>
            {item.content}
          </div>
          {item.pictures && (
            <div className="dline-right-bottom">
              {images.slice(0, 5).map((imgUrl, imgIndex) => (
                <div 
                  key={imgIndex} 
                  className="imageItem" 
                  onClick={() => openLookImageList(item, imgIndex)}
                >
                  <Image
                    src={(imgUrl)}
                    fit="cover"
                    className="imageItem-image"
                  />
                  {imgIndex === 4 && images.length > 5 && (
                    <div className="imageItem-mask">
                      <span>+{images.length - 4}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </Cell>
    );
  };

  const renderEmptyContent = () => (
    <div className="not_content">
      <img 
        className="not_content-image" 
        src="@/static/image/common/not_content_trend.png" 
        alt="暂无内容"
      />
      <span>暂无动态内容</span>
    </div>
  );

  return (
    <div >
      
      {/* {!showPage && <Loading type="spin" />} */}
      
      <YkNavBar
        className={'navTop'}
        // onBack={getNavigateBack}
        title="批量上下架"
      />
      
      {showPage && (
        <>
          <Tabs
            // activeTab={current}
            // onChange={handleTabChange}
            onAfterChange={handleTabChange}
            tabs={tabList.map(item => ({ title: item.name }))}
            className="content-tabs"
            // style={{ height: 'calc(100vh - 168rpx - 88rpx)' }}
          >
            <div className="boxContent">
              {dynamicList.length === 0 && !tabList[0].pages.pageTurn ? (
                renderEmptyContent()
              ) : (
                <>
                  {dynamicList.map((item, index) => renderDynamicItem(item, index, true))}
                  
                  {dynamicList.length > 0 && isLoading && tabList[0].pages.pageTurn && (
                    <div className="notmorelist">
                      <span>正在加载...</span>
                    </div>
                  )}
                  
                  {/* {dynamicList.length > 0 && !tabList[0].pages.pageTurn && (
                    <div className="notmorelist">
                      <span>到底了~</span>
                    </div>
                  )} */}
                </>
              )}
            </div>
            
            <div className="boxContent">
              {dynamicList2.length === 0 && !tabList[1].pages.pageTurn ? (
                renderEmptyContent()
              ) : (
                <>
                  {dynamicList2.map((item, index) => renderDynamicItem(item, index, false))}
                  
                  {dynamicList2.length > 0 && isLoading && tabList[1].pages.pageTurn && (
                    <div className="notmorelist">
                      <span>正在加载...</span>
                    </div>
                  )}
                  
                  {/* {dynamicList2.length > 0 && !tabList[1].pages.pageTurn && (
                    <div className="notmorelist">
                      <span>到底了~</span>
                    </div>
                  )} */}
                </>
              )}
            </div>
          </Tabs>
          
          <div className="footer_content_z"></div>
          
          <div className="footerBtnBox" style={{ paddingBottom }}>
            <div className="footerBtnBox-change">
              <Checkbox
              value={1}
                checked={current === 0 ? allchange : allchange2}
                onChange={getAllChange}
                className="footerBtnBox-change-image"
              />
              <div className="footerBtnBox-change-c" onClick={() => setPopupVisible(true)}>
                <span className="footerBtnBox-change-c-text">
                  选中{current === 0 ? delIdlist.length : delIdlist2.length}条
                </span>
                <img 
                  src="../../../static/image/common/check_all_icon.png" 
                  className="footerBtnBox-change-c-img"
                  alt="选择"
                />
              </div>
            </div>
            
            {current === 0 ? (
              <Button
                type="primary"
                disabled={delIdlist.length === 0}
                onClick={() => {
                  Dialog.confirm({
                    title: "温馨提示",
                    children: "确定要下架商品吗？",
                    onOk: async () => {
                      try {
                        const res: any = await dynamicBatchOperate({
                          status: '2',
                          ids: delIdlist.join(',')
                        });
                        if (res.code === 0) {
                          Taro.showToast({
                            title: current === 0 ? '下架成功' : '上架成功',
                            icon: 'none'
                          })
                          refreshAll();
                          // Toast.success({ content: '下架成功' });
                          // setTimeout(() => Taro.navigateBack(), 1500);
                        } else {
                          // Toast.error({ content: res.msg });
                        }
                      } catch (err) {
                        console.error(err);
                      }
                    }
                  });
                }}
                className={delIdlist.length > 0 ? "footerBtnBox-btn" : "footerBtnBox-notbtn"}
              >
                下架
              </Button>
            ) : (
              <Button
                type="primary"
                disabled={delIdlist2.length === 0}
                onClick={() => {
                  Dialog.confirm({
                    title: "温馨提示",
                    children: "确定要下架商品吗？",
                    onOk: async () => {
                      try {
                        const res: any = await dynamicBatchOperate({
                          status: '1',
                          ids: delIdlist2.join(',')
                        });
                        if (res.code === 0) {
                          Taro.showToast({
                            title: current === 0 ? '下架成功' : '上架成功',
                            icon: 'none'
                          })
                          refreshAll();
                          // Toast.success({ content: '下架成功' });
                          // setTimeout(() => Taro.navigateBack(), 1500);
                        } else {
                          // Toast.error({ content: res.msg });
                        }
                      } catch (err) {
                        console.error(err);
                      }
                    }
                  });
                }}
                className={delIdlist2.length > 0 ? "footerBtnBox-btn" : "footerBtnBox-notbtn"}
              >
                上架
              </Button>
            )}
          </div>
        </>
      )}
      
      <Dialog
        visible={onOffPopupVisible}
        title="温馨提示"
        platform="ios"
        // okText="确定"
        // cancelText="取消"
        // onConfirm={handleBatchOperation}
        close={() => setOnOffPopupVisible(false)}
      >
        <div dangerouslySetInnerHTML={{ 
          __html: `确定要${current === 0 ? '下架' : '上架'}<font color=#6CBE70>${
            current === 0 ? delIdlist.length : delIdlist2.length
          }</font>件商品吗？` 
        }}/>
      </Dialog>
      
      
      <Dialog
        visible={numPopupVisible}
        title="自定义选择数量"
        platform="ios"
        // okText="确定"
        // cancelText="取消"
        // onConfirm={handleNumConfirm}
        close={() => setNumPopupVisible(false)}
      >
        <input 
          type="number" 
          placeholder="请输入自定义数量"
          value={customNum}
          onChange={(e) => setCustomNum(e.target.value)}
          className="num-input"
        />
      </Dialog>
      
      <Popup
        visible={popupVisible}
        direction="bottom"
        close={() => setPopupVisible(false)}
        maskClosable={true}
        onClose={() => setPopupVisible(false)}
      >
        <div className="popup" style={{ marginBottom: popMarginBottom }}>
          <div className="popup-bottomBox">
            <div className="popup-bottomBox-line" onClick={() => {
              setPopupVisible(false);
              setNumPopupVisible(true);
            }}>
              <span>自定义数量</span>
            </div>
            <div className="popup-bottomBox-divider"></div>
            <div className="popup-bottomBox-line" onClick={getAllChange}>
              <span>全部</span>
            </div>
          </div>
        </div>
      </Popup>
    </div>
  );
};

export default BatchOnOffShelf;