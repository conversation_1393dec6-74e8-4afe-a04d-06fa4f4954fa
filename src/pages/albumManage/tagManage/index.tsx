import { View } from "@tarojs/components";
import React from "react";
import YkNavBar from "@/components/ykNavBar/index";
import { Cell } from "@arco-design/mobile-react";
import Taro from '@tarojs/taro';
import "./index.less";

export default function TagManage() {
  return (
    <View >
      <YkNavBar title="标签管理" />
      <View className="tag-manage-list" >
        <Cell.Group bordered={true}>
          <Cell
            label="标签管理"
            className="tag-manage-cell"
            showArrow
            
            onClick={() => {
              // 跳转到标签管理页面
              Taro.navigateTo({
                url: '/pages/albumManage/tagManage/tag/index'
              })
            }}
          />
          <Cell
            label="目录管理"
            className="tag-manage-cell"
            showArrow
            style={{ color: 'red' }}
            onClick={() => {
              // 跳转到目录管理页面
              Taro.navigateTo({
                url: '/pages/albumManage/tagManage/catalog/index'
              })
            }}
          />
        </Cell.Group>
      </View>
    </View>
  );
}
