@import '@arco-design/mobile-react/style/mixin.less';

[id^="/pages/catalog/addTag"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;  
  });
}

.add-tag-page {
    min-height: 100vh;
    background: #fff;
    display: flex;
    flex-direction: column;
  }
  .add-tag-navbar {

    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

  }
  .add-tag-back {
    position: absolute;
    left: 16px;
    font-size: 22px;
    color: #222;
    top: 0;
    height: 48px;
    line-height: 48px;
    cursor: pointer;
  }
  .add-tag-title {
    font-size: 17px;
    font-weight: 500;
    color: #222;
  }
  .add-tag-search-bar {
    margin-top: 20px;
    padding: 16px;
    background: #fff;
  }
  .add-tag-search-inner {
    display: flex;
    align-items: center;
    background: #f7f8fa;
    border-radius: 8px;
    padding: 0 12px;
    height: 36px;
  }
  .add-tag-search-icon {
    font-size: 18px;
    color: #c9cdd4;
    margin-right: 6px;
  }
  .add-tag-search-input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 15px;
    color: #222;
  }
  .add-tag-list {
    flex: 1;
    background: #fff;
    .use-dark-mode-query({
      background-color: var(--dark-container-background-color);   //灰色背景
    });
      
  }
  .add-tag-item {
    border-bottom: 1px solid #f0f0f0;
    padding-left: 16px;
    display: flex;
    align-items: center;
    height: 52px;
  }
  .add-tag-checkbox {
    font-size: 16px;
  }
  .add-tag-label {
    font-size: 16px;
    color: #222;
    margin-left: 8px;
    .use-dark-mode-query({
      color: var(--dark-font-color);    //白色字体
    });
  }
  .add-tag-btn {
    width: 92vw;
    margin: 32px auto 0 auto;
    height: 44px;
    background: #165DFF;
    color: #fff;
    border-radius: 8px;
    font-size: 17px;
    border: none;
    display: block;
    position: fixed;
    left: 4vw;
    bottom: 24px;
  }