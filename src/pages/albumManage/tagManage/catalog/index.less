@import '@arco-design/mobile-react/style/mixin.less';

[id^="/pages/catalog/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;  
  });
}


.tag-manage-page {
  background-color: #f7f8fa;
  .use-dark-mode-query({
    background-color: @dark-background-color;     //黑色背景
  });


  min-height: 100vh;
  
  .search-bar-s {
    margin: 12px 0;

    .search-input {
      background: #fff;
      .use-dark-mode-query({
        background-color: var(--dark-container-background-color);   //灰色背景
      });

      .use-dark-mode-query({
        color: var(--dark-font-color);    //白色字体
      });

      
      border-radius: 8px;
      padding: 8px 12px;
      font-size: 14px;
    }
  }
  
  .tag-list {
    background: #fff;
    .use-dark-mode-query({
      background-color: var(--dark-container-background-color);   //灰色背景
    });
      
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
    
    .arco-swipe-action {
      margin-bottom: 1px;
    }
    
    .tag-item {
      display: flex;
      // justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #fff;
      .use-dark-mode-query({
        background-color: var(--dark-container-background-color);   //灰色背景
      });
        

      .tag-info {
        display: flex;
        align-items: center;
        
        .tag-name {
          font-size: 16px;
          color: #1d2129;
          .use-dark-mode-query({
            color: var(--dark-font-color);    //白色字体
          });
    
        }
        
        .tag-pinned {
          margin-left: 8px;
          font-size: 12px;
          color: #ff7d00;
          background: #fff7e6;
          padding: 2px 6px;
          border-radius: 2px;
        }
      }
    }
  }

  .sort-btn{
    .use-dark-mode-query({
      color: var(--dark-font-color);    //白色字体
    });
  }
  .footer-actions {
    display: flex;
    justify-content: space-between;
    position: fixed;
    bottom: 16px;
    left: 16px;
    right: 16px;
    
    .left-actions {
      
      display: flex;
      gap: 8px;
      
      button {
        margin-right: 0;
      }
    }
    
    button {
      flex-shrink: 0;
    }
  }
}



.catalog-navbar {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.catalog-back {
  position: absolute;
  left: 16px;
  font-size: 22px;
  color: #222;
  top: 0;
  height: 48px;
  line-height: 48px;
  cursor: pointer;
}

.catalog-title {
  font-size: 17px;
  font-weight: 500;
  color: #222;
}

.catalog-empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 80px;
}

.catalog-empty-icon [stroke]{
  margin-bottom: 16px;
  .use-dark-mode-query({
    stroke: var(--dark-font-color);
  });
}

.rect{
  fill: #E5E6EB;
  .use-dark-mode-query({
  //  fill: var(--dark-container-background-color);   //灰色背景
  });
}

.catalog-empty-text {
  font-size: 16px;
  color: #222;
  margin-bottom: 32px;
  margin-top: 8px;
  .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
}

.catalog-btn-primary {
  width: 180px;
  height: 40px;
  background: #165DFF;
  color: #fff;
  border-radius: 6px;
  font-size: 16px;
  border: none;
  margin-bottom: 16px;
  margin-top: 0;
  display: block;
}

.catalog-btn-outline {
  width: 180px;
  height: 40px;
  background: #fff;
  color: #165DFF;
  border: 1.5px solid #165DFF;
  border-radius: 6px;
  font-size: 16px;
  margin-bottom: 0;
  display: block;
}

.tag-manage-page {
  background-color: #f7f8fa;
  min-height: 100vh;
  
  .search-bar-s {
    margin: 12px 0;
    
    .search-input {
      background: #fff;
      border-radius: 8px;
      padding: 8px 12px;
      font-size: 14px;
    }
  }
  
  .tag-list {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
    
    .arco-swipe-action {
      margin-bottom: 1px;
    }
    
    .tag-item {
      display: flex;
      // justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #fff;
      
      .tag-info {
        display: flex;
        align-items: center;
        
        .tag-name {
          font-size: 16px;
          color: #1d2129;
        }
        
        .tag-pinned {
          margin-left: 8px;
          font-size: 12px;
          color: #ff7d00;
          background: #fff7e6;
          padding: 2px 6px;
          border-radius: 2px;
        }
      }
    }
  }
  
  .footer-actions {
    display: flex;
    justify-content: space-between;
    position: fixed;
    bottom: 16px;
    left: 16px;
    right: 16px;
    
    .left-actions {
      display: flex;
      gap: 8px;
      
      button {
        margin-right: 0;
      }
    }
    
    button {
      flex-shrink: 0;
    }
  }
}

.tag-empty-page {
  width: 100vw;
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
  .use-dark-mode-query({
    background-color: @dark-background-color; 
  });
    
}

.tag-empty-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tag-empty-icon {
  margin-bottom: 16px;
}

.tag-empty-text {
  font-size: 16px;
  color: #1D2129;
  margin-bottom: 20px;
  display: block;
  text-align: center;
}

.tag-item-select {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.tag-checkbox {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  accent-color: #165DFF;
}

.multi-select-footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgb(247, 248,250);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  // box-shadow: 0 -2px 8px rgba(247, 248,250.03);

  z-index: 100;
  .use-dark-mode-query({
    background-color: @dark-background-color; 
  });
}

.footer-cancel {
  flex: 1;
  background: #f5f5f5;
  color: #888;
  border-radius: 6px;
  font-size: 16px;
  height: 40px;
  margin-right: 12px;
  border: none;
}

.footer-delete {
  flex: 1;
  background: #165DFF;
  color: #fff;
  border-radius: 6px;
  font-size: 16px;
  height: 40px;
  border: none;
}

.delete-confirm-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-confirm-modal {
  background: #fff;
  border-radius: 12px;
  width: 80vw;
  max-width: 320px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  padding: 24px 0 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.delete-confirm-title {
  font-size: 18px;
  font-weight: bold;
  color: #222;
  text-align: center;
  margin-bottom: 12px;
}

.delete-confirm-content {
  font-size: 15px;
  color: #222;
  text-align: center;
  margin-bottom: 8px;
  line-height: 1.5;
}

.delete-confirm-desc {
  font-size: 13px;
  color: #8c8c8c;
  display: block;
  margin-top: 2px;
}

.delete-confirm-actions {
  display: flex;
  width: 100%;
  border-top: 1px solid #f0f0f0;
  margin-top: 18px;
}

.delete-confirm-cancel {
  flex: 1;
  text-align: center;
  padding: 14px 0;
  color: #888;
  font-size: 16px;
  border-right: 1px solid #f0f0f0;
  cursor: pointer;
}

.delete-confirm-delete {
  flex: 1;
  text-align: center;
  padding: 14px 0;
  color: #F53F3F;
  font-size: 16px;
  cursor: pointer;
}

.tag-item {
  align-items: center;
  padding: 12px 0px;
  margin-left: 16px;
  margin-right: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;

  background: #fff;
  .use-dark-mode-query({
    background-color: @dark-background-color; 
  });



}
.tag-item-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;

 
}
.tag-item-title {
  font-size: 16px;
  color: #222;
  font-weight: 500;
  margin-bottom: 4px;
  .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
}
.tag-item-sub {
  font-size: 13px;
  color: #8c8c8c;
}
.tag-item-drag {
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-pinned {
  margin-left: 8px;
  font-size: 12px;
  color: #ff7d00;
  background: #fff7e6;
  padding: 2px 6px;
  border-radius: 2px;
}