import { View, Text, Input, Button } from "@tarojs/components";
import Taro,{ useDidShow } from "@tarojs/taro";
import React, { useState, useEffect } from "react";
import { editTag, getTagDetail } from "@/utils/api/common/common_user";
import "./editCatalog.less";
import YkNavBar from "@/components/ykNavBar/index";



export default function CreateCatalog() {
  const [catalogName, setCatalogName] = useState("");
  const [tags, setTags] = useState<any[]>([]);
  const [tagId, setTagId] = useState("");

  const userInfo = Taro.getStorageSync('userInfo');

  useDidShow(() => {
    const selected = Taro.getStorageSync('selectedTag') || [];
          setTags(selected)
          console.log(selected,'selected')
  });

  const handleRemove = (id) => {
    const newTag = tags.filter(item => item.id !== id);
    setTags(newTag);
    Taro.setStorageSync('selectedTag', newTag);
  };

  const getTagDetailData = async (id: string) => {
    // const selected = Taro.getStorageSync('selectedArticles') || [];
    // setArticles(selected);
    let res: any = await getTagDetail({id: id});
    if (res.code == 0) {
        setCatalogName(res.data.name);
        if (res.data.labelList) {
            const labelList=res.data.labelList
            setTags(labelList);
            Taro.setStorageSync('selectedTag', labelList);
            // setTags(labelIds.map(item => {
            //     return {
            //         id: item,
            //         name: item
            //     }
            // }))
        }
    }
  }


  useEffect(() => {
 
    const params = Taro.getCurrentInstance().router?.params;
    if (params?.id) {
         setTagId(params.id);
         getTagDetailData(params.id);
    }
  }, []);

  return (
    <View className="create-catalog-page">
      <YkNavBar title="编辑目录" /> 
      <View className="catalog-form">
        <View className="catalog-label">目录名称</View>
        <Input
          className="catalog-input"
          value={catalogName}
          onInput={e => setCatalogName(e.detail.value)}
          placeholder="请输入目录名称"
        />
        <View className="catalog-label">
          目录（{tags.length}）
        </View>
        <View className="catalog-tag-list">
          <View className="catalog-tag-add" onClick={() => {}}>
            <Text className="catalog-tag-add-icon">＋</Text>
            <Text className="catalog-tag-add-text"
                    onClick={() => {
                        Taro.navigateTo({
                          url: '/pages/albumManage/tagManage/catalog/addTag'
                        })
                      }}
            >添加标签
            </Text>
          </View>
          {tags.map(tag => (
            <View className="catalog-tag-item" key={tag.id}>
              <Text className="catalog-tag-name">{tag.name}</Text>
              <Text className="article-remove-s" onClick={() => handleRemove(tag.id)}>删除</Text>
              {/* <View className="catalog-tag-drag">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                  <circle cx="5" cy="7" r="1.2" fill="#D8D8D8"/>
                  <circle cx="5" cy="11" r="1.2" fill="#D8D8D8"/>
                  <circle cx="13" cy="7" r="1.2" fill="#D8D8D8"/>
                  <circle cx="13" cy="11" r="1.2" fill="#D8D8D8"/>
                </svg>
              </View> */}
            </View>
          ))}
        </View>
      </View>
      <Button className="catalog-btn-finish"
        onClick={async () => {
          if (!catalogName) {
            Taro.showToast({ title: '请输入标签名称', icon: 'none' });
            return;
          }
          try {
            const ids = tags.map(item => item.id);
            let data = {
              id: tagId,
              name: catalogName,
              parentId: 0,
              userId: userInfo.id,
              labelIds: ids.join(','),
              type: 2,
    
            }
            console.log(data, 'data')
            let res: any = await editTag(data);
            Taro.setStorageSync('selectedTag', []);
            if (res.code == 0) {
              Taro.setStorageSync('selectedTag', []);
              Taro.showToast({ title: '编辑成功', icon: 'success' });
              setTimeout(() => {
                // Taro.navigateTo({
                //   url: '/pages/albumManage/tagManage/catalog/index'
                // });
                Taro.navigateBack({
                  delta:1
                });
              }, 1000)
              
            } else {
              Taro.showToast({ title: res.msg, icon: 'none' });
            }
          } catch (e) {
            Taro.showToast({ title: '创建失败', icon: 'none' });
          }
        }}

      >完成</Button>
    </View>
  );
}