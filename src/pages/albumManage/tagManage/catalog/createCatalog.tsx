import { View, Text, Input, Button } from "@tarojs/components";
import Taro,{ useDidShow } from "@tarojs/taro";
import React, { useState } from "react";
import { createTag } from "@/utils/api/common/common_user";
import YkNavBar from "@/components/ykNavBar/index";
import "./createCatalog.less";




export default function CreateCatalog() {
  const [catalogName, setCatalogName] = useState("");
  const [tags, setTags] = useState<any[]>([]);
  const userInfo = Taro.getStorageSync('userInfo');

  useDidShow(() => {
    const selected = Taro.getStorageSync('selectedTag') || [];
          setTags(selected)
          console.log(selected,'selected')
    
  });

  const handleRemove = (id) => {
    const newTag = tags.filter(item => item.id !== id);
    setTags(newTag);
    Taro.setStorageSync('selectedTag', newTag);
  };



  return (
    <View className="create-catalog-page">
      <View >
        <YkNavBar title="新建目录" /> 
      </View>
     
        <View className="catalog-label">目录名称</View>
        <Input
          className="catalog-input"
          value={catalogName}
          onInput={e => setCatalogName(e.detail.value)}
          placeholder="请输入目录名称"
        />
        <View className="catalog-label">
          标签（{tags.length}）
        </View>
        <View className="catalog-tag-list">
          <View className="catalog-tag-add" onClick={() => {}}>
            <Text className="catalog-tag-add-icon">＋</Text>
            <Text className="catalog-tag-add-text"
                    onClick={() => {
                        Taro.navigateTo({
                          url: '/pages/albumManage/tagManage/catalog/addTag'
                        })
                      }}
            >添加标签
            </Text>
          </View>
          {tags.map(tag => (
            <View className="catalog-tag-item" key={tag.id}>
              <Text className="catalog-tag-name">{tag.name}</Text>
              <Text className="article-remove" onClick={() => handleRemove(tag.id)}>删除</Text>
              {/* <View className="catalog-tag-drag">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                  <circle cx="5" cy="7" r="1.2" fill="#D8D8D8"/>
                  <circle cx="5" cy="11" r="1.2" fill="#D8D8D8"/>
                  <circle cx="13" cy="7" r="1.2" fill="#D8D8D8"/>
                  <circle cx="13" cy="11" r="1.2" fill="#D8D8D8"/>
                </svg>
              </View> */}
            </View>
          ))}
        </View>
     
      <Button className="catalog-btn-finish"
        onClick={async () => {
          if (!catalogName) {
            Taro.showToast({ title: '请输入标签名称', icon: 'none' });
            return;
          }
          try {
            const ids = tags.map(item => item.id);
            let data = {
              name: catalogName,
              parentId: 0,
              userId: userInfo.id,
              labelIds: ids.join(','),
              type: 2,
    
            }
            let res: any = await createTag(data);
            Taro.setStorageSync('selectedTag', []);
            if (res.code == 0) {
              Taro.setStorageSync('selectedTag', []);
              Taro.showToast({ title: '创建成功', icon: 'success' });
              setTimeout(() => {
                // Taro.navigateTo({
                //   url: '/pages/albumManage/tagManage/catalog/index'
                // });
                Taro.navigateBack({
                  delta:1
                });
              }, 2000)
              
            } else {
              Taro.showToast({ title: res.msg, icon: 'none' });
            }
          } catch (e) {
            Taro.showToast({ title: '创建失败', icon: 'none' });
          }
        }}

      >完成</Button>
    </View>
  );
}