@import '@arco-design/mobile-react/style/mixin.less';

[id^="/pages/catalog/editCatalog"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;  
  });
}

.create-catalog-page {
    min-height: 100vh;
    background: #fafbfc;
    display: flex;
    flex-direction: column;
    .use-dark-mode-query({
      background-color: @dark-background-color;     //黑色背景
    });
  }
  .catalog-navbar {
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
  }
  .catalog-back {
    position: absolute;
    left: 16px;
    font-size: 22px;
    color: #222;
    top: 0;
    height: 48px;
    line-height: 48px;
    cursor: pointer;
  }
  .catalog-title {
    font-size: 17px;
    font-weight: 500;
    color: #222;
  }
  .catalog-form {
    background: #fff;
    padding: 0 0 16px 0;
    // .use-dark-mode-query({
    //   background-color: @dark-background-color;     //黑色背景
    // });
  }
  .catalog-label {
    font-size: 14px;
    color: #8c8c8c;
    background: #f7f8fa;
    padding: 12px 16px 0 16px;
    height: 30px;
    line-height: 20px;
    .use-dark-mode-query({
      background-color: var(--dark-container-background-color);   //灰色背景
    });

  }

  .catalog-input {
    height: 50px;
    line-height: 50px;
    width: 100%;
    border: none;
    background: #fff;
    font-size: 16px;
    color: #222;
    padding: 15px 16px;
    box-sizing: border-box;
    .use-dark-mode-query({
      background-color: @dark-background-color; 
    });
    .use-dark-mode-query({
      color: var(--dark-font-color);    //白色字体
    });

  }
  .catalog-tag-list {
    background: #fff;
    padding: 0 0 0 0;
    .use-dark-mode-query({
      background-color: @dark-background-color; 
    });

  }
  .catalog-tag-add {
    display: flex;
    align-items: center;
    color: #165DFF;
    font-size: 16px;
    padding: 12px 16px;
    cursor: pointer;
  }
  .catalog-tag-add-icon {
    font-size: 20px;
    margin-right: 4px;
  }
  .catalog-tag-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
    .use-dark-mode-query({
      background-color: @dark-background-color; 
    });
  }
  .catalog-tag-name {
    font-size: 16px;
    color: #222;
    flex: 1;
    .use-dark-mode-query({
      color: var(--dark-font-color);    //白色字体
    });
  }
  .catalog-tag-drag {
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .catalog-btn-finish {
    width: 92vw;
    margin: 32px auto 0 auto;
    height: 44px;
    background: #165DFF;
    color: #fff;
    border-radius: 8px;
    font-size: 17px;
    border: none;
    display: block;
    position: fixed;
    left: 4vw;
    bottom: 24px;
  }
  .article-remove {
    // position: absolute;
    // right: 16px;
    // top: 16px;
    color: #F53F3F;
    font-size: 13px;
    cursor: pointer;
  }