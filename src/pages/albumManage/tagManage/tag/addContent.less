@import '@arco-design/mobile-react/style/mixin.less';

[id^="/pages/tag/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;  
  });
}

.add-content-page {
  width: 100vw;
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
  .use-dark-mode-query({
    background-color: @dark-background-color;     //黑色背景
  });

}

.nav-bar-row {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  //height: 44px;
  background: #fff;
}


.nav-bar-filter {
  position: absolute;
  right: 16px;
  top: 0;
  height: 44px;
  line-height: 44px;
  font-size: 15px;
  color: #165DFF;
  z-index: 10;
}

.search-bar-wrap {
  padding: 0 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  .use-dark-mode-query({
    background-color: @dark-background-color;     //黑色背景
  })
}

.search-bar {
  width: 100%;
  height: 32px;
  background: #f7f8fa;
  border-radius: 16px;
  border: none;
  font-size: 14px;
  color: #222;
  margin: 8px 0;
  padding: 0 12px;
  box-sizing: border-box;

}

.content-list {
  flex: 1;
  overflow-y: auto;
  background: #fff;
  .use-dark-mode-query({
    background-color: @dark-background-color;     //黑色背景
  })
}


.content-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px 0 16px;
  width: 400px;
  // border-bottom: 1px solid #f0f0f0;
  background: #fff;
  .use-dark-mode-query({
    background-color: @dark-background-color;     //黑色背景
  });
  

  
}
.content-item.selected {
  background: #fff;
  .use-dark-mode-query({
    background-color: @dark-background-color;     //黑色背景
  });
}

.item-checkbox {
  margin-right: 8px;
  margin-top: 6px;
}

.checkbox-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1.5px solid #c0c4cc;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.checkbox-circle.checked {
  border-color: #165DFF;
  background: #165DFF;
}
.checkbox-circle.checked::after {
  content: '';
  display: block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #fff;
}

.item-main {
  flex: 1;
  min-width: 0;
}
.item-title {
  font-size: 15px;
  color: #222;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
}
.item-images {
  display: flex;
  gap: 4px;
  margin-bottom: 6px;
}
.item-img {
  width: 44px;
  height: 44px;
  border-radius: 4px;
  object-fit: cover;
  background: #f7f8fa;
}
.item-desc {
  font-size: 13px;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.add-content-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px 16px 16px;
  background: #fff;
  height: 56px;
  border-top: 1px solid #f0f0f0;
  .use-dark-mode-query({
    background-color: @dark-background-color;     //黑色背景
  });
  
}
.footer-left {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: left;
  font-size: 15px;
  color: #222;
 }


.footer-all {
  margin-left: 8px;
}
.footer-count {
  color: #222;
  margin-left: -3px;
  .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });

}
.footer-btn {
  background: #165DFF;
  color: #fff;
  border-radius: 6px;
  font-size: 16px;
  height: 36px;
  line-height: 36px;
  padding: 0 32px;
  border: none;
  margin-left: 12px;
}


.arco-input-prefix {
  width:20px;
  margin:0;
  padding:0;

}
.arco-input-label {
  min-width:40px
}
