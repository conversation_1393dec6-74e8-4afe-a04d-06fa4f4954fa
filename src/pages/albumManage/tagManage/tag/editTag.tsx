import { View, Text, Input, Image } from "@tarojs/components";
import React, { useState, useEffect } from "react";
import YkNavBar from "@/components/ykNavBar/index";
import { Cell, Button } from "@arco-design/mobile-react";
import Taro, { useDidShow } from '@tarojs/taro';
import { editTag, uploadFile, getTagDetail } from "@/utils/api/common/common_user";
import "./editTag.less";

export default function EditTag() {
  const [coverImg, setCoverImg] = useState<string>("");
  const [defaultCover, setDefaultCover] = useState('默认');
  const [tagName, setTagName] = useState('');
  const [articles, setArticles] = useState<any[]>([]);
  const [tagId, setTagId] = useState<string>(0);
  const userInfo = Taro.getStorageSync('userInfo');

  useDidShow(() => {
    
    const selected = Taro.getStorageSync('selectedArticles') || [];
    console.log(selected,'selectedArticles')
          setArticles(selected);
  });

  useEffect(() => {
 
    const params = Taro.getCurrentInstance().router?.params;
    if (params?.id) {
      setTagId(params.id);
      getTagDetailData(params.id);
    }
  }, []);

  const getTagDetailData = async (id: string) => {
    // const selected = Taro.getStorageSync('selectedArticles') || [];
    // setArticles(selected);
    let res: any = await getTagDetail({id: id});
    console.log(res,'res')
    if (res.code == 0) {
      setTagName(res.data.name);
      setCoverImg(res.data.coverImage);   
      res.data.coverImage ? setDefaultCover('') : setDefaultCover('默认')
      
      if(res.data.dynamics){
        if (Array.isArray(res.data.dynamics)) {
            const processedDynamics = res.data.dynamics.map(item => {
              if (item.pictures) {
                // 去除首尾逗号，按逗号分割，并过滤空值
                const trimmedPictures = item.pictures.replace(/^,+|,+$/g, '');
                item.pictures = trimmedPictures 
                  ? trimmedPictures.split(',').filter(pic => pic.trim()) 
                  : [];
              } else {
                item.pictures = [];
              }
              return item;
            });  
            setArticles(processedDynamics);
            Taro.setStorageSync('selectedArticles', processedDynamics);
          } 
      }else{
        setArticles([]);
        Taro.setStorageSync('selectedArticles', []);
      }
    }
  }
  const handleChooseCover = async () => {
    const res = await Taro.chooseImage({ count: 1 });
    if (res.tempFilePaths && res.tempFilePaths[0]) {
      try {
        const uploadRes: any = await uploadFile([res.tempFilePaths[0]]);
        let url = '';
        if (uploadRes.data && typeof uploadRes.data === 'string') {
          url = uploadRes.data;
        } else if (uploadRes.data && uploadRes.data.url) {
          url = uploadRes.data.url;
        } else if (Array.isArray(uploadRes.data) && uploadRes.data[0]?.url) {
          url = uploadRes.data[0].url;
        }
        
        setCoverImg(url);
        setDefaultCover('');
      } catch (e) {
        Taro.showToast({ title: '上传失败', icon: 'none' });
      }
    }
  };

  const handleRemove = (id) => {
    const newArticles = articles.filter(item => item.id !== id);
    setArticles(newArticles);
    Taro.setStorageSync('selectedArticles', newArticles);
  };

  return (
    <View className="edit-tag-page">
      <YkNavBar title="编辑标签" />
      <View className="edit-tag-content">
        {/* 标签名称 */}
        <View className="form-group">
          <Text className="form-label">标签名称</Text>
          <Input className="form-input" placeholder="未设置标签名称" value={tagName} onInput={e => setTagName(e.detail.value)} />
        </View>
        {/* 封面图分组 */}
        <Text className="group-title">封面图</Text>
        <Cell.Group bordered={false}>
          <View style={{position: 'relative'}}>
            <Cell
              label="封面图"
              className="cover-cell"
              showArrow
              text={defaultCover}
              onClick={handleChooseCover}
            />
            {coverImg && (
              <Image className="cover-thumb" src={coverImg} mode="aspectFill" />
            )}
          </View>
        </Cell.Group>
        {/* 图文分组 */}
        <Text className="group-title">图文（{articles.length}）</Text>
        <View className="article-list">
          {articles.map(item => (
            <View key={item.id} className="article-item">
              <View className="article-main">
                <View className="article-title">{item.content}</View>
                {item.pictures && item.pictures.length > 0 && (
                  <View className="article-images">
                    {item.pictures.slice(0, 7).map((img, idx) => {
                      if(img){
                        return <Image src={img} key={idx} className="article-img" />
                      }
                    })}
                  </View>
                )}
              </View>
              <Text className="article-remove" onClick={() => handleRemove(item.id)}>删除</Text>
            </View>
          ))}
        </View>
        
        <Button className="add-article-btn" 
                icon={<span className="plus-icon">＋</span>}
                onClick={() => {
                  Taro.navigateTo({
                    url: '/pages/albumManage/tagManage/tag/addContent'
                  })
                }}
        >
          添加图文
        </Button>
      </View>
      <Button className="submit-btn" onClick={async () => {
        if (!tagName) {
          Taro.showToast({ title: '请输入标签名称', icon: 'none' });
          return;
        }
        try {
          const ids = articles.map(item => item.id);
          let data = {
            id: tagId,
            name: tagName,
            userId: userInfo.id,
            dynamicsId: ids.join(','),
            type: 1,
            coverImage: coverImg
          }
          Taro.setStorageSync('selectedArticles', []);
          let res: any = await editTag(data);
          if (res.code == 0) {
            Taro.setStorageSync('selectedArticles', []);
            Taro.showToast({ title: '编辑成功', icon: 'success' });
            setTimeout(() => {
                // Taro.navigateTo({
                //   url: '/pages/albumManage/tagManage/tag/index'
                // });
                Taro.navigateBack({
                  delta:1
                });
              }, 2000)
          } else {
            Taro.showToast({ title: res.msg, icon: 'none' });
          }
        } catch (e) {
          Taro.showToast({ title: '更新失败', icon: 'none' });
        }
      }}>完成</Button>
    </View>
  );
}
