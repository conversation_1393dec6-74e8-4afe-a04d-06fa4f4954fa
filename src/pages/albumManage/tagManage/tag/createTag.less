@import '@arco-design/mobile-react/style/mixin.less';

[id^="/pages/tag/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;  
  });
}

.create-tag-page {
  width: 100vw;
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
  .use-dark-mode-query({
    background-color: @dark-background-color;  
  });
}

.create-tag-content {
  flex: 1;
  padding: 0 16px;
  box-sizing: border-box;
}

.form-group {
  margin-top: 16px;
  margin-bottom: 12px;
}

.form-label {
  font-size: 13px;
  color: #999;
  margin-bottom: 6px;
  display: block;
}

.form-input {
  width: 100%;
  height: 40px;
  border: none;
  background: #f7f8fa;
  border-radius: 6px;
  font-size: 15px;
  color: #1D2129;
  box-sizing: border-box;
  padding: 8px 10px;
  .use-dark-mode-query({
    background-color: var(--dark-container-background-color);   //灰色背景
  });
  .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
}

.group-title {
  font-size: 13px;
  color: #999;
  margin: 18px 0 6px 0;
  display: block;
}

.cover-cell {
  font-size: 15px;
  color: #1D2129;
  margin-left: 0px;
  .use-dark-mode-query({
    background-color: @dark-background-color;  
  });
  margin: 0;
}

.cover-default {
  color: #999;
  font-size: 14px;
}

.cover-thumb {
  position: absolute;
  right: 16px;
  top: 50%;
  width: 36px;
  height: 36px;
  border-radius: 6px;
  transform: translateY(-50%);
  object-fit: cover;
  background: #f7f8fa;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.article-list {
  background: #fff;
  border-radius: 10px;
  //box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  margin-bottom: 12px;
  .use-dark-mode-query({
    background-color: var(--dark-container-background-color);   //灰色背景
  });
  .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });

}

.article-item {
  padding: 12px 16px 0 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  position: relative;
}
.article-item:last-child {
  border-bottom: none;
}
.article-main {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.article-title {
  font-size: 15px;
  color: #222;
  font-weight: 500;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
}
.article-images {
  display: flex;
  gap: 4px;
  margin-bottom: 2px;
}
.article-img {
  width: 44px;
  height: 44px;
  border-radius: 4px;
  object-fit: cover;
  background: #f7f8fa;
}
.article-desc {
  font-size: 13px;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.article-remove {
  position: absolute;
  right: 16px;
  top: 16px;
  color: #F53F3F;
  font-size: 13px;
  cursor: pointer;
}

.add-article-row {
  display: flex;
  align-items: center;
  padding: 8px 0 0 0;
}
.add-article-btn {
  color: #165DFF;
  background: none;
  border: none;
  font-size: 15px;
  display: flex;
  align-items: center;
  padding: 0;
}
.plus-icon {
  font-size: 18px;
  margin-right: 4px;
}

.submit-btn {
  width: calc(100% - 32px);
  margin: 24px 16px 16px 16px;
  background: #165DFF;
  color: #fff;
  border-radius: 6px;
  font-size: 16px;
  height: 44px;
  line-height: 44px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
