import { View, Text,  Image } from "@tarojs/components";
import React, { useState, useRef, useEffect } from "react";
import YkNavBar from "@/components/ykNavBar/index";
import { Cell, Button, Checkbox, Input, SearchBar  } from "@arco-design/mobile-react";
import { getAlbumList, getSearchAlbumList } from "@/utils/api/common/common_user";
import { LoadMoreStatus } from "@arco-design/mobile-react/cjs/load-more";
import Taro,{ useLoad } from "@tarojs/taro";
import IconSearch from "@arco-design/mobile-react/esm/icon/IconSearch";

import "./addContent.less";

// const mockList = [
//   {
//     id: 1,
//     content: "最新潮鞋 工厂大清仓 持续特价 ",
//     pictures: [
//       "https://minio.npm.fjpipixia.com/album/20250508/b1_1746668556622.jpg",
//       "https://minio.npm.fjpipixia.com/album/20250508/b1_1746668556622.jpg",
//       "https://minio.npm.fjpipixia.com/album/20250508/b1_1746668556622.jpg",
//       "https://minio.npm.fjpipixia.com/album/20250508/b1_1746668556622.jpg",
//       "https://minio.npm.fjpipixia.com/album/20250508/b1_1746668556622.jpg",
//       "https://minio.npm.fjpipixia.com/album/20250508/b1_1746668556622.jpg",
//     ],
//     desc: "新品 最新潮鞋 工厂大清仓 持续特价 白原气女安..."
//   },
//   {
//     id: 2,
//     content: "新品 最新潮鞋 工厂大清仓 持续特价",
//     pictures: [
//       "https://minio.npm.fjpipixia.com/album/20250508/b1_1746668556622.jpg",
//       "https://minio.npm.fjpipixia.com/album/20250508/b1_1746668556622.jpg",
//       "https://minio.npm.fjpipixia.com/album/20250508/b1_1746668556622.jpg",
//     ],
//     desc: "Air Jordan 1 Low AJ1 P3980 厚sole强支撑..."
//   },
//   {
//     id: 3,
//     content: "Air Jordan 1 Low AJ1 P3980 厚sole强支撑...",
//     pictures: [],
//     desc: "高端品质 Chan*1234实寄新单 专柜同步春季..."
//   }
// ];



export default function AddContent() {
  const userInfo = Taro.getStorageSync("userInfo");
 
  const [search, setSearch] = useState("");
  const [selected, setSelected] = useState<number[]>([]);
  const [allIds, setAllIds] = useState<number[]>([]);
  const allSelected = selected.length === allIds.length;
  const [albumLimit, setAlbumLimit] = React.useState(10);
  const albumPageRef = useRef(1);
  const [originAlbumList, setOriginAlbumList] = useState<any[]>([]);
  const [albumList, setAlbumList] = useState<any[]>([]);
  const [isLoadEnd, setIsLoadEnd] = React.useState(false);
  const [loadStatus, setLoadStatus] = React.useState<LoadMoreStatus>("prepare"); //"before-ready" | "prepare" | "loading" | "nomore" | "retry"
    // 获取相册列表
  const getAlbumListData = async () => {
            let data = {
                pageNo: albumPageRef.current,
                pageSize: albumLimit,
                userId: userInfo.id,
                content: search
            };
            // if (isLoadEnd) {
            //   setLoadStatus("nomore");
            //   return;
            // }
            let res: any;
            console.log(data, "data");
            res = await getAlbumList(data);
            if (res.code == 0) {
                const updatedAlbumList = res.data.list.map((album) => {
                    // 如果 pictures 是字符串，按逗号分割成数组
                    const picturesArray = (album.pictures === '' || album.pictures === ',') 
                      ?  []
                      : album.pictures.split(','); // 如果已经是数组，保持不变
              
                    return {
                      ...album, // 保留其他字段
                      pictures: picturesArray, // 更新 pictures 为数组
                    };
                  });
                setAllIds(res.data.list.map(i => i.id));
                setOriginAlbumList(updatedAlbumList);
                setAlbumList(updatedAlbumList);
                // 判断返回数据的总条数
                if (res.data.list.length < albumLimit) {    //最后一页
                    setIsLoadEnd(true);
                    setLoadStatus("nomore");
                } else {
                    setLoadStatus("prepare");
                    setIsLoadEnd(false);
                albumPageRef.current = albumPageRef.current + 1;
                }
            } else {
                setLoadStatus("retry");
                Taro.showToast({
                    title: '请求失败',
                    icon: 'none'
                });
            }
  };
  const handleChange = (vals: any) => {
    setSelected(vals);
  };
  const handleSelectAll = () => {
    setSelected(allSelected ? [] : allIds);
  };

  useLoad(() => {
    albumPageRef.current = 1;
    setIsLoadEnd(false);
    getAlbumListData();
   
  });

  useEffect(() => {

    console.log(albumList, "albumList");
  }, [albumList]);


  return (
    <View className="add-content-page">
      <View className="nav-bar-row">
        <YkNavBar title="添加图文" />
      </View>
        <SearchBar 

          actionButton={<span className="demo-search-btn"></span>} 
          placeholder="筛选."
          onChange={e => {
            const value =  e.target.value;
            //setSearch(value);
            if (!value) {
              setAlbumList(originAlbumList);
            } else {
              setAlbumList(originAlbumList.filter(item => item.content.includes(value)));
            }
          }}
          className="demo-input-btn-input"
          clearable
          onClear={() => {
            //setSearch(''); // 清空输入框
            setAlbumList(originAlbumList);              
          }}
        //   suffix={<div className="demo-input-btn-wrap">
        //     <Button inline  size="mini" onClick={() => {
        //         getAlbumListData();
        //     }}>Send</Button>
        // </div>}
        //   border="none"
        />


     
      <View className="content-list" style={{ margin: "10px 10px",width: "100%" }}>
        <Checkbox.Group value={selected} onChange={handleChange}>
          {albumList.map(item => (
            <View key={item.id} className="checkbox-item-container">
              <Checkbox value={item.id} className="content-item-checkbox">
                <View className={`content-item${selected.includes(item.id) ? ' selected' : ''}`}>
                  <View className="item-main">
                    <View className="item-title">{item.content}</View>
                    {item.pictures.length > 0 && (
                      <View className="item-images">
                        {item.pictures.slice(0,7).map((img, idx) => (
                          <Image className="item-img" src={img} key={idx} />
                        ))}
                      </View>
                    )}
                  </View>
                </View>
              </Checkbox>
            </View>
          ))}
        </Checkbox.Group>
      </View>
      <View className="add-content-footer">
        <View className="footer-left" onClick={handleSelectAll}>
          <Checkbox checked={allSelected} value="全选" />
          {/* {selected.length > 0 && <Text className="footer-count">（{selected.length}）</Text>} */}
          {<Text className="footer-count">（{selected.length}）</Text>}
        </View>
        <Button className="footer-btn" onClick={() => {
          const selectedItems = albumList.filter(item => selected.includes(item.id));
          const selectedArticles = Taro.getStorageSync('selectedArticles') || [];
          // 去重
          const newArticles = [
            ...selectedArticles,
            ...selectedItems.filter(item => !selectedArticles.some(a => a.id === item.id))
          ];
          const oldArticles=Taro.getStorageSync('selectedArticles') || []
          const tempData=[
            ...oldArticles, 
            ...newArticles.filter(item => !oldArticles.some(a => a.id === item.id))
          ]
          Taro.setStorageSync('selectedArticles', tempData);
          Taro.navigateBack();
        }}>添加</Button>
      </View>
    </View>
  );
}
