import { View, Text } from "@tarojs/components";
import Taro from "@tarojs/taro";
import React, { useState, useEffect } from "react";
import YkNavBar from "@/components/ykNavBar/index";
import { Checkbox } from "@arco-design/mobile-react";
import "./setSort.less";

const SORT_OPTIONS = [
  { label: "默认排序", value: "0" },
  { label: "字母排序", value: "1" }
];

export default function SetSort() {
  const [selected, setSelected] = useState("");
  const [pending, setPending] = useState("default");
  const [showConfirm, setShowConfirm] = useState(false);

  // 选择排序方式时，先弹出确认框
  const handleSelect = (value) => {
    setPending(value);
    setShowConfirm(true);
  };

  // 确认修改
  const handleConfirm = (value) => {
    Taro.setStorageSync('selectedTagSort', value);
    setSelected(pending);
    setShowConfirm(false);
    // 可在此处保存到 storage 或回传
  };
  useEffect(() => {
    const selectedTagSort = Taro.getStorageSync('selectedTagSort');
    setSelected(selectedTagSort || "0");
  }, []);
  return (
    <View className="set-sort-page">
      <YkNavBar title="排序方式" />
      <View className="set-sort-list">
        {SORT_OPTIONS.map((item, idx) => (
          <View
            className="set-sort-item"
            key=""
          >
            <Checkbox
              checked={selected === item.value}
              onChange={() => handleSelect(item.value)}
              className="set-sort-checkbox"
              shape="circle"
              value=""
            >
                <Text className="set-sort-label">{item.label}</Text>
            </Checkbox>
            {idx !== SORT_OPTIONS.length - 1 && <View className="set-sort-divider" />}
          </View>
        ))}
      </View>
      {showConfirm && (
        <View className="sort-confirm-mask">
          <View className="sort-confirm-modal">
            <View className="sort-confirm-title">确认修改排序方式?</View>
            <View className="sort-confirm-content">
              排序方式修改后目录将按新的方式<br />
              排序并显示
            </View>
            <View className="sort-confirm-actions">
              <View className="sort-confirm-cancel" onClick={() => setShowConfirm(false)}>取消</View>
              <View className="sort-confirm-ok" onClick={() => handleConfirm(pending)}>确定</View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}
