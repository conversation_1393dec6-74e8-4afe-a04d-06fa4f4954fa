import { View, Text, Input, Image } from "@tarojs/components";
import React, { useState, useEffect } from "react";
import YkNavBar from "@/components/ykNavBar/index";
import { Cell, Button } from "@arco-design/mobile-react";
import Taro, { useDidShow } from '@tarojs/taro';
import { createTag, uploadFile } from "@/utils/api/common/common_user";
import "./createTag.less";

export default function CreateTag() {
  const [coverImg, setCoverImg] = useState<string>("");
  const [defaultCover, setDefaultCover] = useState('默认');
  const [tagName, setTagName] = useState('');
  const [articles, setArticles] = useState<any[]>([]);
  const userInfo = Taro.getStorageSync('userInfo');

  useDidShow(() => {
    const selected = Taro.getStorageSync('selectedArticles') || [];
          setArticles(selected);
    
  });

  const handleChooseCover = async () => {
    const res = await Taro.chooseImage({ count: 1,sourceType: ['album', 'camera'] });
    if (res.tempFilePaths && res.tempFilePaths[0]) {
      // setCoverImg(res.tempFilePaths[0]);
      // setDefaultCover('')
      // 上传图片
      try {
        const uploadRes: any = await uploadFile([res.tempFilePaths[0]]);
        // 假设返回 { data: { url: 'xxx' } } 或数组
        let url = '';
        console.log(uploadRes,'-----------')
        if (uploadRes.data && typeof uploadRes.data === 'string') {
          url = uploadRes.data;
        } else if (uploadRes.data && uploadRes.data.url) {
          url = uploadRes.data.url;
        } else if (Array.isArray(uploadRes.data) && uploadRes.data[0]?.url) {
          url = uploadRes.data[0].url;
        }
        
        setCoverImg(url);
        setDefaultCover('');
      } catch (e) {
        Taro.showToast({ title: '上传失败', icon: 'none' });
      }
    }
  };

  const handleRemove = (id) => {
    const newArticles = articles.filter(item => item.id !== id);
    console.log(newArticles,'newArticles')
    setArticles(newArticles);
    Taro.setStorageSync('selectedArticles', newArticles);
  };

  return (
    <View className="create-tag-page">
      <YkNavBar title="新建标签." />
      <View className="create-tag-content">
        {/* 标签名称 */}
        <View className="form-group">
          <Text className="form-label">标签名称</Text>
          <Input className="form-input" placeholder="未设置标签名称" value={tagName} onInput={e => setTagName(e.detail.value)} />
        </View>
        {/* 封面图分组 */}
        <Text className="group-title">封面图</Text>
        <Cell.Group bordered={false}>
          <View style={{position: 'relative'}}>
            <Cell
              label="封面图"
              className="cover-cell"
              showArrow
              text={defaultCover}
              onClick={handleChooseCover}
            />
            {coverImg && (
              <Image className="cover-thumb" src={coverImg} mode="aspectFill" />
            )}
          </View>
        </Cell.Group>
        {/* 图文分组 */}
        <Text className="group-title">图文（{articles.length}）</Text>
        <View className="article-list">
          {articles.map(item => (
            <View key={item.id} className="article-item">
              <View className="article-main">
                <View className="article-title">{item.content}</View>
                {item.pictures && item.pictures.length > 0 && (
                  <View className="article-images">
                    {item.pictures.slice(0, 7).map((img, idx) => (
                      <Image src={img} key={idx} className="article-img" />
                    ))}
                  </View>
                )}
                {/* <View className="article-desc">{item.desc}</View> */}
              </View>
              <Text className="article-remove" onClick={() => handleRemove(item.id)}>删除</Text>
            </View>
          ))}
        </View>
        <View className="add-article-row">
          <Button className="add-article-btn" 
                  icon={<span className="plus-icon">＋</span>}
                  onClick={() => {
                    Taro.navigateTo({
                      url: '/pages/albumManage/tagManage/tag/addContent'
                    })
                  }}
          >
            添加图文
          </Button>
        </View>
      </View>
      <Button className="submit-btn" onClick={async () => {
        if (!tagName) {
          Taro.showToast({ title: '请输入标签名称', icon: 'none' });
          return;
        }
        try {
          const ids = articles.map(item => item.id);
          let data = {
            name: tagName,
            userId: userInfo.id,
            dynamicsId: ids.join(','),
            type: 1,
            coverImage: coverImg
          }
          let res: any = await createTag(data);
          Taro.setStorageSync('selectedArticles', []);
          if (res.code == 0) {
            Taro.setStorageSync('selectedArticles', []);
            Taro.showToast({ title: '创建成功', icon: 'success' });
            setTimeout(() => {
              // Taro.navigateTo({
              //   url: '/pages/albumManage/tagManage/tag/index'
              // });
              Taro.navigateBack({
                delta:1
              });
            }, 2000)
            
          } else {
            Taro.showToast({ title: res.msg, icon: 'none' });
          }
        } catch (e) {
          Taro.showToast({ title: '创建失败', icon: 'none' });
        }
      }}>完成</Button>
    </View>
  );
}
