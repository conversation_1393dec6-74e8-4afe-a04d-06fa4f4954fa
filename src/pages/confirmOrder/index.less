@import "../../utils/css/variables.less";

.confirm-order-page {
  background: #f7f8fa;
  min-height: 100vh;
  padding-bottom: 80px;
}

.co-section {
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 16px;
  min-height: 48px;
  border-bottom: 1px solid #f0f0f0;
}

.co-address {
  margin-top: 8px;
  .co-address-icon {
    width: 24px;
    height: 24px;
    background: url('https://img.alicdn.com/imgextra/i4/6000000000422/O1CN01Qw1QwK1z2v7kRrgkA_!!6000000000422.png') no-repeat center/cover;
    margin-right: 8px;
  }
  .co-address-content {
    flex: 1;
    .co-address-add {
      color: #222;
      font-size: 16px;
    }
  }
  .co-arrow {
    width: 16px;
    height: 16px;
    background: url('data:image/svg+xml;utf8,<svg fill="%23999" height="16" viewBox="0 0 1024 1024" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M384 192l256 320-256 320z"/></svg>') no-repeat center/contain;
  }
}

.co-delivery, .co-custom-sender {
  .co-label {
    color: #222;
    font-size: 15px;
    flex: 1;
  }
  .co-value {
    color: #666;
    font-size: 15px;
    margin-right: 8px;
  }
  .co-arrow {
    width: 16px;
    height: 16px;
    background: url('data:image/svg+xml;utf8,<svg fill="%23999" height="16" viewBox="0 0 1024 1024" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M384 192l256 320-256 320z"/></svg>') no-repeat center/contain;
  }
}

.co-shop-card {
  background: #fff;
  margin-top: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  padding: 0 16px 16px 16px;
}
.co-shop-header {
  display: flex;
  align-items: center;
  padding: 12px 0 8px 0;
}
.co-shop-avatar {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  margin-right: 8px;
}
.co-shop-name {
  font-size: 15px;
  color: #222;
  margin-right: 6px;
}
.co-shop-tag {
  width: 16px;
  height: 16px;
}
.co-product-list {
  margin-bottom: 8px;
}
.co-product-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}
.co-product-img {
  width: 56px;
  height: 56px;
  border-radius: 6px;
  margin-right: 10px;
  background: #f5f5f5;
}
.co-product-info {
  flex: 1;
}
.co-product-title {
  font-size: 14px;
  color: #222;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.co-product-meta-row {
  display: flex;
  align-items: center;
  gap: 8px;
}
.co-product-size {
  font-size: 13px;
  color: #666;
}
.co-product-price {
  font-size: 14px;
  color: #f53f3f;
}
.co-product-x {
  font-size: 13px;
  color: #999;
}
.co-divider {
  margin: 8px 0;
}
.co-summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}
.co-summary-real {
  font-weight: bold;
  color: #222;
  .co-summary-real-value {
    color: #f53f3f;
    font-size: 16px;
  }
}
.co-remark {
  margin-top: 8px;
  width: 100%;
  background: #f7f8fa;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  padding: 8px;
}
.co-bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.03);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 56px;
  z-index: 10;
}
.co-bottom-count {
  font-size: 15px;
  color: #222;
}
.co-bottom-price {
  color: #f53f3f;
  font-size: 16px;
  font-weight: bold;
}
.co-bottom-btn {
  margin-left: 12px;
  min-width: 140px;
  height: 40px;
  font-size: 16px;
  border-radius: 20px;
}
