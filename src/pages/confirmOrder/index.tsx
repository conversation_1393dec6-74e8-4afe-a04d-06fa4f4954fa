import React, { useState } from 'react';
import { View, Text, Input } from '@tarojs/components';
import { Button, Image, Switch, Stepper, Divider } from '@arco-design/mobile-react';
import YkNavBar from '@/components/ykNavBar';
import './index.less';

const shop = {
  shopName: '尚品百货',
  shopTag: true,
  shopAvatar: 'https://img.alicdn.com/imgextra/i3/2200704180016/O1CN01Qw1QwK1z2v7kRrgkA_!!2200704180016.jpg',
  products: [
    {
      id: 1,
      img: 'https://img.alicdn.com/imgextra/i3/2200704180016/O1CN01Qw1QwK1z2v7kRrgkA_!!2200704180016.jpg',
      name: 'Nk SB Dunk Low LV联名-高端定制 低帮定制 低帮休闲鞋大厂 纯原品质细节完美出自新款2025... ',
      price: 230,
      size: '36.5',
      count: 1,
    },
    {
      id: 2,
      img: 'https://img.alicdn.com/imgextra/i3/2200704180016/O1CN01Qw1QwK1z2v7kRrgkA_!!2200704180016.jpg',
      name: 'Nk SB Dunk Low LV联名-高端定制 低帮定制 低帮休闲鞋大厂 纯原品质细节完美出自新款2025... ',
      price: 230,
      size: '38',
      count: 1,
    },
  ],
};

const ConfirmOrder = () => {
  const [productList, setProductList] = useState(shop.products);
  const [remark, setRemark] = useState('');
  const [customSender, setCustomSender] = useState(false);

  const handleCount = (idx, type) => {
    setProductList(prev => {
      const newList = prev.map((item, i) => {
        if (i === idx) {
          let count = item.count;
          if (type === 'add') count += 1;
          if (type === 'minus' && count > 1) count -= 1;
          return { ...item, count };
        }
        return item;
      });
      return newList;
    });
  };

  const totalCount = productList.reduce((sum, p) => sum + p.count, 0);
  const totalPrice = productList.reduce((sum, p) => sum + p.count * p.price, 0);
  const express = 10;
  const realPrice = totalPrice + express;

  return (
    <View className="confirm-order-page">
      <YkNavBar title="确认订单" />
      <View className="co-section co-address">
        <View className="co-address-icon" />
        <View className="co-address-content">
          <Text className="co-address-add">添加收货地址</Text>
        </View>
        <View className="co-arrow" />
      </View>
      <View className="co-section co-delivery">
        <Text className="co-label">配送方式</Text>
        <Text className="co-value">快递</Text>
        <View className="co-arrow" />
      </View>
      <View className="co-section co-custom-sender">
        <Text className="co-label">自定义发件人</Text>
        <Switch checked={customSender} onChange={setCustomSender} />
      </View>
      <View className="co-shop-card">
        <View className="co-shop-header">
          <Image className="co-shop-avatar" src={shop.shopAvatar} />
          <Text className="co-shop-name">{shop.shopName}</Text>
          <Image className="co-shop-tag" src="https://img.alicdn.com/imgextra/i1/6000000000422/O1CN01Qw1QwK1z2v7kRrgkA_!!6000000000422.png" />
        </View>
        <View className="co-product-list">
          {productList.map((prod, idx) => (
            <View className="co-product-item" key={prod.id}>
              <Image className="co-product-img" src={prod.img} />
              <View className="co-product-info">
                <Text className="co-product-title">{prod.name}</Text>
                <View className="co-product-meta-row">
                  <Text className="co-product-size">{prod.size}</Text>
                  <Text className="co-product-price">￥{prod.price}</Text>
                  <Text className="co-product-x">x</Text>
                  <Stepper value={prod.count} step={1} onChange={val => handleCount(idx, val > prod.count ? 'add' : 'minus')} />
                </View>
              </View>
            </View>
          ))}
        </View>
        <Divider className="co-divider" />
        <View className="co-summary-row">
          <Text className="co-summary-label">商品数量</Text>
          <Text className="co-summary-value">{totalCount}</Text>
        </View>
        <View className="co-summary-row">
          <Text className="co-summary-label">商品总金额</Text>
          <Text className="co-summary-value">￥{totalPrice.toFixed(2)}</Text>
        </View>
        <View className="co-summary-row">
          <Text className="co-summary-label">运费</Text>
          <Text className="co-summary-value">￥{express.toFixed(2)}</Text>
        </View>
        <View className="co-summary-row co-summary-real">
          <Text className="co-summary-label">实付金额</Text>
          <Text className="co-summary-value co-summary-real-value">￥{realPrice.toFixed(2)}</Text>
        </View>
        <Input className="co-remark" placeholder="留言(选填)" value={remark} onInput={e => setRemark(e.detail.value)} />
      </View>
      <View className="co-bottom-bar">
        <Text className="co-bottom-count">共{totalCount}件 | <Text className="co-bottom-price">￥{totalPrice}</Text></Text>
        <Button className="co-bottom-btn" type="primary">立即支付 ￥{realPrice}</Button>
      </View>
    </View>
  );
};

export default ConfirmOrder;
