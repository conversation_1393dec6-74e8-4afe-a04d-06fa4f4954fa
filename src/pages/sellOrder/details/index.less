@import "@arco-design/mobile-react/style/mixin.less";

.order-details-box {
  width: 100%;
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 80px;

  .order-status-bar {
    background: #2468f2;
    color: #fff;
    font-size: 16px;
    padding: 12px 0 12px 16px;
    font-weight: bold;
  }

  .order-info-card,
  .order-user-card,
  .order-goods-card {
    background: #fff;
    border-radius: 12px;
    margin: 16px;
    box-shadow: 0 2px 8px rgba(36,104,242,0.03);
    padding: 8px 0 8px 0;
  }

  .order-user-type {
    background: #fff;
    border-radius: 12px;
    margin: 16px;
    box-shadow: 0 2px 8px rgba(36,104,242,0.03);
    padding: 8px 0 8px 0;
  }

  .order-user-row {
    display: flex;
    align-items: center;
    padding: 16px 0 0 16px;
  }
  .order-user-name {
    font-size: 15px;
    font-weight: bold;
    color: @font-color;
  }
  .order-user-phone {
    font-size: 12px;
    color: #888;
    margin-top: 2px;
  }

  .order-remark {
    background: #fff;
    border-radius: 12px;
    margin: 0 16px 16px 16px;
    box-shadow: 0 2px 8px rgba(36,104,242,0.03);
  }

  .order-img-list {
    display: flex;
    flex-wrap: wrap;
    padding: 12px 16px;
    background: #fff;
    border-radius: 12px;
    margin: 0 16px 16px 16px;
  }

  .order-goods-title {
    display: flex;
    align-items: center;
    font-size: 15px;
    font-weight: bold;
    padding: 16px 0 0 16px;
  }
  .order-goods-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 16px 0 16px;
  }
  .order-goods-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .order-goods-name {
    font-size: 14px;
    font-weight: bold;
    color: @font-color;
    margin-bottom: 6px;
  }
  .order-goods-spec {
    font-size: 12px;
    color: #888;
    margin-bottom: 8px;
  }
  .order-goods-price {
    font-size: 13px;
    color: #e35848;
    font-weight: bold;
  }
  .order-goods-summary {
    font-size: 13px;
    color: #333;
    padding: 0 16px 12px 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 12px 24px;
    .order-goods-actual {
      color: #e35848;
      font-weight: bold;
    }
  }

  .order-footer-bar {
    height: 50px;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    box-shadow: 0 -2px 8px rgba(36,104,242,0.03);
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 12px 8px 12px 8px;
    z-index: 10;
    .order-footer-btn {
      flex: 1;
      margin: 0 4px;
      font-size: 15px;
      border-radius: 8px;
      &.order-footer-btn-pay {
        font-weight: bold;
      }
    }
  }
}




.arco-cell .cell-content .cell-text{
  //  text-align: left;
  //  color: #000;
}

.cell .cell-label{

    margin-right: 10px;
    font-family: PingFang SC;
    font-size: 13px;
    font-weight: normal;
    line-height: 140%;
    text-align: justify; /* 浏览器可能不支持 */
    letter-spacing: normal;
    color: #86909C
 
}

.cell .cell-title
{
    width: 60px;
}




.demo-cell-avatar-label {
    display: flex;
    align-items: center;
    .arco-avatar {
        .rem(width, 32);
        .rem(height, 32);
        .rem-with-rtl(margin-right, 8);
    }
}
.demo-cell-avatar {
    .cell-text {
        font-size: 0;
    }
    .arco-avatar {
        .rem(width, 24);
        .rem(height, 24);
        display: inline-block;
    }
}



.demo-cell-info {
    text-align: right;
    .info {
        .use-var(color, font-color);
        .rem(font-size, 16);
    }
    .sub-info {
        .use-var(color, cell-desc-color);
        .rem(font-size, 14);
    }
}

.info
{
    font-size: 12px;
    line-height:18px;
    padding-bottom:16;
    width: 180px;
    margin-right:  35px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;    /* 限制显示行数 */
    overflow: hidden;
    max-height: 6.0em;        /* 可选，设置最大高度 */
}

.order-btn {
  flex: 1;
  min-width: 0;
  border-radius: 0;
  font-size: 16px;
  padding: 0;
  height: 44px;
  background: #fff;
  border: none;
  color: #222;
  border-right: 1px solid #f0f0f0;
  box-shadow: none;
  margin: 0;
}
.order-btn:last-child {
  border-right: none;
}
.order-btn.primary {
  color: #86909C;
  font-weight: bold;
  background: #fff;
  border: none;
}


.order-btn.btn-active {
  color: #165dff !important;
  font-weight: bold;
}
.empty-order {
  text-align: center;
  color: #bbb;
  font-size: 15px;
  padding: 48px 0 32px 0;
}

.order-user-type .cell-title {
  margin-right: 20px;
}


.order-info {
  color: #000;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}