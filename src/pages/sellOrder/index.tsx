import React, { useState, useEffect } from "react";

import { View, Image, Text } from "@tarojs/components";
import { Button, Cell, SearchBar } from "@arco-design/mobile-react";
import { getOrderList } from "@/utils/api/common/common_user";
import YkNavBar from "@/components/ykNavBar";
import Taro from "@tarojs/taro";
import "./index.less";
import { Style } from "@tarojs/runtime";

const orderList1 = [
  {
    shop: "尚品百货",
    status: "待付款",
    goods: [
      {
        img: "https://img.alicdn.com/imgextra/i1/2200708363777/O1CN01Qw1k8B1wJv2lYQ6gA_!!0-item_pic.jpg",
        name: "Nk SB Dunk Low LV联名-高端定制 低帮休闲鞋大厂纯原品质细节完美纯白新款...",
        price: 80,
        count: 1,
      },
      {
        img: "https://img.alicdn.com/imgextra/i2/2200708363777/O1CN01Qw1k8B1wJv2lYQ6gA_!!0-item_pic.jpg",
        name: "80P耐克NikeAirForce1Low纯白新款...",
        price: 110,
        count: 1,
      },
    ],
    express: "快递",
    time: "10-11 16:47",
    total: 198,
    freight: 8,
    statusText: "待付款",
    tab: 1, // 待付款
    buyerRemark: "备注留言内容备注留言内容备注留言内容备注留言内容备注留言内容.备注留言内容备注留言内容备注留言内容备注留言内容备注留言内容...备注留言内容备注留言内容备注留言内容备注留言内容备注留言内容.....",
    myRemark: "我的备注内容我的备注内容我的备注内容我的备注内容...",
  },
  {
    shop: "潮流优选",
    status: "已完成",
    goods: [
      {
        img: "https://img.alicdn.com/imgextra/i3/2200708363777/O1CN01Qw1k8B1wJv2lYQ6gA_!!0-item_pic.jpg",
        name: "Adidas Originals Superstar 经典贝壳头...",
        price: 599,
        count: 1,
      },
      {
        img: "https://img.alicdn.com/imgextra/i4/2200708363777/O1CN01Qw1k8B1wJv2lYQ6gA_!!0-item_pic.jpg",
        name: "李宁韦德之道全新配色篮球鞋...",
        price: 399,
        count: 2,
      },
    ],
    express: "顺丰",
    time: "09-28 10:20",
    total: 599,
    freight: 0,
    statusText: "已完成",
    tab: 4, // 已完成
  },
  {
    shop: "运动专营店",
    status: "已发货",
    goods: [
      {
        img: "https://img.alicdn.com/imgextra/i4/2200708363777/O1CN01Qw1k8B1wJv2lYQ6gA_!!0-item_pic.jpg",
        name: "李宁韦德之道全新配色篮球鞋...",
        price: 399,
        count: 2,
      },

    ],
    express: "EMS",
    time: "10-10 09:00",
    total: 798,
    freight: 12,
    statusText: "已发货",
    tab: 3, // 已发货
  },
  {
    shop: "尚品百货",
    status: "已付款",
    goods: [
      {
        img: "https://img.alicdn.com/imgextra/i5/2200708363777/O1CN01Qw1k8B1wJv2lYQ6gA_!!0-item_pic.jpg",
        name: "Nike Air Max 270 React 运动鞋...",
        price: 899,
        count: 1,
      },
    ],
    express: "圆通",
    time: "10-09 13:30",
    total: 899,
    freight: 0,
    statusText: "已付款",
    tab: 2, // 已付款
  },
  {
    shop: "尚品百货",
    status: "退款",
    goods: [
      {
        img: "https://img.alicdn.com/imgextra/i6/2200708363777/O1CN01Qw1k8B1wJv2lYQ6gA_!!0-item_pic.jpg",
        name: "Nike Dunk Low 粉色限定...",
        price: 799,
        count: 1,
      },
      {
        img: "https://img.alicdn.com/imgextra/i6/2200708363777/O1CN01Qw1k8B1wJv2lYQ6gA_!!0-item_pic.jpg",
        name: "Nike Dunk Low 粉色限定...",
        price: 799,
        count: 1,
      },
    ],
    express: "中通",
    time: "10-12 11:11",
    total: 799,
    freight: 0,
    statusText: "退款",
    tab: 5, // 退款
  },

];

const tabList = ["全部", "待付款", "待发货", "已发货", "已完成", "退款"];
const buttonList = [
    ['转发','编辑'],
    ['转发','编辑'],
    ['转发','退款','发货','全部发货'],
    ['转发','退款','部分发货','全部发货'],
    ['转发','退款'],
    ['转发','退款'],
];



export default function sellOrderPage() {
  const [activeTab, setActiveTab] = useState(1); // 默认高亮"待付款"
  const [activeBtn, setActiveBtn] = useState(-1); // -1表示无高亮按钮
  const [orderList, setOrderList]= useState<any[]>([]); 
  const userInfo = Taro.getStorageSync('userInfo');



  const getOrderListData = async () => {
    const data = {
      userId:userInfo.id,
      pageNo: 1,
      pageSize: 10,
    }
    const orderListData: any = await getOrderList(data);
    if (orderListData.code == 0) {
        if (orderListData.data.list.length > 0) {
        orderListData.data.list.forEach(item => {
          item.tab = '1'; // 这里可以设置你想要的默认值
        });
        setOrderList(orderListData.data.list);
        console.log(orderListData.data.list,'-----------');    
      } else {
        
      }
    }
  }
  
  useEffect(() => {
    getOrderListData();
  }, []); //



  return (
    <View className="order-page">
      <YkNavBar title="我卖的" />
      <View className="order-tabs">
        {tabList.map((tab, idx) => (
          <View
            className={`tab${activeTab === idx ? " highlight" : ""}`}
            key={tab}
            onClick={() => setActiveTab(idx)}
          >
            {tab} 
          </View>
        ))}
      </View>
      <View className="searchLine">
        <SearchBar placeholder="客户名/商品名/货号/收件人信息" clearable={false} actionButton={<span className="demo-search-btn">筛选</span>} />
        {/* <SearchBar shape="round" placeholder="搜索" clearable={false}  /> */}

      </View>
      <View className="order-list">
        {(activeTab === 0
          ? orderList1
          : orderList1.filter((order) => order.tab === activeTab)
        ).map((order, idx) => (
          <View className="order-card" key={idx}>
            <Cell label={<Text className="shop-name">{order.shop}</Text>} 
                  text={<Text className='order-status'>{order.status}</Text>} 
                  onClick={() => Taro.navigateTo({url: '/pages/sellOrder/details/index?status='+activeTab})}
                  showArrow  
            />
            
            <View className="goods-list">
              {order.goods.map((g, i) => (
                <View  className="goods-item" key={i}>
                  <Image className="goods-img" src={g.img} mode="aspectFill" />
                  <View className="goods-info" style={activeTab === 5 ? { height: '65px' } : {}}>
                    <Text className="goods-title">{g.name}</Text>
                    <View className="goods-meta">
                      <Text className="goods-price">￥{g.price}</Text>
                      <Text className="goods-count">x{g.count}</Text>
                    </View>
                    {activeTab === 5 && (                    
                      <View>
                        <Text className="goods-title" >L</Text>
                        <View className="goods-meta">
                          <Text className="goods-price" style={{marginTop: '3px'}} >退款x{g.count}</Text>
                        </View>
                        <Text className="goods-title" >X</Text>
                        <View className="goods-meta" >
                          <Text className="goods-price" style={{marginTop: '3px'}} >退款x{g.count}</Text>
                        </View>
                      </View> 
                    )}

                    {activeTab === 4 && (                    
                      <View>
                        <Text className="goods-title" >L</Text>
                        <View className="goods-meta">
                          <Text className="goods-price" style={{marginTop: '3px'}} >x{g.count}(已发货1)</Text>
                        </View>
                        <Text className="goods-title" >X</Text>
                        <View className="goods-meta" >
                          <Text className="goods-price" style={{marginTop: '3px'}} >x{g.count}(已发货2)</Text>
                        </View>
                      </View> 
                    )}

                    {activeTab === 3 && (                    
                      <View>
                        <Text className="goods-title" >L</Text>
                        <View className="goods-meta">
                          <Text className="goods-price" style={{marginTop: '3px'}} >x{g.count}(未发货0)</Text>
                        </View>
                        <Text className="goods-title" >X</Text>
                        <View className="goods-meta" >
                          <Text className="goods-price" style={{marginTop: '3px'}} >x{g.count}(未发货0)</Text>
                        </View>
                      </View> 
                    )}

                    {activeTab === 2 && (          
                      <View style={{marginTop: '10px'}}>
                        <Text className="goods-title" >默认</Text>
                        <View className="goods-meta">
                          <Text  style={{marginTop: '3px'}} >x2</Text>
                        </View>
                      </View> 
                    )}

                  </View>

                  
                </View>
                
              ))}
            </View>
            {/* 买家留言和我的备注 */}

            {activeTab != 5 && activeTab != 4 && activeTab != 3 && activeTab != 2  && (                    
                      <View className="remark-section">
                      <View className="remark-row">
                        <Text className="remark-label">买家留言：</Text>
                        <View className="remark-label-block">
                          <Text className="remark-img-btn">🖼 查看图片</Text>
                          <View>
                            <Text className="remark-content remark-content-block">{order.buyerRemark || '无'}</Text>
                          </View>
                        </View>
                      </View>
                      
                      <View className="remark-row">
                        <Text className="remark-label">我的备注：</Text>
                        <View className="remark-label-block">
                          <Text className="remark-img-btn">🖼 查看图片</Text>
                          <View>
                            <Text className="remark-content remark-content-block">{order.myRemark || '无'}</Text>
                          </View>
                        </View>
                      </View>
        
                    </View>
             )}
            


            <View className="order-summary">
              <Text className="express">{order.express}</Text>
              <Text className="order-time"></Text>
              <Text className="order-total">
                <View className="freight">{order.time}</View>
                共{order.goods.length}件
                <Text className="total-price">￥{order.total}.00</Text>(含运费￥{order.freight})
              </Text>
            </View>
  
            <View className="order-actions">
              {buttonList[activeTab].map((btn, i) => (
                <Button
                  className={`order-btn primary${activeBtn === i ? " btn-active" : ""}`}
                  size="small"
                  key={btn}
                  onClick={() => setActiveBtn(i)}
                >
                  {btn}
                </Button>
              ))}
      
            </View>
          </View>
        ))}
        {(activeTab === 0
          ? orderList1.length === 0
          : orderList1.filter((order) => order.tab === activeTab).length === 0
        ) && (
          <View className="empty-order">暂无订单</View>
        )}
      </View>
    </View>
  );
}
