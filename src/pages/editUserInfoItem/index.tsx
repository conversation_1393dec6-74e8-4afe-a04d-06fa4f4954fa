import { useState, useEffect } from 'react';
import { Input, Button, Textarea } from '@arco-design/mobile-react';
import './index.less';
import { View, Text } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import PageSwipeBack from '@/components/PageSwipeBack';
import { editUser } from '@/utils/api/common/common_user';
import YkNavBar from "@/components/ykNavBar/index";

export default function EditItem() {
    const router = useRouter();
    const [content, setContent] = useState<string>('');
    const [hint, setHint] = useState<string>('');
    const [placeHolder, setPlaceHolder] = useState<string>('');
    const [maxLength, setMaxLength] = useState<number>(32);
    const [type, setType] = useState<number | null>(null);
    const [isFocused, setIsFocused] = useState<boolean>(false); // 焦点状态
    useEffect(() => {
        if (router?.params?.type) {
            setType(Number(router.params.type));
        }
        if (router?.params?.content) {
            setContent(decodeURIComponent(router.params.content));
        }
    }, [router]);

    // 动态设置标题等
    useEffect(() => {
        switch (type) {
            case 1:
                setPlaceHolder('请输入昵称');
                setHint('请输入正确的昵称');
                setMaxLength(16);
                break;
            case 2:
                setPlaceHolder('请输入手机号');
                setHint('请输入正确的手机号');
                setMaxLength(11);
                break;
            case 3:
                setPlaceHolder('请输入个人微信号');
                setHint('请输入正确的微信号');
                setMaxLength(32);
                break;
            case 4:
                setPlaceHolder('请输入个性签名~');
                setHint('');
                setMaxLength(200);
                break;
            default:
                break;
        }
    }, [type]);

    const handleSave = () => {
        let formData: { nickname?: string; mobile?: string; wechatNumber?: string; personalProfile?: string } = {};

        switch (type) {
            case 1:
                formData = { nickname: content };
                break;
            case 2:
                formData = { mobile: content };
                break;
            case 3:
                formData = { wechatNumber: content };
                break;
            case 4:
                formData = { personalProfile: content };
                break;
            default:
                console.warn("Invalid type");
                return;
        }

        // 调用 editUser 接口
        editUser(formData)
            .then((res) => {
                console.log(JSON.stringify(res))
                if (res.code === 0) {
                    // 成功处理
                    Taro.showToast({
                        title: '保存成功',
                        icon: 'success',
                    });
                    Taro.eventCenter.trigger("refreshUserInfo"); // 通知父页面刷新
                    Taro.navigateBack();
                } else {
                    // 错误处理
                    Taro.showToast({
                        title: '保存失败',
                        icon: 'success',
                    });
                }
            })
            .catch((err) => {
                Taro.showToast({
                    title: '保存失败',
                    icon: 'success',
                });
            });

    };

    return (
        <PageSwipeBack leftDrag={true} >
            <View className='edit'>
                 <YkNavBar title={type === 1 ? '昵称' : type === 2 ? '手机号' : type === 3 ? '微信号' : '个性签名'} />
                <View className='input-container'>
                    {type === 4 ? (
                        <Textarea
                            border='none'
                            className={`custom-textarea ${isFocused && !content ? 'focused' : ''}`}
                            value={content}
                            onChange={(_, value) => setContent(value)}
                            placeholder={placeHolder}
                            maxLength={maxLength}
                        />
                    ) : (
                        <Input
                            border='none'
                            className={`custom-input ${isFocused && !content ? 'focused' : ''}`}
                            value={content}
                            onFocus={() => setIsFocused(true)}
                            onBlur={() => setIsFocused(false)}
                            onChange={(_, value) => setContent(value)}
                            placeholder={placeHolder}
                            maxLength={maxLength}
                        />
                    )}
                    {type !== 4 && <Text className={`hint ${isFocused && !content ? 'visible' : ''}`}>{hint}</Text>}
                </View>
                <View className="footer-container">
                    <Button
                        className={`footer-button ${!content ? 'disabled' : ''}`}
                        onClick={handleSave}
                        disabled={!content}
                    >
                        保存
                    </Button>
                </View>
            </View>
        </PageSwipeBack>
    );
};

