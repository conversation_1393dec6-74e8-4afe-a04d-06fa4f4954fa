import { View, Text } from "@tarojs/components";
import "./index.less";
import {
  Cell,
  Dialog
} from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

export default function Index() {

  // 前往绑定手机号页面
  const getInPageBindMobilePhone = () => {
    Taro.navigateTo({
      url: "/pages/bindMobilePhone/index",
    });
  };

  // 前往重置密码页面
  const getInLoginedResetPassword = () => {
    Taro.navigateTo({
      url: "/pages/loginedResetPassword/index",
    });
  };

  // 退出登录
  const exitLogin = () => {
    let _window:any = window
    _window.modalInstance = Dialog.confirm({
      title: '提示',
      children: '确定要退出登录吗？',
      platform: 'ios',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        // 清除用户信息
        Taro.removeStorageSync("userInfo");
        // 关闭所有页面，跳转到登录页面
        Taro.reLaunch({
          url: "/pages/login/index",
        });
      },
  });
  };

  return (
    <View className="setPageContent">
      <YkNavBar title="设置" />

      <View className="module">
        <View className="module-title">
          <Text className="module-title-text">账号与安全</Text>
        </View>
        <View className="module-content">
          <Cell.Group bordered={false}>
            {/* 绑定手机号 */}
            <Cell
              label="绑定手机号"
              className="module-content-cell"
              text=""
              showArrow
              onClick={getInPageBindMobilePhone}
            ></Cell>
            {/* 重置密码 */}
            <Cell
              label="重置密码"
              className="module-content-cell"
              text=""
              showArrow
              onClick={getInLoginedResetPassword}
            ></Cell>
            {/* 绑定微信 */}
            <Cell
              label="绑定微信"
              className="module-content-cell"
              text=""
              showArrow
            ></Cell>
          </Cell.Group>
        </View>
      </View>

      <View className="module">
        <View className="module-title">
          <Text className="module-title-text">系统设置</Text>
        </View>
        <View className="module-content">
          <Cell.Group bordered={false}>
            {/* 系统权限管理 */}
            <Cell
              label="系统权限管理"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: "/pages/permissionSetting/index",
                });
              }}
            ></Cell>
            {/* 颜色设置 */}
            <Cell
              label="颜色设置"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: "/pages/colorSettings/index",
                });
              }}
            ></Cell>
           
          </Cell.Group>
        </View>
      </View>

      <View className="module">
        <View className="module-title">
          <Text className="module-title-text">其他</Text>
        </View>
        <View className="module-content">
          <Cell.Group bordered={false}>
            {/* 关于我们 */}
            <Cell
              label="关于我们"
              className="module-content-cell"
              text=""
              showArrow
              onClick={()=>{
                Taro.navigateTo({
                  url: "/pages/aboutUs/index",
                });}}
            ></Cell>
            {/* 清理缓存 */}
            <Cell
              label="清理缓存"
              className="module-content-cell"
              text=""
              showArrow
            ></Cell>
            {/* 账号注销 */}
            <Cell
              label="账号注销"
              className="module-content-cell"
              text=""
              showArrow
            ></Cell>
            {/* 个人信息收集清单 */}
            <Cell
              label="个人信息收集清单"
              className="module-content-cell"
              text=""
              showArrow
              onClick={()=>{
                Taro.navigateTo({
                  url: "/pages/PICC/index",
                });}}
            ></Cell>
            {/* 第三方SDK清单 */}
            <Cell
              label="第三方SDK清单"
              className="module-content-cell"
              text=""
              showArrow
            ></Cell>
            {/* 版本更新 */}
            <Cell
              label="版本更新"
              className="module-content-cell"
              text=""
              showArrow
            ></Cell>
          </Cell.Group>
        </View>
      </View>

      <View className="module">
        <View className="moduleBtn">
          <Text className="moduleBtn-text">切换账号</Text>
        </View>

        <View className="moduleBtn" onClick={()=>exitLogin()}>
          <Text className="moduleBtn-text">退出登录</Text>
        </View>
      </View>
    </View>
  );
}
