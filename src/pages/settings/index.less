
@import '@arco-design/mobile-react/style/mixin.less';

[id^="/pages/settings/index?stamp="] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
}

.setPageContent {
  position: relative;
  width: 100%;
}

.module {
  width: 100%;
  box-sizing: border-box;
  padding: 10px;

  &-title {
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;

    &-text {
      font-size: 13px;
      color: #999999;
    }
  }

  &-content {
    width: 100%;
    box-sizing: border-box;
    border-radius: 10px;
    overflow: hidden;
  }
}

.moduleBtn {
  width: 100%;
  box-sizing: border-box;
  border-radius: 10px;
  overflow: hidden;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  margin-bottom: 15px;

  &:active{
    opacity: .8;
  }

  &-text{
    font-size: 16px;
    color: #333333;
    font-weight: bold;
  }
}


.arco-theme-dark{
  .moduleBtn{
    background-color: var(--dark-cell-background-color);
    &-text{
      color: #FFFFFF;
    }
  }
}