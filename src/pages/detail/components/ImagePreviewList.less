@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pages/detail/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });

    .image-preview-list {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #000;
      z-index: 4000;
      display: flex;
      flex-direction: column;
    
      .nav-top {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 4001;
        transition: opacity 0.3s;
    
        .nav-content {
          display: flex;
          align-items: center;
          padding: 16px;
    
          .back-icon {
            width: 15px;
            height: 15px;
          }
    
          .page-indicator {
            flex: 1;
            text-align: center;
            color: #fff;
            font-size: 16px;
            font-weight: bold;
          }
        }
      }
    
          
      .title {
        margin-bottom: 172px;
        // width: 100%;
        margin-top: 20px;
        padding: 0 15px;
        
        text {
          color: #fff;
          font-size: 13px;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }

      .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 16px;
        transition: opacity 0.3s;

    
        .controls {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          // background: #f96527;
    
          .download {
            display: flex;
            align-items: center;
            .icon {
              width: 16px;
              height: 16px;
              margin-right: 9px;
            }
    
            text {
              color: #fff;
              font-size: 12px;
            }
          }
    
          .right-controls {
            display: flex;
            gap: 8px;
    
            .share-btn, .cart-btn {
              padding: 8px 0;
              width: 88px;
              text-align: center;
              border-radius: 22px;
              font-size: 13px;
              color: #fff;
            }
    
            .share-btn {
              background: #6cbe70;
            }
    
            .cart-btn {
              background: #f96527;
            }
          }
        }
      }
    
      .hidden {
        opacity: 0;
        pointer-events: none;
      }
    
      .carousel {
        margin-top: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 375px;
        height: 375px;
        .image {
          width: 375px;
          height: 375px;
        }
      }
    
    } 

}
