import { View, Text } from "@tarojs/components";
import { useState, useEffect } from "react";
import { Toast, Image, Dialog, Carousel } from "@arco-design/mobile-react";
import { navigateToVue } from '@/utils/yk-common';
import Taro from "@tarojs/taro";
import "./ImagePreviewList.less";

interface ImagePreviewListProps {
  show: boolean;
  images: string[];
  initialIndex?: number;
  dynamic?: any;
  onClose?: () => void;
  showCar?: boolean;
  showBottom?: boolean;
  userInfo?: any;
}

export default function ImagePreviewList({
  show,
  images = [],
  initialIndex = 0,
  dynamic = {},
  onClose,
  showCar = true,
  showBottom = true,
  userInfo = {}
}: ImagePreviewListProps) {

  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [showControls, setShowControls] = useState(true);
  const [scale, setScale] = useState(1);

  // 处理图片预览关闭
  const handleClose = () => {
    setCurrentIndex(0);
    onClose?.();
  };

  // 处理下载确认
  const handleDownloadConfirm = () => {
    // 实现下载逻辑
    Toast.success({
      content: dynamic.dynamic_title ? '下载成功,文字已复制' : '下载成功'
    });
    if(dynamic.dynamic_title) {
      Taro.setClipboardData({
        data: dynamic.dynamic_title
      });
    }
  };

  // 处理转发/分享
  const handleShare = () => {
    if(dynamic.user_id !== userInfo.user_id) {
      Taro.setStorageSync('releaseDynamicList', dynamic);
      navigateToVue('/pages/pagesA/releaseDynamic/index?type=4');
    }
  };

  // 处理加入购物车
  const handleAddCart = () => {
    // TODO: 实现加入购物车逻辑
  };

  if (!show) return null;

  return (
    <View className="image-preview-list">
      {/* 顶部导航 */}
      <View className={`nav-top ${showControls ? '' : 'hidden'}`}>
        <View className="nav-content">
          <Image 
            className="back-icon"
            src={require('@/assets/images/common/left_return_white.png')}
            onClick={handleClose}
            bottomOverlap={null}
          />
          <Text className="page-indicator">
            {currentIndex + 1} / {images.length}
          </Text>
        </View>
      </View>

      {/* 图片预览 */}
      {/* <Carousel
        images={images}
        startPosition={currentIndex}
        onClose={handleClose}
        onChange={setCurrentIndex}
        onScale={setScale}
      /> */}
                    <Carousel
                style={{ height: '375px' }}
                autoPlay={false}
                showIndicator={false}
                className="carousel"
                // onChange={(index) => setCurrent(index)}
              >
                {images.map((item, index) => (
                  <Image
                    key={index}
                    src={item}
                    fit="cover"
                    width="100%"
                    height="375px"
                  />
                ))}
              </Carousel>

              {dynamic.dynamic_title && (
            <View className="title">
              <Text>{dynamic.dynamic_title}</Text>
            </View>
          )}

      {/* 底部控制栏 */}
      {showBottom && (
        <View className={`footer ${showControls ? '' : 'hidden'}`}>

          
          <View className="controls">
            <View className="download" onClick={handleDownloadConfirm}>
              <Image 
                className="icon"
                bottomOverlap={null}
                src={require('@/assets/images/common/download_img_icon.png')}
              />
              <Text>下载</Text>
            </View>

            <View className="right-controls">
              {dynamic.price && Number(dynamic.price) > 0 && showCar ? (
                <>
                  <Text className="share-btn" onClick={handleShare}>
                    {userInfo.user_id === dynamic.user_id ? '一键分享' : '一键转发'}
                  </Text>
                  <Text className="cart-btn" onClick={handleAddCart}>
                    加入购物车
                  </Text>
                </>
              ) : (
                <Text className="share-btn" onClick={handleShare}>
                  {userInfo.user_id === dynamic.user_id ? '一键分享' : '一键转发'}
                </Text>
              )}
            </View>
          </View>
        </View>
      )}
    </View>
  );
} 