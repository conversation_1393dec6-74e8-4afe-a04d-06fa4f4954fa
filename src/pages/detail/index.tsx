import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Toast, Image, Dialog, ImagePreview, Carousel, Button, Avatar,ActionSheet, Input, Cell, Stepper, Textarea  } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import { useState, useRef, useEffect } from "react";
import YkNavBar from "@/components/ykNavBar/index";
import ImagePreviewList from './components/ImagePreviewList';
import {  addCart  } from "@/utils/api/common/common_user";



import { 
  dynamicDetails, 
  dynamicBatchDel, 
  dynamicRefresh, 
  updateDynamic, 
  dynamicBatchOperate, 
  favoritesDynamic,
  deleteFavoritesDynamic, 
  getShopHome 
} from '@/utils/api/common/common_user';
import defaultHeadImg from '@/assets/images/common/default_head.png';
// import GoodCar from '@/components/goodCar';
import { navigateToVue } from '@/utils/yk-common';



export default function Detail() {
  // 状态定义
  const [showPage, setShowPage] = useState(false);
  const [dynamic, setDynamic] = useState<any>({id : ''});
  const [current, setCurrent] = useState(0);
  const [swiperList, setSwiperList] = useState([]);
  const [merchantUserInfo, setMerchantUserInfo] = useState({ 
    head_img: '',
    nickname: '',
    today_number: 0,
    total_number: 0
  });
  const [carListNum, setCarListNum] = useState(0);
  const [isCarShow, setIsCarShow] = useState(false);
  const [actionSheetVisible, setActionSheetVisible] = useState(false); // 1. 添加 state 控制显隐
  
  const goodCarRef = useRef(null);
  const userInfo = Taro.getStorageSync('userInfo') || {};
  const dynamicId = Taro.getCurrentInstance().router?.params?.dynamicId;
  const userId = Taro.getCurrentInstance().router?.params?.dynamicUserId;
  const [showImagePreview, setShowImagePreview] = useState(false);

  const [goodsCount, setGoodsCount] = useState(0);
  // 新增：用数组管理每个 Stepper 的数量
  const [stepperValues, setStepperValues] = useState([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);
  const [stepperNames, setStepperNames] = useState([]);
  //const [format, setFormat] = useState([]);
  // 初始化加载
  useLoad(() => {

    setCarListNum(Taro.getStorageSync('goodCarNum') || 0);
    getDynamicDetails();
    getShopHomeInfo();
    console.log('userInfo', userInfo);
  });


  useEffect(() => {
    
  }, ['goodsCount']); // 组件挂载时执行一次

  // 获取动态详情
  const getDynamicDetails = async () => {
    try {
      if (!dynamicId) return;
      const res: any = await dynamicDetails({ id: dynamicId });
      console.log('res', res.data);
      if (res.code === 0) {
        setDynamic(res.data);
        if(res.data.pictures) {
          setSwiperList(res.data.pictures.split(','));
        }
        setTimeout(() => setShowPage(true), 1);
        console.log([ ...res.data.productSpecificationsNames.split(','), ...res.data.productColorNames.split(',')])
      } else {
        Toast.error({ content: res.msg });
      }
    } catch (err) {
      console.error(err);
      Toast.error({ content: '获取详情失败' });
    }
  };

  // 获取商家信息
  const getShopHomeInfo = async () => {
    try {
   
      if (!userId) return;
      const res: any = await getShopHome({ user_id: userId });
      console.log(res, '---------------');
      if (res.code === 0) {
        setMerchantUserInfo(res.data);
    
      }
    } catch (err) {
      console.error(err);
    }
  };

  const addCartData = async () => {
    
    const specificationsNum=dynamic.productSpecificationsNames ? dynamic.productSpecificationsNames.split(',').length : 0;
    const colorNum=dynamic.productColorNames ? dynamic.productColorNames.split(',').length : 0;
    //const stepperValues=new Array(specificationsNum + colorNum).fill(0);

    const format: any[] = []
    // 规格部分
  
    stepperValues.slice(0, specificationsNum).forEach((item, index) => {
        if(item > 0) {
            format.push({
                productSpecificationsId: dynamic.productSpecificationsIds.split(',')[index],
                productColorId: '',
                quantity: item,
                price: dynamic.price,
            })
        }
    });

    // 颜色部分
    stepperValues.slice(specificationsNum, specificationsNum + colorNum).forEach((item, index) => {
        if(item > 0) {
            format.push({
                productSpecificationsId: '',
                productColorId: dynamic.productColorIds.split(',')[index],
                quantity: item,
                price: dynamic.price,
            })
        }
    })

    try {
        const data = {
          userId: userId,
          dynamicsId: dynamicId,
          type:1,
          details:format,
        }
        const res: any = await addCart(data);
        if (res.code === 0) {
          Toast.success({ content: '加入购物车成功' });
          setCarListNum(carListNum + 1);
          Taro.setStorageSync('goodCarNum', carListNum + 1);
        } else {
          Toast.error({ content: res.msg });
        }
    } catch (err) {
        console.error(err);
    }
  };
  

    // 添加前往商家主页方法
    const getIntoMerchantHomePage = () => {
        if (userId == userInfo.userId) {
          navigateToVue('/pages/pagesA/albumManage/albumManage');
        } else {
          navigateToVue(`/pages/pagesA/userDetail/index?user_id=${userId}`);
        }
      };

  // 收藏/取消收藏
  const handleCollect = async () => {
    try {
      let res: any;
      if (dynamic.isCollect == 1) {
        res = await deleteFavoritesDynamic({ dynamicsId: dynamic.id, userId: userInfo.userId });
      } else {
        res = await favoritesDynamic({ dynamicsId: dynamic.id, userId: userInfo.userId });
      }
      if (res.code === 0) {
        const newDynamic = {...dynamic};
        newDynamic.isCollect = dynamic.isCollect === 1 ? 0 : 1;
        setDynamic(newDynamic);
        Toast.success({
          content: dynamic.isCollect === 1 ? '取消成功' : '收藏成功'
        });
      } else {
        Toast.error({ content: res.msg });
      }
    } catch (err) {
      console.error(err);
    }
  };

  // 置顶/取消置顶
  const handleTop = async () => {
    try {
      const res: any = await updateDynamic({
        isTop: dynamic.isTop === 1 ? 0 : 1,
        id: dynamic.id
      });
      if (res.code === 0) {
        const newDynamic = {...dynamic};
        newDynamic.isTop = dynamic.isTop === 1 ? 0 : 1;
        setDynamic(newDynamic);
        Toast.success({
          content: dynamic.isTop === 1 ? '取消置顶成功' : '置顶成功'
        });
      } else {
        Toast.error({ content: res.msg });
      }
    } catch (err) {
      console.error(err);
    }
  };

  // 下架商品
  const handleDown = () => {
    Dialog.confirm({
      title: "温馨提示",
      children: "确定要下架商品吗？",
      onOk: async () => {
        try {
          const res: any = await updateDynamic({
            isListed: 2,
            id: dynamic.id
          });
          if (res.code === 0) {
            Toast.success({ content: '下架成功' });
            setTimeout(() => Taro.navigateBack(), 1500);
          } else {
            Toast.error({ content: res.msg });
          }
        } catch (err) {
          console.error(err);
        }
      }
    });
  };

  // 刷新
  const handleRefresh = async () => {
    try {
      await dynamicRefresh({ dynamic_id: dynamic.id });
      Toast.success({ content: '刷新成功' });
    } catch (err) {
      console.error(err);
    }
  };

  // 删除
  const handleDelete = () => {
    Dialog.confirm({
      title: "温馨提示",
      children: "删除图文后不可恢复，确定删除？",
      onOk: async () => {
        try {
          const res: any = await dynamicBatchDel({ id: dynamic.id });
          if (res.code === 0) {
            Toast.success({ content: '删除成功' });
            setTimeout(() => Taro.navigateBack(), 1500);
          } else {
            Toast.error({ content: res.msg });
          }
        } catch (err) {
          console.error(err);
        }
      }
    });
  };

  // 编辑
  const handleEdit = () => {
    Taro.setStorageSync('releaseDynamicList', dynamic);
    Taro.navigateTo({
      url: `/pages/releaseDynamic/index?type=2`,

    });
  };

  // 加入购物车
  const handleAddCar = () => {
    setStepperValues([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]); // 每次打开前重置
    setActionSheetVisible(true); // 3. 点击按钮时，只更新 state
  };

  // 前往购物车页面
  const goToCarPage = () => {
    if (!userInfo?.userId) {
      Toast.error({ content: '请先登录' });
      setTimeout(() => {

        Taro.navigateTo({ url: '/pages/login/index' });
      }, 1500);
      return;
    }
    navigateToVue("/pages/pagesA/car/index");
  };

  // 先将字符串转数组
  const productColorArr = dynamic.productColorNames ? dynamic.productColorNames.split(',') : [];
  const productSpecificationsArr = dynamic.productSpecificationsNames ? dynamic.productSpecificationsNames.split(',') : [];

  return (
    <View className="detailBox">
      <YkNavBar title={ dynamic.id ? `详情${dynamic.id}` : '详情'} />
      
      {showPage && (
        <>
          {/* 轮播图 */}
          {swiperList.length > 0 && (
            <View className="swiperList">
              <Carousel
                style={{ height: '375px' }}
                onChange={(index) => setCurrent(index)}
              >
                {swiperList.map((item, index) => (
                  <Image
                    key={index}
                    src={item}
                    fit="cover"
                    width="100%"
                    height="375px"
                    onClick={() => setShowImagePreview(true)}
                  />
                ))}
              </Carousel>
              <View className="imgnum">
                <Text>{`${current + 1}/${swiperList.length}`}</Text>
              </View>
            </View>
          )}

          {/* 操作按钮 */}
          {userId == userInfo.userId && (
            <View className="opera">
              <Text onClick={handleEdit} className="opera-text">编辑</Text>
              <Text onClick={handleTop} className="opera-text">
                {dynamic.isTop == '1' ? '取顶' : '置顶'}
              </Text>
              <Text onClick={handleDelete} className="opera-text">删除</Text>
              <Text onClick={handleDown} className="opera-text">下架</Text>
              <Text onClick={handleRefresh} className="opera-text">刷新</Text>
            </View>
          )}

          {/* 商品信息 */}
          <View className="dynamicSign">
            {dynamic.price && Number(dynamic.price) > 0 && (
              <View className="dynamicSign-price">
                <Text className="dynamicSign-price-title">售价</Text>
                <Text className="dynamicSign-price-text">
                  ￥{Number(dynamic.price)}
                </Text>
              </View>
            )}
            <View className="dynamicSign-desc">
              <Text className="dynamicSign-desc-text">
                {dynamic.content?.replace(/(\r\n|\n|\r)/gm, '')}
              </Text>
              <View className="dynamicSign-desc-collect" onClick={handleCollect}>
                <Image
                  className="dynamicSign-desc-collect-img"
                  bottomOverlap={null}
                  src={dynamic.isCollect === 1 ? 
                    require('@/assets/images/common/trend_collect_p.png') :
                    require('@/assets/images/common/trend_collect_n.png')
                  }
                />
                <Text className="dynamicSign-desc-collect-text">
                  {dynamic.isCollect === 1 ? '已收藏' : '收藏'}
                </Text>
              </View>
            </View>
          </View>

          {/* 商家信息 */}
          <View className="myInfo">
            <View className="myInfo-head">
              <Avatar
                className="myInfo-head-image"
                src={merchantUserInfo.head_img || defaultHeadImg}
              />
              <View className="myInfo-head-name">
                <Text>{merchantUserInfo.nickname}</Text>
              </View>
              <View className="myInfo-head-entry" onClick={getIntoMerchantHomePage}>
                <Text>店铺主页</Text>
              </View>
            </View>
            <View className="myInfo-more">
              <View className="myInfo-more-item">
                <Text className="myInfo-more-item-text">上新</Text>
                <Text className="myInfo-more-item-num">
                  {merchantUserInfo.today_number}
                </Text>
              </View>
              <View className="myInfo-more-divider" />
              <View className="myInfo-more-item">
                <Text className="myInfo-more-item-text">总数</Text>
                <Text className="myInfo-more-item-num">
                  {merchantUserInfo.total_number}
                </Text>
              </View>
            </View>
          </View>

          {/* 图文详情 */}
          {(dynamic.pictures !== '' || dynamic.content !== '') && (
            <>
              <View className="dlistImage_title">—— 图文详情 ——</View>
              <View className="dlistImage_desc">
                <Text selectable>{dynamic.dynamic_title}</Text>
              </View>
              {dynamic.pictures !== '' && swiperList.map((item, index) => (
                <View key={index}>
                  <Image 
                    className="dlistImage"
                    src={item}
                    // mode="widthFix"
                  />
                </View>
              ))}
            </>
          )}

          {/* 底部操作栏 */}
          {!isCarShow && (
            <View className="footer">
              <Button 
                className="footer-share"
                onClick={() => {
                  if(userId == userInfo.userId) {
                    //分享

                  } else {
                    //转存
                    Taro.setStorageSync('releaseDynamicList', dynamic);
                    Taro.navigateTo({
                      url: `/pages/releaseDynamic/index?type=4`
                    });
                  }
                }}
              >
                {userId == userInfo.userId ? '一键分享' : '一键转存'}
              </Button>
              {dynamic.price && Number(dynamic.price) > 0 && (
                <View className="footer-car" 
                  onClick={() => {Taro.navigateTo({ url: '/pages/cart/index' });}}
                >
                  <Image
                    className="footer-car-img"
                    bottomOverlap={null}
                    src={require('@/assets/images/common/good_car_icon.png')}
                  />
                </View>
              )}
            </View>
          )}

          {/* 购物车入口 */}
          <View className="car" onClick={handleAddCar}>
            <Image
              className="car-img"
              bottomOverlap={null}
              src={require('@/assets/images/common/car_icon.png')}
            />
            {carListNum > 0 && (
              <Text className="car-text">{carListNum}</Text>
            )}
          </View>

          {/* 购物车组件 */}
          {/* <GoodCar ref={goodCarRef} /> */}
        </>
      )}
      {/* 2. 使用声明式的 ActionSheet 组件 */}
      <ActionSheet
        className='demo-action-sheet'
        visible={actionSheetVisible}
        onClose={() => setActionSheetVisible(false)}
        close={() => setActionSheetVisible(false)}
        maskClosable
        subTitle={(
          <View className="move-item-my" >
            <Image radius={10} className="move-img" src='https://minio.npm.fjpipixia.com/album/20250627/yf1_1750994061897.jpg' />
            <View className="move-info-my-s">
                <Text className="move-title-text-my" >哥哥黑哥哥黑椒鸡块哥哥黑椒鸡哥哥黑椒鸡块块椒鸡哥哥黑椒鸡块块</Text>
                <Text className="move-price">¥23</Text>
            </View>                                   
          </View>
        )}
        items={[
          { 
            content: (
              <Cell.Group bordered={false}>
                {/* 规格遍历 */}
                {productSpecificationsArr.map((item, idx) => (
                  <Cell label={item} style={{height:'40px'}} key={item + idx}>
                    <Stepper
                      style={{marginLeft:'150px'}}
                      min={0}
                      max={100}
                      value={stepperValues[idx]}
                      onChange={val => {
                        if (val !== null) {
                          const newValues = [...stepperValues];
                          newValues[idx] = val - 1;
                          setStepperValues(newValues);
                        }
                      }}
                    />
                  </Cell>
                ))}
                {/* 颜色遍历 */}
                {productColorArr.map((item, idx) => (
                  <Cell label={item} style={{height:'40px'}} key={item + idx}>
                    <Stepper
                      style={{marginLeft:'150px'}}
                      min={0}
                      max={100}
                      value={stepperValues[idx + productSpecificationsArr.length]}
                      onChange={val => {
                        if (val !== null) {
                          const newValues = [...stepperValues];
                          newValues[idx + productSpecificationsArr.length] = val - 1;

                          setStepperValues(newValues);

                        }
                      }}
                    />
                  </Cell>
                ))}
              </Cell.Group>
            ), 
            style: {height:'100%'},
            onClick:() => true  
          },
          { 
            content: (
              <Textarea
                onErrStatusChange={hasError => console.log('hasError', hasError)}
                autosize
                placeholder="可备注颜色，尺码..."
                border="none"
              />
            ), 
            style: {height:'100%'},
            onClick:() => true  
          },
          { 
            content: (
              <View style={{width:'100%',padding:'0 15px 15px 0'}}>
                <View style={{display:'flex',justifyContent:'flex-end',alignItems:'center',marginBottom: '12px'}}>
                  {/* 统计总件数 */}
                  <Text style={{fontSize:16,color:'#222',fontWeight:500}}>共<Text style={{color:'#e35848'}}>{stepperValues.reduce((a, b) => a + b, 0)}</Text>件 <Text style={{color:'#e35848'}}>￥{stepperValues.reduce((a, b) => a + b, 0) * dynamic.price}</Text></Text>
                </View>
                <View style={{display:'flex',gap:'16px',padding:'0 16px 16px 16px',marginTop: '30px'}}>
                  <Button style={{flex:1,height:44,background:'#f7f8fa',color:'#bcbcbc',borderRadius:8,border:'none',fontSize:14}} 
                  onClick={() => {addCartData()}}
                  >加入购物车</Button>
                  <Button style={{flex:1,height:44,background:'#93bfff',color:'#fff',borderRadius:8,border:'none',fontSize:14}} 
                  onClick={() => {Taro.navigateTo({ url: '/pages/cart/index' });}}
                  >立即购买</Button>
                </View>
              </View>
            ), 
            style: {height:120,marginTop:-30},
          }
        ]}
      />
      <ImagePreviewList
  show={showImagePreview}
  images={swiperList}
  initialIndex={current}
  dynamic={dynamic}
  onClose={() => setShowImagePreview(false)}
  showCar={true}
  showBottom={true}
  userInfo={userInfo}
/>
    </View>
  );
}
