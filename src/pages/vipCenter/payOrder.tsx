import { View, Text } from '@tarojs/components';
import { useEffect, useState } from 'react';
import {
  PullRefresh,
  LoadMore,
  Toast,
  Button,
} from '@arco-design/mobile-react';
import PageSwipeBack from '@/components/PageSwipeBack';
import YkNavBar from '@/components/ykNavBar';
import YkNoticeBar from '@/components/YkNoticeBar';
import { getVipOrderList } from '@/utils/api/common/common_user';
import { DebugPanel } from '@/pages/debug';
import { IconLoadEmpty } from '@/components/YkIcons';
import Taro from '@tarojs/taro';
import {
  VipType,
  VipPackage,
  PayStatus,
  VipOrderRecordForUI,
  VipOrderStatus,
  PaginationInfo,
  GetVipOrderListRequest,
} from './types';
import {
  isDebugMode,
  getDebugScenario,
  debugLog,
  vipCenterDebugger,
  generateMockOrderData,
  mockApiResponse,
  formatTime,
  transformOrdersForUI,
} from './utils';
import './payOrder.less';

const PayOrder = () => {
  const [loading, setLoading] = useState(false);
  const [orderList, setOrderList] = useState<VipOrderRecordForUI[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 10,
    hasMore: true,
    isLoading: false,
  });

  const fetchOrderList = async (isRefresh = false, forceScenario?: string) => {
    const { isLoading, hasMore } = pagination;
    if (isLoading && !isRefresh) return;
    if (!hasMore && !isRefresh) return;

    const currentPage = isRefresh ? 1 : pagination.page;
    setPagination(prev => ({ ...prev, isLoading: true }));
    if (isRefresh) {
      setLoading(true);
    }

    try {
      // 获取本地用户信息
      const localUserInfo = Taro.getStorageSync("userInfo");
      if (!localUserInfo || !localUserInfo.id) {
        debugLog('[API] User info not found, skip fetching order list');
        return;
      }

      // MOCK DATA LOGIC
      if (isDebugMode()) {
        // 使用强制场景或当前URL场景
        const scenario = forceScenario || getDebugScenario();
        debugLog(`[MOCK] Active scenario: ${scenario}${forceScenario ? ' (forced)' : ' (from URL)'}`);
        const mockOrders = generateMockOrderData(scenario);
        const response = await mockApiResponse(mockOrders, scenario);
        const rawOrders = Array.isArray(response.data) ? response.data : [];
        const newOrders = transformOrdersForUI(rawOrders);

        debugLog(`[MOCK] Generated ${rawOrders.length} raw orders, transformed to ${newOrders.length} UI orders`);

        if (isRefresh) {
          setOrderList(newOrders);
          debugLog(`[MOCK] Refreshed order list with ${newOrders.length} orders`);
        } else {
          setOrderList(prev => Array.isArray(prev) ? [...prev, ...newOrders] : newOrders);
          debugLog(`[MOCK] Appended ${newOrders.length} orders to existing list`);
        }

        // 空订单场景的特殊处理
        if (scenario === 'empty_orders') {
          setPagination(prev => ({
            ...prev,
            page: 1,
            hasMore: false, // 空订单场景没有更多数据
          }));
          debugLog(`[MOCK] Empty orders scenario - no more data available`);
        } else {
          const newHasMore = newOrders.length >= pagination.limit;
          setPagination(prev => ({
            ...prev,
            page: newHasMore ? currentPage + 1 : currentPage,
            hasMore: newHasMore,
          }));
          debugLog(`[MOCK] Pagination updated - hasMore: ${newHasMore}, page: ${newHasMore ? currentPage + 1 : currentPage}`);
        }
        return;
      }

      // API CALL
      const requestData: GetVipOrderListRequest = {
        pageNo: currentPage,
        pageSize: pagination.limit,
        userId: localUserInfo.id,
        payStatus: PayStatus.PAIDED // 已支付订单
      };
      const response = await getVipOrderList(requestData);
      if (response.code === 0) {
        // 确保 response.data 是分页结构
        const responseData = response.data || {};
        const rawOrders = Array.isArray(responseData) ? responseData : (responseData.list || []);
        const newOrders = transformOrdersForUI(rawOrders);

        if (isRefresh) {
          setOrderList(newOrders);
        } else {
          setOrderList(prev => Array.isArray(prev) ? [...prev, ...newOrders] : newOrders);
        }
        const newHasMore = newOrders.length >= pagination.limit;
        setPagination(prev => ({
          ...prev,
          page: newHasMore ? currentPage + 1 : currentPage,
          hasMore: newHasMore,
        }));
      } else {
        Toast.error(response.msg || '获取订单列表失败');
      }
    } catch (error) {
      debugLog('[API ERROR]', error);
      Toast.error('请求出错，请稍后再试');
    } finally {
      setLoading(false);
      setPagination(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleRefresh = async () => {
    await fetchOrderList(true);
  };

  const handleLoadMore = (callback: (status: 'prepare' | 'loading' | 'nomore' | 'retry') => void) => {
    fetchOrderList(false).then(() => {
      if (pagination.hasMore) {
        callback('prepare');
      } else {
        callback('nomore');
      }
    }).catch(() => {
      callback('retry');
    });
  };

  useEffect(() => {
    fetchOrderList(true);
  }, []);

  const getStatusInfo = () => {
    const orderCount = Array.isArray(orderList) ? orderList.length : 0;
    const scenario = getDebugScenario();
    const isEmptyScenario = scenario === 'empty_orders';

    return {
      '当前场景': scenario,
      '场景描述': isEmptyScenario ? '🔍 空订单测试场景' : '📋 正常订单场景',
      '订单数': orderCount,
      '是否空列表': orderCount === 0 ? '✅ 是（符合预期）' : '❌ 否',
      '显示状态': orderCount === 0 && !loading ? '📭 空状态显示' : '📋 列表状态显示',
      '可加载更多': pagination.hasMore ? '是' : '否',
      '当前页': pagination.page,
      '加载中': pagination.isLoading ? '⏳ 是' : '✅ 否',
      '分页限制': pagination.limit,
      '调试模式': isDebugMode() ? '✅ 启用' : '❌ 禁用',
    };
  };



  const handleBackToPurchase = () => {
    Taro.navigateBack();
  };

  const renderEmptyState = () => {
    const scenario = getDebugScenario();
    const isEmptyOrdersScenario = scenario === 'empty_orders';
    const isDebugModeActive = isDebugMode();

    return (
      <View className="empty-state-container">
        {isDebugModeActive && (
          <YkNoticeBar
            type={isEmptyOrdersScenario ? "success" : "info"}
            content={
              isEmptyOrdersScenario
                ? "🔍 调试模式：空订单场景测试 - 功能正常工作"
                : `🐛 调试模式：${scenario} 场景`
            }
            closeable
          />
        )}
        <View className="empty-content">
          <View>
            <IconLoadEmpty className='empty-icon'/>
          </View>
          <Text className="empty-text">
            {isEmptyOrdersScenario ? "🔍 调试场景：暂无订单数据" : "您还未购买任何服务"}
          </Text>
          <Text className="empty-description">
            {isEmptyOrdersScenario
              ? "这是空订单场景的测试状态，用于验证空状态显示效果"
              : "购买会员服务后，您的订单将在这里显示"
            }
          </Text>
          <Button
            className="back-button"
            type="primary"
            size="small"
            onClick={handleBackToPurchase}
          >
            {isEmptyOrdersScenario ? "🔄 返回测试购买" : "返回购买"}
          </Button>
        </View>
      </View>
    );
  };

  const renderOrderItem = (order: VipOrderRecordForUI) => (
    <View key={order.id} className="vip-order-item">
      <View className="vip-box">
        <View className="vip-badge">
          <Text className="vip-text">VIP</Text>
        </View>
      </View>
      <View className="vip-order-details">
        <Text className="vip-order-title">{order.package_name}</Text>
        <Text className="vip-order-expire">支付时间：{formatTime(order.payTime)}</Text>
      </View>
    </View>
  );

  return (
    <PageSwipeBack>
      <View className="pay-order-page">
        <YkNavBar title="已购服务" />
        <View className="content-wrapper">
          {(!Array.isArray(orderList) || orderList.length === 0) && !loading ? (
            renderEmptyState()
          ) : (
            <PullRefresh onRefresh={handleRefresh}>
              <View className="vip-order-list">
                {Array.isArray(orderList) && orderList.map(renderOrderItem)}
                {/* 只有在有数据且可能有更多数据时才显示 LoadMore */}
                {orderList.length > 0 && (
                  <LoadMore
                    status={pagination.isLoading ? 'loading' : pagination.hasMore ? 'prepare' : 'nomore'}
                    getData={handleLoadMore}
                  />
                )}
              </View>
            </PullRefresh>
          )}
        </View>
        {isDebugMode() && (
          <DebugPanel
            scenario={getDebugScenario()}
            config={vipCenterDebugger.config}
            onLoadMockData={(scenario) => {
              debugLog(`[debug-panel] Loading mock data for scenario: ${scenario}`);
              fetchOrderList(true, scenario);
            }}
            statusInfo={getStatusInfo()}
          />
        )}
      </View>
    </PageSwipeBack>
  );
};

export default PayOrder;