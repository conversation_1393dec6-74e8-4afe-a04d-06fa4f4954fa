@import "@arco-design/mobile-react/style/mixin.less";
@import "@/utils/css/variables.less";

.vip-center {
  min-height: 100vh;
  background-color: #f7f8fa; // 填充 Fill/填充fill-1
  background-color: @card-background-color !important;
  .use-dark-mode-query({
    background-color: @dark-card-background-color !important;
  });

  // 用户信息区域
  .user-info-section {
    // background: #FFFFFF; // 填充 Fill/填充white
    padding: 40px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;

    .avatar-image {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      //overflow: hidden;
    }

    .user-name {
      font-size: 15px;
      font-weight: 500; // PingFangSC-Medium
      color: #1d2129; // 文字 Text/文字-5-基础  Grey 10
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: var(--dark-font-color) !important;
      });
      line-height: 1.4;
    }

    .member-expire-info {
      display: flex;
      flex-direction: row;
      align-items: center;

      .member-expire-msg {
        font-size: 12px;
        // color: #4e5969; // 文字 Text/文字-4-副标- Grey 8
        .use-var(color, sub-font-color);
        .use-dark-mode-query({
          color: var(--dark-sub-font-color) !important;
        });
        line-height: 1.4;
      }
      .member-expire-time {
        font-size: 12px;
        // color: #4e5969; // 文字 Text/文字-4-副标- Grey 8
        color: @--warning-6;
        line-height: 1.4;
      }
    }
  }

  // 套餐选择区域
  .packages-section {
    padding: 0 15px;
    display: flex;
    flex-direction: column;
    gap: 22px;
    margin-top: 27px; // 167 - 140 = 27px

    .package-card {
      background: #ffffff;
      .use-var(background-color, background-color);

      .use-dark-mode-query({
        background-color: var(--dark-cell-background-color) !important;
      });
      border-radius: 10px;
      padding: 12px;
      box-shadow: 0px 2px 4.5px 7px rgba(0, 0, 0, 0.03);
      position: relative;

      .package-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;

        .package-left {
          display: flex;
          align-items: center;
          gap: 10px;

          .vip-box {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            .rem(padding, 5);
            background-color: @--warning-1;
            border-radius: 10px;
          }

          .vip-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 2px 6px; // 紧凑的内边距
            background: linear-gradient(
              269deg,
              #fe9460 0%,
              #ffebc0 97%
            ); // 橙色渐变背景
            border: 0.5px solid #ffcf8b; // 金色边框
            border-radius: 6px; // 圆角
            max-width: 100%; // 防止溢出

            .vip-text {
              font-family: "PingFang SC";
              font-size: 11px; // 小字体
              font-weight: 900; // 超粗字体
              line-height: 1.2; // 紧凑行高
              color: #1d2129; // 深色文字
              .use-var(color, font-color); // 支持主题变量
            }
          }

          .package-info {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 12px;

            .package-name-container {
              position: relative; // 为Badge提供定位基准

              .recommended-text {
                height: 19px;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 1px 7px;
                // border-radius: 8px 8px 8px 0;
                 border-radius: 50px 50px 50px 3px;
                 border: 1px solid @--warning-6;; 
                background-color: @--warning-1;
                color: @--warning-6;
                * {
                  font-size: 12px;
                  line-height: 1.4;
                }
              }
            }

            .package-name {
              font-size: 18px;
              // font-weight: normal;
              font-weight: 600;
              // color: #1d2129;
              .use-var(color, font-color);
              .use-dark-mode-query({
                color: var(--dark-font-color) !important;
              });

              line-height: 1.4;
            }

            .package-price {
              font-size: 14px;
              font-weight: 500;
              // color: #3d3d3d;
              .use-var(color, sub-font-color);
              .use-dark-mode-query({
                color: var(--dark-sub-font-color) !important;
              });
              line-height: 1.4;
            }
          }
        }

        .purchase-btn {
          // width: 60px;
          // height: 32px;
          border-radius: 16px;
          font-size: 14px;
          border: none;
          cursor: pointer;

          &.outline {
            background: transparent;
            border: 1px solid #165dff; // 主色 Brand Color/主色-6-基础
            color: #165dff;
          }

          &.primary {
            background: #165dff;
            color: #ffffff;
          }
        }
      }

      // // 推荐标签
      // .recommended-badge {
      //   position: absolute;
      //   top: -8.5px;
      //   left: 53.5px;
      //   width: 62px;
      //   height: 19px;
      //   background: #FFF7E8;
      //   border: 1px solid #FF7D00;
      //   border-radius: 10px 50px 50px 3px;
      //   display: flex;
      //   align-items: center;
      //   justify-content: center;

      //   .recommended-text {
      //     font-size: 12px;
      //     font-weight: 500;
      //     color: #FF7D00;
      //   }
      // }
    }
  }

  // 功能区域
  .functions-section {
    padding: 60px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;

    .function-links {
      display: flex;
      align-items: center;
      gap: 25px;

      .function-link {
        font-size: 14px;
        font-weight: 500;
        color: #002c8c; // Primary/colorPrimaryText
        cursor: pointer;
      }

      .divider {
        width: 1px;
        height: 11px;
        background: #bebebe;
        transform: rotate(90deg);
      }
    }

    .benefits-btn {
      // width: 104px;
      // height: 24px;
      background: #86909c; // 文字 Text/文字-3-附加信息-Grey 6
      border: none;
      border-radius: 12px;
      font-size: 12px;
      color: #ffffff;
      cursor: pointer;
    }
  }

  // 底部协议
  .agreement-section {
    position: absolute;
    bottom: 20px;
    width: 100%;
    margin: 0 auto;
    padding: 0 0 20px;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    flex: 1;

    .agreement-text-msg {
      font-size: 11px;
      font-weight: 500;
      color: #86909c;
      text-align: center;
    }

    .agreement-text-link {
      font-size: 11px;
      font-weight: 500;
      color: #165dff;
      text-align: center;
    }
  }

  // 支付状态显示样式
  .payment-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;

    .payment-loading-content {
      background: white;
      border-radius: 12px;
      padding: 40px 30px;
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 200px;

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }

      .loading-text {
        font-size: 16px;
        color: #333;
        text-align: center;
      }
    }
  }

  .payment-error-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;

    .payment-error-content {
      background: white;
      border-radius: 12px;
      padding: 30px;
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 200px;

      .error-text {
        font-size: 16px;
        color: #ff4d4f;
        text-align: center;
        margin-bottom: 20px;
      }

      .retry-button {
        background: #1890ff;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 20px;
      }
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}
