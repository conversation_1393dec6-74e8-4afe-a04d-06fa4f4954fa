import { View, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import PageSwipeBack from '@/components/PageSwipeBack';
import YkNavBar from '@/components/ykNavBar';
import { Button } from '@arco-design/mobile-react';
import { IconCircleChecked } from '@arco-design/mobile-react/esm/icon';
import {
  isDebugMode,
  getDebugScenario,
  debugLog,
  DebugScenario,
  vipCenterDebugger
} from './utils';
import { DebugPanel } from '@/pages/debug';
import './paySuccess.less';

const PaySuccess = () => {
  // 获取URL参数
  const router = Taro.useRouter();
  const orderId = router.params.orderId || '';

  // 我知道了 - 返回会员中心
  const handleConfirm = () => {
    debugLog('[basic] 用户点击我知道了');

    // 返回会员中心页面
    Taro.navigateBack({
      delta: 1 // 返回到会员中心
    });
  };

  // 获取状态信息用于调试面板显示
  const getStatusInfo = () => ({
    '页面': '支付成功页',
    '订单ID': orderId || '无',
    '状态': '支付完成'
  });

  // 加载模拟数据
  const loadMockData = async (scenario: DebugScenario) => {
    debugLog(`[mock] 开始加载模拟数据，场景: ${scenario}`);
    debugLog(`[mock] 模拟数据加载完成`);
  };

  return (
    <PageSwipeBack>
      <View className="pay-success">
        <YkNavBar title="支付成功" />
        
        {/* 主要内容区域 */}
        <View className="main-content">
          {/* 成功图标 */}
          <View>
            <IconCircleChecked className="success-icon"/>
          </View>
          
          {/* 支付成功文字 */}
          <Text className="success-text">支付成功</Text>
        </View>

        {/* 底部按钮 */}
        <View className="bottom-button">
          <Button 
            className="confirm-btn" 
            type="primary"
            onClick={handleConfirm}
          >
            我知道了
          </Button>
        </View>

        {/* 调试组件 - 独立的调试功能，不影响实际页面代码 */}
        {isDebugMode() && (
          <DebugPanel
            scenario={getDebugScenario()}
            config={vipCenterDebugger.config}
            onLoadMockData={loadMockData}
            statusInfo={getStatusInfo()}
          />
        )}
      </View>
    </PageSwipeBack>
  );
};

export default PaySuccess;
