@import '@arco-design/mobile-react/style/mixin.less';
@import "@/utils/css/variables.less";

.pay-order-page {
  width: 100%;
  min-height: 100vh;
  background-color: #F7F8FA; /* 填充 Fill/填充fill-1 */
  background-color: @card-background-color !important;
  .use-dark-mode-query({
    background-color: @dark-card-background-color !important;
  });

  .content-wrapper {
    height: calc(100vh - 54px);
  }

  .vip-order-list {
    display: flex;
    flex-direction: column;

    padding: 12px 15px;
    gap: 22px;
  }

  .vip-order-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background-color: #FFFFFF; /* 填充 Fill/Container 容器背景色 */
    .use-var(background-color, background-color);
    .use-dark-mode-query({
      background-color: var(--dark-cell-background-color) !important;
    });
    border-radius: 10px;
    box-shadow: 0px 2px 4.5px 7px rgba(0, 0, 0, 0.03);
    gap: 12px;

    .vip-box {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      .rem(padding, 5);
      background-color: @--warning-1;
      border-radius: 10px;

      .vip-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 2px 6px; // 可根据视觉效果微调
        background: linear-gradient(269deg, #FE9460 0%, #FFEBC0 97%);
        border: 0.5px solid #FFCF8B;
        border-radius: 6px; // 可略调小，更贴合文字
        max-width: 100%; // 防止撑出容器

        .vip-text {
          font-family: 'PingFang SC';
          font-size: 11px;
          font-weight: 900;
          line-height: 1.2; // 让文字垂直居中贴合
          color: #1D2129;
          .use-var(color, font-color);

          .use-dark-mode-query {
            color: @dark-font-color !important;
          }

        }
      }
    }

    .vip-order-details {
      flex: 1;
      display: flex;
      flex-direction: column;

      .vip-order-title {
        font-family: 'PingFang SC';
        font-size: 18px;
        font-weight: bold;
        line-height: 1.4;
        .use-var(color, font-color);
        .use-dark-mode-query({
          color: var(--dark-font-color) !important;
        });
        margin-bottom: 4px;
      }

      .vip-order-expire {
        font-family: 'PingFangSC-Regular';
        font-size: 12px;
        line-height: 1.4;
        color: #86909C; /* 文字 Text/文字-3-附加信息-Grey 6 */
        .use-var(color, sub-font-color);
        .use-dark-mode-query {
          color: @dark-sub-font-color;
        }
      }
    }
  }

  .empty-state-container {
    height: 100%;
    background-color: #FFFFFF; /* 填充 Fill/Container 容器背景色 */
    .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 249px 0;
      gap: 30px;

      .empty-icon {
        width: 50px;
        height: 57px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary-color);
      }

      .empty-text {
        font-family: 'PingFang SC';
        font-size: 14px;
        font-weight: 500;
        line-height: 1.4;
        color: #86909C; /* 文字 Text/文字-3-附加信息-Grey 6 */
        .use-var(color, sub-font-color);
        .use-dark-mode-query {
          color: @dark-sub-font-color !important;
        }
      }

      .back-button {
        width: 88px;
        height: 32px;
        font-family: 'PingFangSC-Regular';
        font-size: 14px;
        line-height: 1.4;
        color: #FFFFFF; /* 填充 Fill/Mask 蒙层内容字体颜色 */
      }

      .empty-description {
        font-family: 'PingFang SC';
        font-size: 12px;
        font-weight: 400;
        line-height: 1.4;
        color: #C9CDD4; /* 更浅的灰色 */
        text-align: center;
        margin-top: -15px; /* 减少与上方文字的间距 */
      }

      .debug-info-box {
        margin-top: 20px;
        padding: 12px 16px;
        background-color: rgba(22, 93, 255, 0.05);
        border: 1px solid rgba(22, 93, 255, 0.2);
        border-radius: 8px;
        min-width: 200px;
      }

      .debug-info-title {
        font-family: 'PingFang SC';
        font-size: 13px;
        font-weight: 600;
        color: #165DFF;
        text-align: center;
        margin-bottom: 8px;
      }

      .debug-info {
        font-family: 'PingFang SC';
        font-size: 11px;
        color: #666;
        text-align: left;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // 调试快速按钮
  .debug-quick-buttons {
    position: fixed;
    top: 100px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 1000;

    .debug-empty-button,
    .debug-full-button {
      font-size: 11px;
      padding: 6px 10px;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .debug-empty-button {
      background-color: #fff7e6;
      border: 1px solid #ffa940;
      color: #fa8c16;
    }

    .debug-full-button {
      background-color: #f6ffed;
      border: 1px solid #52c41a;
      color: #389e0d;
    }
  }
}