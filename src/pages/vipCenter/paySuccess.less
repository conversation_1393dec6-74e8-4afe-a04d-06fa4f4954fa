@import '@arco-design/mobile-react/style/mixin.less';

.pay-success {
  min-height: 100vh;
  // background-color: #f8f9fa;
  background-color: @card-background-color !important;
  .use-dark-mode-query({
    background-color: @dark-card-background-color !important;
  });
  // display: flex;
  // flex-direction: column;

  // 主要内容区域
  .main-content {
    margin-top: 165px;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0px 30px;

    // 成功图标
    .success-icon {
      margin-bottom: 20px;
      width: 50px;
      height: 50px;
      color: var(--dark-primary-color); 
    }

    // 支付成功文字
    .success-text {
      font-size: 15px;
      color: #333333;
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: var(--dark-font-color) !important;
      });
      font-weight: 500;
    }
  }

  // 底部按钮区域
  .bottom-button {
    margin-top: 300px;
    padding: 0 30px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    display: flex;
    justify-content: center;
    align-items: center;

    // 确认按钮
    .confirm-btn {
      width:164px;
      font-size: 32px;
      font-weight: 500;
      background-color: var(--dark-primary-color); 
      color: #ffffff;
      border: none;
      display: flex;
      justify-content: center;
      align-items: center;
      

      // &:active {
      //   background-color: #096dd9;
      // }
    }
  }

  // 暗色模式适配
  .use-dark-mode-query({
    background-color: var(--dark-bg-color);

    .main-content {
      .success-icon .icon-circle {
        background-color: var(--dark-primary-color);
      }

      .success-text {
        color: var(--dark-text-color);
      }
    }

    .bottom-button .confirm-btn {
      background-color: var(--dark-primary-color);

      &:active {
        background-color: var(--dark-primary-color-hover);
      }
    }
  });
}
