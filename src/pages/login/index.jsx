import { View, Text } from '@tarojs/components'
import { Button, Checkbox, Image, Popup } from '@arco-design/mobile-react'
// import { IconWechat } from '@arco-design/mobile-react/esm/icon'
import './index.less'
import Taro from '@tarojs/taro'
import { socialLogin, getUserInfo } from '@/utils/api/common/common_user'
import { getSystem } from '@arco-design/mobile-utils';
import { useState } from 'react';
import YkNavBar from '@/components/ykNavBar'
import { Style } from '@tarojs/runtime'
import { useReady } from '@tarojs/taro'
import ProtocolMask from '@/components/ProtocolMask'
import { useEffect } from 'react'
import md5 from 'js-md5'

export default function Login() {
  // const [wxLoginInfoState, setWxLoginInfoState] = useState({});
  const [check, setCheck] = useState(false);
  const [visible, setVisible] = useState(false);
  const [protocolVisible, setProtocolVisible] = useState(false);

  useReady(() => {

    let startPrivacy = Taro.getStorageSync('startPrivacy');
    if (!startPrivacy) {
      setProtocolVisible(true);
    }
    if (Taro.getStorageSync('userInfo')) {
      Taro.navigateTo({ url: '/pages/index/index' })
    }
  })

  useEffect(() => {
    // const loginInfo = window.wxLoginInfo;
    // window.wxLoginInfo = wxLoginAndroid;
    window.wxLoginCode = wxLoginIos;

    return () => {
      window.wxLoginCode = null;
    };
}, []);

  const handleProtocolConfirm = () => {
    Taro.setStorageSync('startPrivacy', true);
    setProtocolVisible(false);
  }

  const handleWechatLoginCheck = () => {
    if (!check) {
      setVisible(true);
      return
    }
    handleWechatLogin();
  }

  const handleWechatLogin = () => {
    let uaAll = window.navigator.userAgent;

    // 判断是否是 Android 或 iOS
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;

    if (isAndroid) {
      window.wxLogin.wxLogin();
      // wechatLoginApi()
    } else if (isIos) {
      window.webkit?.messageHandlers.wxLogin.postMessage('');
    } else {
      Taro.showToast({
        title: '当前平台不支持微信登录',
        icon: 'none'
      })
    }
  }

  const wechatLoginApi = (code) => {
    let data = {
      type: 32
    }
    data.code = code;
    data.state = md5(new Date().getTime().toString());

    console.log(data)
    socialLogin(data).then((res) => {
    console.log(res)
      if (res.code == 0) {
        Taro.setStorageSync('userInfo', res.data)

        getUserInfo().then((res2) => {
          if (res2.code == 0) {
            //合并用户信息
            Taro.showToast({
              title: '登录成功',
              icon: 'none'
            })
            Taro.setStorageSync('userInfo', {
              ...res2.data,
              ...res.data
            })
            Taro.reLaunch({ url: '/pages/index/index' })
          }
        })
      } else {
        Taro.showToast({
          title: res.msg,
          icon: 'none'
        })
      }
    })

  }

  const wxLoginIos = (data) => {

    if (data) {
      console.log("wxLoginIos: " + JSON.stringify(data))
      wechatLoginApi(data)
    } else {
      Taro.showToast({
        title: '获取微信信息失败',
        icon: 'none'
      })
    }
  };

  return (
    <View className='login'>
      <YkNavBar title="" />
      <View className='logo-container'>
        <Image className='logo' src={require('../../assets/images/login/logo.png')} bottomOverlap={null} />
        <Text className='app-name'>东东相册</Text>
      </View>

      <View className='login-buttons'>
        <Button
          className='wechat-login'
          icon={<Image src={require('../../assets/images/login/wechat.png')} className='wechat-icon' bottomOverlap={null} />}
          //   shape='round'
          onClick={handleWechatLoginCheck}
        >
          微信登录
        </Button>

        <Button
          className='phone-login'
          //   shape='round'
          type="ghost"
          onClick={() => { Taro.navigateTo({ url: '/pages/phoneLogin/index' }) }}
        >
          手机号登录
        </Button>
      </View>

      <View className='agreement'>
        <Checkbox checked={check} onChange={() => { setCheck(!check) }} value={''}></Checkbox>
        <Text>已阅读并同意</Text>
        <Text className='link' onClick={() => { Taro.navigateTo({ url: '/pages/webView/index?id=123&name=用户协议' }) }}>《用户协议》</Text>
        <Text>和</Text>
        <Text className='link' onClick={() => { Taro.navigateTo({ url: '/pages/webView/index?id=126&name=隐私政策' }) }}>《隐私政策》</Text>
      </View>

      <Popup visible={visible} close={() => setVisible(false)} contentStyle={{ borderRadius: '10px 10px 0 0' }} direction={'bottom'}>
        <View className='cus-popup-content'>
          <Image src={require('../../assets/images/login/close.png')} className='popup-close' onClick={() => setVisible(false)} bottomOverlap={null} />
          <View className="popup-title">用户协议及隐私保护</View>
          <View className="popup-desc">
            <Text>为了更好地保障您的合法权益，请您阅读并同意以下协议</Text>
            <Text className='link' onClick={() => { Taro.navigateTo({ url: '/pages/webView/index?id=123&name=用户协议' }); setVisible(false) }}>《用户协议》</Text>
            <Text>和</Text>
            <Text className='link' onClick={() => { Taro.navigateTo({ url: '/pages/webView/index?id=126&name=隐私政策' }); setVisible(false) }}>《隐私政策》</Text>
            <Text>的全部内容。</Text>
          </View>
          <Button className="popup-confirm" onClick={() => {
            setCheck(true);
            handleWechatLogin();
            setVisible(false);
          }}>
            同意并继续
          </Button>
        </View>
      </Popup>

      <ProtocolMask visible={protocolVisible} close={() => setProtocolVisible(false)} onConfirm={handleProtocolConfirm} />
    </View>
  )
}