@import '@arco-design/mobile-react/style/mixin.less';

.login {
  // min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  .rem(padding-top, 84);
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });

  .logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    .rem(margin-top, 65);
    text-align: center;

    .logo {
      .rem(width, 70);
      .rem(height, 70);
      .rem(margin-bottom, 10);
    }

    .app-name {
      .text-medium();
      .rem(font-size, 18);
      .use-var(color, font-color);
      .use-dark-mode-query({
        color: @dark-font-color;
      });
    }
  }

  .login-buttons {
    // width: 100%;
    .rem(margin-top, 130);

    .wechat-login {
      .wechat-icon {
        .rem(width, 25);
        .rem(height, 25);
      }
      display: flex;
      align-items: center;
      justify-content: center;
      .rem(width, 290);
      .rem(height, 44);
      .rem(border-radius, 6);
      .rem(margin-bottom, 20);
    }

    .phone-login {
      .rem(width, 290);
      .rem(height, 44);
      .rem(border-radius, 6);
    }
  }

  .agreement {
    .rem(font-size, 10);
    .use-var(color, sub-info-font-color);
    .use-dark-mode-query({
      color: @dark-sub-info-font-color;
    }); 
    display: flex;
    align-items: center;
    .rem(margin-top, 15);

    .link {
      .use-var(color, primary-color);
    }
  }

  
} 

.cus-popup-content {
  .rem(margin-bottom, 24);
  .rem(padding, 24);
  
  .popup-close {
    position: absolute;
    .rem(top, 15);
    .rem(right, 15);
    .rem(width, 10);
    .rem(height, 10);
    // .rem(margin-bottom, 10);
  }
  .popup-title {
    .text-medium();
    .rem(font-size, 16);
    .rem(margin-bottom, 9);
    text-align: center;
    .use-var(color, font-color);
    .use-dark-mode-query({
      color: @dark-font-color;
    });
  }
  
  .popup-desc {
    .rem(font-size, 13);
    .use-var(color, sub-info-font-color);
    .use-dark-mode-query({
      color: @dark-sub-info-font-color;
    });
    // .rem(line-height, 1.5);
    .rem(margin-bottom, 24);
    text-align: left;
    .link {
      .use-var(color, primary-color);
    }
  }
  
  .popup-confirm {
    width: 100%;
    height: 44px;
    .rem(border-radius, 2);
    .rem(font-size, 15);
  }
}
