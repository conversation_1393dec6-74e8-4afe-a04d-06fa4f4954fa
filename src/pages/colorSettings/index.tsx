import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import {
  Cell,
  Switch,
  ContextProvider,
  Picker,
} from "@arco-design/mobile-react";
import React from "react";
import Taro from "@tarojs/taro";
// 引入色板
import arcoPalette from "@/utils/arco-palette.json";


// 组件
import YkNavBar from "@/components/ykNavBar/index";


export default function colorSettings() {
  const [httpPlatform, setHttpPlatform] = React.useState("H5");
  // 明亮模式色板列表
  const lightPaletteList = arcoPalette.light;
  // 暗黑模式色板列表
  const darkPaletteList = arcoPalette.dark;

  // 暗黑模式
  const [darkMode, setDarkMode] = React.useState(false);

  // 切换暗黑模式
  const handleSetDarkMode = (value) => {
    Taro.setStorageSync("darkMode", value);
    setDarkMode(value);
  };

  // 主题
  const [themeType, setThemeType] = React.useState("");
  const pageBgColor = ["#FFFFFF", "#F7F8FA", "#FAFAFA", "#F7F7F7"];
  const [pageBgColorType, setPageBgColorType] = React.useState("#F7F7F7");

  const theme = React.useMemo(() => {
    let pageArr = {
      "page-primary-background-color": pageBgColorType,
    };
    if (themeType != "" && themeType) {
      Taro.setStorageSync("themeType", themeType);
      let arr = {
        "primary-color": lightPaletteList[themeType][6 - 1], //6
        "primary-disabled-color": lightPaletteList[themeType][3 - 1], //3
        "lighter-primary-color": lightPaletteList[themeType][1 - 1], //1
        "button-primary-clicked-background": lightPaletteList[themeType][7 - 1], //7

        "dark-primary-color": darkPaletteList[themeType][6 - 1],
        "dark-primary-disabled-color": darkPaletteList[themeType][3 - 1],
        "dark-lighter-primary-color": darkPaletteList[themeType][1 - 1],
        "dark-button-primary-clicked-background":
          darkPaletteList[themeType][7 - 1],
        ...pageArr,
      };
      Taro.setStorageSync("theme", arr);
      return arr;
    } else {
      return pageArr;
    }
  }, [themeType, pageBgColorType]);

  const getColorPickerData = (originalColors: Record<string, any>) => {
    return Object.entries(originalColors).map(([colorName, colorArray]) => {
      const background = colorArray[5];
      return {
        label: (
          <span className="demo-picker-color">
            <i style={{ background: background }} />
            <span>{colorName}</span>
          </span>
        ),
        value: colorName,
      };
    });
  };

  // 页面背景颜色
  const getPageBgColorPickerData = () => {
    return pageBgColor.map((colorName) => {
      const background = colorName;
      return {
        label: (
          <span className="demo-picker-color">
            <i style={{ background: background }} />
            <span>{colorName}</span>
          </span>
        ),
        value: colorName,
      };
    });
  };

  const httpPlatformMode = (darkMode) => {
    let _window: any = window;
    if (httpPlatform == "Android") {
      if (darkMode) {
        console.log("httpPlatform StatusBarDarkMode");
        _window.StatusBarLightMode.StatusBarLightMode();

      } else {
        console.log("httpPlatform StatusBarLightMode");
        _window.StatusBarDarkMode.StatusBarDarkMode();

      }
    } else if (httpPlatform == "IOS") {
      console.log("IOS darkMode", darkMode);
      if (darkMode) {
        _window.webkit.messageHandlers.configStatusBarStyle.postMessage(
          "white"
        );
      } else {
        _window.webkit.messageHandlers.configStatusBarStyle.postMessage(
          "black"
        );
      }
    }
  };

  const themeSingle = React.useMemo(() => {
    // StatusBarDarkMode
    // StatusBarLightMode
    // configStatusBarStyle('black')
    // configStatusBarStyle('white')
    httpPlatformMode(darkMode);
    if (darkMode) {
      return getColorPickerData(darkPaletteList);
    } else {
      return getColorPickerData(lightPaletteList);
    }
  }, [darkMode]);

  // 页面背景颜色
  const pageBgColorSingle = React.useMemo(() => {
    return getPageBgColorPickerData();
  }, []);

  useLoad(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    if (isAndroid) {
      setHttpPlatform("Android");
    } else if (isIos) {
      setHttpPlatform("IOS");
    } else {
      setHttpPlatform("H5");
    }
    // 初始化暗黑模式
    setDarkMode(!!Taro.getStorageSync("darkMode"));
    setThemeType(
      Taro.getStorageSync("themeType")
        ? Taro.getStorageSync("themeType")
        : "arcoblue"
    );
    setPageBgColorType(
      Taro.getStorageSync("pageBgColorType")
        ? Taro.getStorageSync("pageBgColorType")
        : "#F7F7F7"
    );
  });

  return (
    <View className="setPageContent">
      <YkNavBar title="设置" />

      <View className="module">
        <View className="module-title">
          <Text className="module-title-text">系统设置</Text>
        </View>
        <View className="module-content">
          <ContextProvider
            isDarkMode={darkMode}
            darkModeSelector="arco-theme-dark"
            theme={theme}
          >
            <Cell.Group bordered={false}>
              {/* 主色设置 */}
              <Picker
                cascade={false}
                data={themeSingle}
                maskClosable={true}
                value={[themeType]}
                onChange={(val: any) => setThemeType(val[0])}
                mountOnEnter={false}
                unmountOnExit={false}
                renderLinkedContainer={(_, data) => (
                  <Cell
                    label="主色设置"
                    className="module-content-cell"
                    showArrow
                  >
                    {data[0]?.label}
                  </Cell>
                )}
              />
              {/* 页面背景颜色设置 */}
              <Picker
                cascade={false}
                data={pageBgColorSingle}
                maskClosable={true}
                value={[pageBgColorType]}
                onChange={(val: any) => setPageBgColorType(val[0])}
                mountOnEnter={false}
                unmountOnExit={false}
                renderLinkedContainer={(_, data) => (
                  <Cell
                    label="页面背景颜色设置"
                    className="module-content-cell"
                    showArrow
                  >
                    {data[0]?.label}
                  </Cell>
                )}
              />

              {/* 暗黑模式 */}
              <Cell className="module-content-cell" label="暗黑模式" text="">
                <Switch
                  checked={darkMode}
                  platform={httpPlatform == "IOS" ? 'ios': 'android'}
                  onChange={(value) => handleSetDarkMode(value)}
                />
              </Cell>
            </Cell.Group>
          </ContextProvider>
        </View>
      </View>
    </View>
  );
}
