import { View, Text, RichText } from "@tarojs/components";
import { Button, Image, Input, NavBar } from "@arco-design/mobile-react";
import "./index.less";
import Taro, { useLoad } from "@tarojs/taro";
import { getAgreement } from "@/utils/api/common/common_user";
import React, { useState, useEffect } from "react";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
// interface AgreementResponse {
//   data: {
//     content: string;
//   }
// }

export default function WebView() {
  const [content, setContent] = useState("");
  const [aid, setAid] = useState("");
  const [name, setName] = useState("");

  useLoad((e) => {
    console.log(e,)
    setAid(e.id)
    setName(decodeURIComponent(e.name))
  });

  useEffect(() => {
    if(aid){
      console.log('aid',aid)
      getAgreementInfo(aid);
    }
  }, [aid]);

  // 获取协议内容
  const getAgreementInfo = (id: string) => {
    let data = {
      id,
    };
    getAgreement(data).then((res:any) => {
      if (res.code == 0) {
        setContent(res.data.content);
      }else{
        Taro.showToast({
          title: res.msg,
          icon: "none",
        });
      }
    })
  };



  return (
    <View className="webview">
      <YkNavBar title={name} />
      <View className="agreement-content">
        {/* <RichText nodes={content} /> */}
        <View dangerouslySetInnerHTML={{ __html: content }}></View>
      </View>
    </View>
  );
}
