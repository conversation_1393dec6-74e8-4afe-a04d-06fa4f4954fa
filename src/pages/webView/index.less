@import '@arco-design/mobile-react/style/mixin.less';

.webview {
//   min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  .agreement-content {
    width: 100%;
    padding: 20px 16px;
    word-wrap: break-word;     // 允许长单词或 URL 地址换行到下一行
    word-break: break-all;     // 允许在单词内换行
    .use-var(color, font-color);
    .use-dark-mode-query({
      color: @dark-font-color;
    });
    box-sizing: border-box;
  }
} 

.arco-theme-dark {
  .agreement-content { 
    .MsoNormal span{
      color: #FFFFFF !important;
    }
  }
}