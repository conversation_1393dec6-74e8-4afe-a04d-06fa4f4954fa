/**
 * @description VIP会员支付服务模块，用于处理混合应用中的原生支付流程
 * 支持微信支付调起和支付结果回调处理
 */

// 支付方式常量
export const PAYMENT_PROVIDERS = {
  ALIPAY: 'alipay_app',
  WECHAT: 'wx_app',
};

// 支付状态常量
export const PAYMENT_STATUS = {
  SUCCESS: 1,    // 支付成功
  CANCEL: 2,     // 用户取消
  FAILURE: 3,    // 支付失败
  PROCESSING: 4, // 处理中
};

// 显示模式常量
export const DISPLAY_MODE = {
  APP: 'app',    // App调起支付
  URL: 'url',    // URL跳转支付
  QR: 'qr',      // 二维码支付
};

/**
 * @private
 * 检测运行环境
 * @returns {{isIos: boolean, isAndroid: boolean, isApp: boolean, isH5: boolean}}
 */
function getEnvironment() {
  const ua = window.navigator.userAgent;

  // 检测UserAgent信息
  const uaInfo = {
    userAgent: ua,
    includesAndroid: ua.includes('android'),
    includesIos: ua.includes('ios'),
    includesIPhone: ua.includes('iPhone'),
    includesAndroidCapital: ua.includes('Android'),
  };

  // 检测Window对象中的支付相关属性
  const windowInfo = {
    hasWxPay: !!window.wxPay,
    hasWxPayMethod: !!(window.wxPay && window.wxPay.wxPay),
    hasAliPay: !!window.aliPay,
    hasAliPayMethod: !!(window.aliPay && window.aliPay.aliPay),
    hasWebkit: !!window.webkit,
    hasWebkitMessageHandlers: !!(window.webkit && window.webkit.messageHandlers),
    hasWxPayHandler: !!(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.wxPayWithStr),
    hasAliPayHandler: !!(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.aliPayWithStr),
  };

  // 检测是否在App环境中
  const isAndroid = ua.includes('android') && (window.wxPay || window.aliPay);
  const isIos = ua.includes('ios') && window.webkit?.messageHandlers;
  const isApp = isAndroid || isIos;
  const isH5 = !isApp;

  // 环境检测结果
  const environmentResult = {
    isIos,
    isAndroid,
    isApp,
    isH5,
  };

  // 支付能力检测
  const paymentCapabilities = {
    canWechatPayAndroid: isAndroid && windowInfo.hasWxPayMethod,
    canWechatPayIos: isIos && windowInfo.hasWxPayHandler,
    canAlipayAndroid: isAndroid && windowInfo.hasAliPayMethod,
    canAlipayIos: isIos && windowInfo.hasAliPayHandler,
    canWechatPay: (isAndroid && windowInfo.hasWxPayMethod) || (isIos && windowInfo.hasWxPayHandler),
    canAlipay: (isAndroid && windowInfo.hasAliPayMethod) || (isIos && windowInfo.hasAliPayHandler),
  };

  // 强制输出环境信息（用于生产环境调试）
  console.log('🔍 [FORCE] PaymentService Environment Detection:', {
    uaInfo,
    windowInfo,
    environmentResult,
    paymentCapabilities
  });

  // 输出详细的调试信息
  console.group('🔍 [PaymentService] Environment Detection');
  console.log('📱 UserAgent Info:', uaInfo);
  console.log('🪟 Window Object Info:', windowInfo);
  console.log('🌍 Environment Result:', environmentResult);
  console.log('💳 Payment Capabilities:', paymentCapabilities);
  console.groupEnd();

  return { isIos, isAndroid, isApp, isH5 };
}

/**
 * @private
 * 检测运行环境
 * @returns {{isIos: boolean, isAndroid: boolean, isApp: boolean, isH5: boolean}}
 */
function getEnvironmentBak() {
  const ua = window.navigator.userAgent;
  // 检测是否在App环境中 todo
  const isAndroid = ua.includes('android') && (window.wxPay || window.aliPay);
  const isIos = ua.includes('iPhone') && window.webkit?.messageHandlers;
  const isApp = isAndroid || isIos;
  const isH5 = !isApp;
  return { isIos, isAndroid, isApp, isH5 };
}



/**
 * 解析微信支付参数
 * @param {string} displayContent - 从后端返回的支付参数JSON字符串
 * @returns {object} 解析后的微信支付参数
 */
export function parseWechatPayParams(displayContent) {
  try {
    const payParams = JSON.parse(displayContent);
    return {
      appid: payParams.appid,
      partnerId: payParams.partnerId,
      prepayId: payParams.prepayId,
      packageValue: payParams.packageValue,
      noncestr: payParams.noncestr,
      timestamp: payParams.timestamp,
      sign: payParams.sign,
    };
  } catch (error) {
    console.error('解析微信支付参数失败:', error);
    throw new Error('支付参数格式错误');
  }
}

/**
 * 调起微信支付
 * @param {object} payParams - 微信支付参数
 * @param {string} payParams.appid - 微信应用ID
 * @param {string} payParams.partnerId - 商户ID
 * @param {string} payParams.prepayId - 预支付ID
 * @param {string} payParams.packageValue - 包值
 * @param {string} payParams.noncestr - 随机字符串
 * @param {string} payParams.timestamp - 时间戳
 * @param {string} payParams.sign - 签名
 * @returns {Promise<void>}
 */
export function invokeWechatPay(payParams) {
  return new Promise((resolve, reject) => {
    const { isIos, isAndroid, isApp } = getEnvironment();

    // 强制输出支付调起信息（用于生产环境调试）
    console.log('🔍 [FORCE] 微信支付调起开始:', {
      isIos,
      isAndroid,
      isApp,
      hasWxPay: !!window.wxPay,
      hasWxPayMethod: !!(window.wxPay && window.wxPay.wxPay),
      wxPayType: typeof window.wxPay,
      wxPayMethodType: typeof (window.wxPay && window.wxPay.wxPay),
      payParams
    });

    if (!isApp) {
      const error = new Error('当前环境不支持原生支付，请在App中打开');
      console.error('🔍 [FORCE] 环境检测失败:', error.message);
      reject(error);
      return;
    }

    // 构造支付参数字符串
    const payParamsStr = JSON.stringify(payParams);

    console.log('[微信支付] 调起支付参数:', payParams);

    try {
      if (isAndroid && window.wxPay?.wxPay) {
        // Android 微信支付调起
        console.log('🔍 [FORCE] 调起Android微信支付:', payParamsStr);
        window.wxPay.wxPay(payParamsStr);
        console.log('🔍 [FORCE] Android微信支付调起完成');
        resolve();
      } else if (isIos && window.webkit?.messageHandlers?.wxPayWithStr) {
        // iOS 微信支付调起
        console.log('🔍 [FORCE] 调起iOS微信支付:', payParamsStr);
        window.webkit.messageHandlers.wxPayWithStr.postMessage(payParamsStr);
        console.log('🔍 [FORCE] iOS微信支付调起完成');
        resolve();
      } else {
        const error = new Error('微信支付功能不可用，请检查App版本');
        console.error('🔍 [FORCE] 微信支付功能检测失败:', {
          isAndroid,
          isIos,
          hasWxPay: !!window.wxPay,
          hasWxPayMethod: !!(window.wxPay && window.wxPay.wxPay),
          hasWebkit: !!window.webkit,
          hasMessageHandlers: !!(window.webkit && window.webkit.messageHandlers),
          hasWxPayHandler: !!(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.wxPayWithStr)
        });
        reject(error);
      }
    } catch (error) {
      console.error('🔍 [FORCE] 微信支付调起异常:', error);
      reject(new Error('支付调起失败'));
    }
  });
}

/**
 * 调起支付宝支付
 * @param {string} payInfo - 支付宝支付信息字符串
 * @returns {Promise<void>}
 */
export function invokeAlipay(payInfo) {
  return new Promise((resolve, reject) => {
    const { isIos, isAndroid, isApp } = getEnvironment();

    // 强制输出支付调起信息（用于生产环境调试）
    console.log('🔍 [FORCE] 支付宝支付调起开始:', {
      isIos,
      isAndroid,
      isApp,
      hasAliPay: !!window.aliPay,
      hasAliPayMethod: !!(window.aliPay && window.aliPay.aliPay),
      aliPayType: typeof window.aliPay,
      aliPayMethodType: typeof (window.aliPay && window.aliPay.aliPay),
      payInfo
    });

    if (!isApp) {
      const error = new Error('当前环境不支持原生支付，请在App中打开');
      console.error('🔍 [FORCE] 环境检测失败:', error.message);
      reject(error);
      return;
    }

    console.log('[支付宝支付] 调起支付参数:', payInfo);

    try {
      if (isAndroid && window.aliPay?.aliPay) {
        // Android 支付宝支付调起
        console.log('🔍 [FORCE] 调起Android支付宝支付:', payInfo);
        window.aliPay.aliPay(payInfo);
        console.log('🔍 [FORCE] Android支付宝支付调起完成');
        resolve();
      } else if (isIos && window.webkit?.messageHandlers?.aliPayWithStr) {
        // iOS 支付宝支付调起
        console.log('🔍 [FORCE] 调起iOS支付宝支付:', payInfo);
        window.webkit.messageHandlers.aliPayWithStr.postMessage(payInfo);
        console.log('🔍 [FORCE] iOS支付宝支付调起完成');
        resolve();
      } else {
        const error = new Error('支付宝支付功能不可用，请检查App版本');
        console.error('🔍 [FORCE] 支付宝支付功能检测失败:', {
          isAndroid,
          isIos,
          hasAliPay: !!window.aliPay,
          hasAliPayMethod: !!(window.aliPay && window.aliPay.aliPay),
          hasWebkit: !!window.webkit,
          hasMessageHandlers: !!(window.webkit && window.webkit.messageHandlers),
          hasAliPayHandler: !!(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.aliPayWithStr)
        });
        reject(error);
      }
    } catch (error) {
      console.error('🔍 [FORCE] 支付宝支付调起异常:', error);
      reject(new Error('支付调起失败'));
    }
  });
}

/**
 * 处理支付结果数据
 * @param {object} payResult - submitPayOrder 返回的支付结果
 * @param {string} channelCode - 支付渠道编码
 * @returns {Promise<any>} 支付调起结果
 */
export function handlePaymentResult(payResult, channelCode) {
  return new Promise((resolve, reject) => {
    const { status, displayMode, displayContent } = payResult;

    console.log('[支付处理] 支付结果:', payResult);
    console.log('[支付处理] 支付渠道:', channelCode);

    // 检查支付状态
    if (status !== 0) {
      reject(new Error(`支付订单状态异常: ${status}`));
      return;
    }

    // 根据显示模式处理
    switch (displayMode) {
      case DISPLAY_MODE.APP:
        // App调起支付 - 根据渠道类型选择支付方式
        try {
          if (channelCode && channelCode.startsWith('alipay')) {
            // 支付宝支付
            console.log('[支付处理] 调起支付宝支付');
            invokeAlipay(displayContent)
              .then(() => resolve({ alipayInfo: displayContent }))
              .catch(reject);
          } else {
            // 微信支付（默认）
            console.log('[支付处理] 调起微信支付');
            const payParams = parseWechatPayParams(displayContent);
            invokeWechatPay(payParams)
              .then(() => resolve(payParams))
              .catch(reject);
          }
        } catch (error) {
          reject(error);
        }
        break;

      case DISPLAY_MODE.URL:
        // URL跳转支付（H5环境）
        if (displayContent) {
          window.location.href = displayContent;
          resolve({ url: displayContent });
        } else {
          reject(new Error('支付URL为空'));
        }
        break;

      case DISPLAY_MODE.QR:
        // 二维码支付
        resolve({ qrCode: displayContent });
        break;

      default:
        reject(new Error(`不支持的支付模式: ${displayMode}`));
    }
  });
}

/**
 * 注册支付回调函数到全局 window 对象
 * @param {{onSuccess: Function, onFailure: Function, onCancel: Function}} callbacks
 * @returns {Function} 清理函数
 */
export function registerPaymentCallbacks(callbacks) {
  const { isIos, isAndroid } = getEnvironment();

  const handlePaymentResult = (status, message = '') => {
    console.log('[支付回调] 收到支付结果:', { status, message });

    // 支付状态处理
    // 1: 成功, 2: 取消, 3: 失败, 4: 处理中
    switch (String(status)) {
      case '1':
        console.log('[支付回调] 支付成功');
        callbacks.onSuccess?.();
        break;
      case '2':
        console.log('[支付回调] 用户取消支付');
        callbacks.onCancel?.();
        break;
      case '3':
        console.log('[支付回调] 支付失败:', message);
        callbacks.onFailure?.(message || '支付失败');
        break;
      case '4':
        console.log('[支付回调] 支付处理中');
        // 处理中状态，可以显示loading或等待
        break;
      default:
        console.log('[支付回调] 未知支付状态:', status);
        callbacks.onFailure?.(`未知支付状态: ${status}`);
        break;
    }
  };

  // 注册全局回调函数
  if (isIos) {
    // iOS 回调函数
    window.wxPayCallback = handlePaymentResult;
    window.aliPayCallback = handlePaymentResult;
  } else if (isAndroid) {
    // Android 回调函数
    window.wxPayCallback = handlePaymentResult;
    window.aliPayCallback = handlePaymentResult;
    window.webPaySuc = handlePaymentResult; // 兼容旧版本
  }

  // 返回清理函数
  return () => {
    if (isIos) {
      delete window.wxPayCallback;
      delete window.aliPayCallback;
    } else if (isAndroid) {
      delete window.wxPayCallback;
      delete window.aliPayCallback;
      delete window.webPaySuc;
    }
    console.log('[支付回调] 已清理支付回调函数');
  };
}

/**
 * 获取支付环境信息
 * @returns {object} 环境信息
 */
export function getPaymentEnvironment() {
  const env = getEnvironment();
  return {
    ...env,
    supportWechat: env.isApp && (
      (env.isAndroid && window.wxPay?.wxPay) ||
      (env.isIos && window.webkit?.messageHandlers?.wxPayWithStr)
    ),
    supportAlipay: env.isApp && (
      (env.isAndroid && window.aliPay?.aliPay) ||
      (env.isIos && window.webkit?.messageHandlers?.aliPayWithStr)
    ),
  };
}