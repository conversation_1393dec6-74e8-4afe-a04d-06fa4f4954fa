import Taro from "@tarojs/taro";
import sign from "@/utils/sign/sign.js";

// const BASE_URL = process.env.TARO_APP_API;
import {URL_BASE,URL_PPXC} from '@/utils/api/urls';
const blobToFile = async (blobUrl, fileName = `${Date.now()}.jpg`) => {
  const response = await fetch(blobUrl);
  const blob = await response.blob();
  return new File([blob], fileName, { type: blob.type });
};

const requestUploadBlobFiles = async (url, data, blobUrls) => {
  let urlPath = "";
  
  if (url.indexOf("http") === -1) {
    console.log(url)
    // 根据 url 判断使用哪个 BASE_URL
    if (url.startsWith("ppxc")) {
      urlPath = URL_PPXC + url.replace(/^ppxc\//, "");
    } else {
      urlPath = URL_BASE + url;
    }
  } else {
    urlPath = url;
  }
  // 设置请求头
  let http_platform = "H5";
  let uaAll = window.navigator.userAgent;

  // 判断是否是 Android 或 iOS
  let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
  let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;

  if (isAndroid) {
    http_platform = 'android';
  }
  if (isIos) {
    http_platform = 'ios';
  }
  console.log(http_platform);

  const userInfo = Taro.getStorageSync("userInfo");

  if (userInfo) {
    sign.setUserId(userInfo.id);
    sign.setToken(userInfo.token);
  }
  sign.setPlatform(http_platform);
  let headers = {
    "tenant-id": 1
  }
  if (userInfo.accessToken) {
    headers['Authorization'] = userInfo.accessToken;
  }
  // 删除 `Content-Type`，后面 `FormData` 会自动设置
  delete headers["Content-Type"];

  // 确保 blobUrls 是数组
  const blobArray = Array.isArray(blobUrls) ? blobUrls : [blobUrls];

  // 将所有 blobUrls 转换为 File 对象
  const files = await Promise.all(
    blobArray.map((blobUrl, index) =>
      blobToFile(blobUrl, `file_${Date.now()}_${index}.jpg`)
    )
  );

  // 构建 FormData
  const formData = new FormData();
  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value);
  });
  files.forEach((file, index) => {
    formData.append(`file`, file); // 字段名可根据接口要求调整
  });

  // 使用 Taro.request 发送请求
  return Taro.request({
    url: urlPath,
    method: "POST",
    data: formData,
    header: headers,
  })
    .then((res) => {
      console.log("上传结果：", res);
      return res.data;
    })
    .catch((err) => {
      console.error("上传失败：", err);
      throw err;
    });
};

const requestUploadFile = async (url, fileData) => { // 参数改为 file
  let urlPath = "";

  if (url.indexOf("http") === -1) {
    // 根据 url 判断使用哪个 BASE_URL
    if (url.startsWith("ppxc")) {
      urlPath = URL_PPXC + url.replace(/^ppxc\//, "");
    } else {
      urlPath = URL_BASE + url;
    }
  } else {
    urlPath = url;
  }
  // 设置请求头
  let http_platform = "H5";
  let uaAll = window.navigator.userAgent;

  // 判断是否是 Android 或 iOS
  // 注意：APP_NAME 需要在你的环境中定义
  let isAndroid = uaAll.indexOf(`android_${typeof APP_NAME !== 'undefined' ? APP_NAME : 'default_app'}`) > -1;
  let isIos = uaAll.indexOf(`ios_${typeof APP_NAME !== 'undefined' ? APP_NAME : 'default_app'}`) > -1;

  if (isAndroid) {
    http_platform = 'android';
  }
  if (isIos) {
    http_platform = 'ios';
  }
  console.log(http_platform);

  const userInfo = Taro.getStorageSync("userInfo");

  if (userInfo) {
    sign.setUserId(userInfo.id);
    sign.setToken(userInfo.token);
  }
  sign.setPlatform(http_platform);
  let headers = {
    "tenant-id": 1
  }
  if (userInfo && userInfo.accessToken) { // 添加 userInfo 判空
    headers['Authorization'] = userInfo.accessToken;
  }
  // 删除 `Content-Type`，后面 `FormData` 会自动设置
  delete headers["Content-Type"];
  const formData = new FormData();
  formData.append('file', fileData);
  // 使用 Taro.request 发送请求
  return Taro.request({
    url: urlPath,
    method: "POST",
    data: formData,
    header: headers,
  })
    .then((res) => {
      console.log("上传结果：", res);
      // 根据你的 API 返回结构处理成功逻辑
      if (res.statusCode === 200 && res.data /* 根据你的成功判断条件修改 */) {
          return res.data;
      } else {
          // 处理请求成功但业务失败的情况
          console.error("上传业务失败：", res);
          // 可以抛出错误或返回特定错误对象
          throw new Error(res.data?.message || 'Upload failed with status: ' + res.statusCode);
      }
    })
    .catch((err) => {
      console.error("上传网络或代码错误：", err);
      throw err; // 继续抛出错误，让调用者处理
    });
};

export { requestUploadBlobFiles, requestUploadFile };