import Taro from '@tarojs/taro';

let url_base = '';
let url_ppxc = '';

// 判断运行环境和 NODE_ENV
if (Taro.getEnv() === Taro.ENV_TYPE.WEB) {
  // H5 环境
  url_base = process.env.NODE_ENV === 'development' ? '/devApi' : BASE_URL;
  url_ppxc = process.env.NODE_ENV === 'development' ? '/ppxcdevApi' : BASE_URL_PPXC;
} else if (Taro.getEnv() === Taro.ENV_TYPE.APP) {
  // App 环境
  url_base = BASE_URL; // BASE_URL 必须定义
  url_ppxc = BASE_URL_PPXC; // BASE_URL_PPXC 必须定义
}

// 导出命名变量
export const URL_BASE = url_base;
export const URL_PPXC = url_ppxc;

// 默认导出
export default { URL_BASE, URL_PPXC };
