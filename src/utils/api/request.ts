import Taro from "@tarojs/taro";
import sign from "@/utils/sign/sign.js";
const TIME_OUT = 30000;
import {URL_BASE,URL_PPXC} from '@/utils/api/urls';

// 请求选项接口
interface RequestOptions {
  /** 请求URL */
  url: string;
  /** 请求方法 */
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  /** 请求数据 */
  data?: any;
  /** 内容类型，默认为json */
  contentType?: 'json' | 'form' | 'multipart';
  /** 超时时间 */
  timeout?: number;
  /** 额外的请求头 */
  headers?: Record<string, any>;
}

/**
 * 获取平台信息
 */
const getPlatform = (): string => {
  let http_platform = "H5";

  if (typeof window !== 'undefined') {
    const uaAll = window.navigator.userAgent;
    const isAndroid = uaAll.indexOf(`android_${typeof APP_NAME !== 'undefined' ? APP_NAME : 'default_app'}`) > -1;
    const isIos = uaAll.indexOf(`ios_${typeof APP_NAME !== 'undefined' ? APP_NAME : 'default_app'}`) > -1;

    if (isAndroid) {
      http_platform = 'android';
    } else if (isIos) {
      http_platform = 'ios';
    }
  }

  return http_platform;
};

/**
 * 构建请求URL
 */
const buildUrl = (url: string): string => {
  if (url.indexOf("http") !== -1) {
    return url;
  }

  // 根据 url 判断使用哪个 BASE_URL
  if (url.startsWith("ppxc")) {
    return URL_PPXC + url.replace(/^ppxc\//, "");
  } else {
    return URL_BASE + url;
  }
};

/**
 * 处理表单数据
 */
const processFormData = (data: any, contentType: string): any => {
  if (contentType === 'form' && data && typeof data === 'object') {
    // 转换为 URLSearchParams 格式
    const formData = new URLSearchParams();
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, String(value));
      }
    });
    return formData.toString();
  }
  return data || {};
};

/**
 * 通用请求函数
 * 支持旧的调用方式: request(url, method, data)
 * 支持新的调用方式: request({ url, method, data, contentType })
 */
const request = (
  options: string | RequestOptions,
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE',
  data?: any
): Promise<any> => {
  // 兼容旧的调用方式
  let requestOptions: RequestOptions;
  if (typeof options === 'string') {
    requestOptions = {
      url: options,
      method: method || 'GET',
      data: data || {},
      contentType: 'json'
    };
  } else {
    requestOptions = options;
  }

  const {
    url,
    method: requestMethod = 'GET',
    data: requestData = {},
    contentType = 'json',
    timeout = TIME_OUT,
    headers: extraHeaders = {}
  } = requestOptions;

  const urlPath = buildUrl(url);
  const http_platform = getPlatform();

  // 设置用户信息和签名
  const userInfo = Taro.getStorageSync("userInfo");
  console.log("请求头"+JSON.stringify(userInfo));
  if (!!userInfo) {
    sign.setUserId(userInfo.id);
    sign.setToken(userInfo.token);
  }
  sign.setPlatform(http_platform);

  // 构建请求头
  let headers: Record<string, any> = {
    "tenant-id": 1,
    ...extraHeaders
  };

  // 根据内容类型设置Content-Type
  if (contentType === 'json') {
    headers['Content-Type'] = 'application/json';
  } else if (contentType === 'form') {
    headers['Content-Type'] = 'application/x-www-form-urlencoded';
  }
  // multipart 类型不设置 Content-Type，让浏览器自动设置

  if (userInfo?.accessToken) {
    headers['Authorization'] = userInfo.accessToken;
  }

  // 处理请求数据
  const processedData = processFormData(requestData, contentType);

  return new Promise((resolve, reject) => {
    Taro.request({
      url: urlPath,
      method: requestMethod,
      timeout,
      data: processedData,
      header: headers,
      success(res) {
        resolve(res.data);
      },
      fail(err) {
        reject(err);
      },
    });
  });
};

export default request;
