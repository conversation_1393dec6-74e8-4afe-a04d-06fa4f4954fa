import Taro from '@tarojs/taro';

const TIME_OUT = 30000; // 30秒超时

type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'CONNECT' | 'HEAD' | 'OPTIONS' | 'TRACE';

// OCR请求工具函数
export const ocrRequest = <T>(url: string, method: RequestMethod = 'POST', data?: any, customHeaders = {}): Promise<T> => {
  return new Promise((resolve, reject) => {
    // 记录请求信息
    console.log('[OCR Request] 发起请求:', {
      url,
      method,
      data,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        ...customHeaders
      }
    });

    Taro.request({
      url,
      method,
      timeout: TIME_OUT,
      data: data || {},
      header: {
        'Content-Type': 'application/x-www-form-urlencoded',
        ...customHeaders
      },
      success(res) {
        // 记录响应信息
        console.log('[OCR Request] 请求成功:', {
          status: res.statusCode,
          data: res.data
        });
        resolve(res.data as T);
      },
      fail(err) {
        // 记录错误信息
        console.error('[OCR Request] 请求失败:', err);
        reject(err);
      },
    });
  });
}; 