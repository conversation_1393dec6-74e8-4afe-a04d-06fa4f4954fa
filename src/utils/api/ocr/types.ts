// OCR配置类型
export interface OCRConfig {
  apiKey: string;
  secretKey: string;
  accessToken?: string;
}

// OCR识别结果类型
export interface OCRResponse<T> {
  error_code?: number;
  error_msg?: string;
  log_id?: number;
  words_result_num?: number;
  words_result?: T;
}

// Token响应类型
export interface TokenResponse {
  access_token: string;
  expires_in: number;
  refresh_token?: string;
  scope?: string;
  session_key?: string;
  session_secret?: string;
}

// 身份证识别结果类型
export interface IDCardResult {
  住址?: { words: string };
  出生?: { words: string };
  姓名?: { words: string };
  公民身份号码?: { words: string };
  性别?: { words: string };
  民族?: { words: string };
  签发机关?: { words: string };
  签发日期?: { words: string };
  失效日期?: { words: string };
}

// 营业执照识别结果类型
export interface BusinessLicenseResult {
  单位名称?: { words: string };
  法人?: { words: string };
  地址?: { words: string };
  社会信用代码?: { words: string };
  成立日期?: { words: string };
  有效期?: { words: string };
  证件编号?: { words: string };
  类型?: { words: string };
}

// 银行卡识别结果类型
export interface BankCardResult {
  bank_card_number?: { words: string };
  bank_name?: { words: string };
  bank_card_type?: { words: string };
} 

// 开户证明识别结果类型
export interface AccountOpeningResult {
  账号?: { word: string };
  公司名称?: { word: string };
  开户银行?: { word: string };
}