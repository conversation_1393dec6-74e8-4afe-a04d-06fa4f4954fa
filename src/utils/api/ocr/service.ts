import { ocrRequest } from './request';
import type {
  OCRConfig,
  OCRResponse,
  TokenResponse,
  IDCardResult,
  BusinessLicenseResult,
  BankCardResult
} from './types';

class OCRService {
  private config: OCRConfig;
  private baseUrl = '/baiduApi';

  constructor(apiKey: string, secretKey: string) {
    this.config = {
      apiKey,
      secretKey
    };
  }

  // 获取access token
  async getAccessToken(): Promise<string> {
    try {
      console.log('[OCR Service] 开始获取 access_token, 参数:', {
        grant_type: 'client_credentials',
        client_id: this.config.apiKey,
        client_secret: this.config.secretKey
      });

      const response = await ocrRequest<TokenResponse>(
        `${this.baseUrl}/oauth/2.0/token`,
        'POST',
        {
          grant_type: 'client_credentials',
          client_id: this.config.apiKey,
          client_secret: this.config.secretKey
        }
      );

      console.log('[OCR Service] token响应:', response);

      if (response.access_token) {
        this.config.accessToken = response.access_token;
        return response.access_token;
      }
      throw new Error('获取access token失败: 返回数据中没有access_token');
    } catch (error) {
      console.error('[OCR Service] 获取access token失败:', error);
      throw error;
    }
  }

  // 确保access token存在
  private async ensureAccessToken(): Promise<string> {
    if (!this.config.accessToken) {
      return await this.getAccessToken();
    }
    return this.config.accessToken;
  }

  // 身份证识别（正面）
  async recognizeIDCardFront(image: string): Promise<OCRResponse<IDCardResult>> {
    const accessToken = await this.ensureAccessToken();
    try {
      console.log('[OCR Service] 开始识别身份证: front');
      
      // 构建请求参数
      const formData = new URLSearchParams();
      formData.append('access_token', accessToken);
      formData.append('image', image);
      formData.append('id_card_side', 'front');

      return await ocrRequest<OCRResponse<IDCardResult>>(
        `${this.baseUrl}/rest/2.0/ocr/v1/idcard`,
        'POST',
        formData.toString()
      );
    } catch (error) {
      console.error('[OCR Service] 身份证识别失败:', error);
      throw error;
    }
  }
  // 身份证识别（背面）
  async recognizeIDCardBack(image: string): Promise<OCRResponse<IDCardResult>> {
    const accessToken = await this.ensureAccessToken();
    try {
      console.log('[OCR Service] 开始识别身份证: back');
      
      // 构建请求参数
      const formData = new URLSearchParams();
      formData.append('access_token', accessToken);
      formData.append('image', image);
      formData.append('id_card_side', 'back');

      return await ocrRequest<OCRResponse<IDCardResult>>(
        `${this.baseUrl}/rest/2.0/ocr/v1/idcard`,
        'POST',
        formData.toString()
      );
    } catch (error) {
      console.error('[OCR Service] 身份证识别失败:', error);
      throw error;
    }
  }

  // 营业执照识别
  async recognizeBusinessLicense(image: string): Promise<OCRResponse<BusinessLicenseResult>> {
    const accessToken = await this.ensureAccessToken();
    try {
      console.log('[OCR Service] 开始识别营业执照');
      
      // 构建请求参数
      const formData = new URLSearchParams();
      formData.append('access_token', accessToken);
      formData.append('image', image);

      return await ocrRequest<OCRResponse<BusinessLicenseResult>>(
        `${this.baseUrl}/rest/2.0/ocr/v1/business_license`,
        'POST',
        formData.toString()
      );
    } catch (error) {
      console.error('[OCR Service] 营业执照识别失败:', error);
      throw error;
    }
  }

  // 银行卡识别
  async recognizeBankCard(image: string): Promise<OCRResponse<BankCardResult>> {
    const accessToken = await this.ensureAccessToken();
    try {
      console.log('[OCR Service] 开始识别银行卡');
      
      // 构建请求参数
      const formData = new URLSearchParams();
      formData.append('access_token', accessToken);
      formData.append('image', image);

      return await ocrRequest<OCRResponse<BankCardResult>>(
        `${this.baseUrl}/rest/2.0/ocr/v1/bankcard`,
        'POST',
        formData.toString()
      );
    } catch (error) {
      console.error('[OCR Service] 银行卡识别失败:', error);
      throw error;
    }
  }

  // 开户证明
  async recognizeAccountOpening(image: string): Promise<OCRResponse<BankCardResult>> {
    const accessToken = await this.ensureAccessToken();
    try {
      console.log('[OCR Service] 开始识别开户证明');
      
      // 构建请求参数
      const formData = new URLSearchParams();
      formData.append('access_token', accessToken);
      formData.append('image', image);

      return await ocrRequest<OCRResponse<BankCardResult>>(
        `${this.baseUrl}/rest/2.0/ocr/v1/account_opening`,
        'POST',
        formData.toString()
      );
    } catch (error) {
      console.error('[OCR Service] 开户证明识别失败:', error);
      throw error;
    }
  }
}

// 创建OCR服务实例
const ocrService = new OCRService(
  'OD4oT0bT7Co1FswNN8uuJ0t5',  // API Key
  'wM3KAK3l66MgOtA1YesoUx9MdzHQ7TGN'  // Secret Key
);

export default ocrService; 