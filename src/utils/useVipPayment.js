import { useState, useEffect, useCallback } from "react";
import {
  handlePaymentResult,
  registerPaymentCallbacks,
  getPaymentEnvironment,
  PAYMENT_PROVIDERS,
  PAYMENT_STATUS,
} from "@/utils/paymentService";
import {
  createVipOrder,
  getPayOrder,
  submitPayOrder,
} from "@/utils/api/common/common_user";
import Taro from "@tarojs/taro";

/**
 * @description 用于处理VIP会员支付流程的React Hook
 * 支持完整的支付流程：创建订单 -> 获取支付信息 -> 提交支付 -> 调起支付 -> 处理回调
 * @param {{onSuccess?: Function, onFailure?: Function, onCancel?: Function, debug?: boolean}} options
 */
export function useVipPayment({
  onSuccess = () => {},
  onFailure = () => {},
  onCancel = () => {},
  debug = false,
} = {}) {
  const [isPaying, setIsPaying] = useState(false);
  const [error, setError] = useState(null);
  const [paymentStep, setPaymentStep] = useState(""); // 当前支付步骤
  const [paymentData, setPaymentData] = useState(null); // 支付相关数据

  // 调试日志函数
  const debugLog = useCallback(
    (message, data = {}) => {
      if (debug) {
        console.log(`[VIP支付] ${message}`, data);
      }
    },
    [debug]
  );

  // 注册和清理全局回调
  useEffect(() => {
    const cleanup = registerPaymentCallbacks({
      onSuccess: () => {
        debugLog("支付成功回调");
        setIsPaying(false);
        setPaymentStep("success");
        setError(null);
        onSuccess(paymentData);
      },
      onFailure: (errorMessage) => {
        debugLog("支付失败回调", { errorMessage });
        setIsPaying(false);
        setPaymentStep("failed");
        setError(errorMessage);
        onFailure(errorMessage);
      },
      onCancel: () => {
        debugLog("支付取消回调");
        setIsPaying(false);
        setPaymentStep("cancelled");
        setError(null);
        onCancel();
      },
    });

    return cleanup; // 在组件卸载时执行清理
  }, [onSuccess, onFailure, onCancel, paymentData, debugLog]);

  // 创建Promise化的API请求函数
  const createOrderPromise = useCallback(
    (vipType, price, userId) => {
      return new Promise((resolve, reject) => {
        setPaymentStep("creating_order");
        debugLog("步骤1：创建会员订单");

        createVipOrder({
          menberType: vipType,
          userId: userId,
          price: price,
          payStatus: 0,
        })
          .then((response) => {
            if (response.code !== 0) {
              reject(new Error(response.msg || "创建订单失败"));
              return;
            }

            const payOrderId = response.data;
            if (!payOrderId) {
              reject(new Error("获取支付单ID失败"));
              return;
            }

            debugLog("步骤1完成：获取到支付单ID", { payOrderId });
            resolve(payOrderId);
          })
          .catch((error) => {
            debugLog("创建订单失败", { error });
            reject(error);
          });
      });
    },
    [debugLog]
  );

  const getPayOrderPromise = useCallback(
    (payOrderId) => {
      return new Promise((resolve, reject) => {
        setPaymentStep("getting_pay_info");
        debugLog("步骤2：获取支付订单信息");

        getPayOrder({ id: payOrderId })
          .then((response) => {
            if (response.code !== 0) {
              reject(new Error(response.msg || "获取支付信息失败"));
              return;
            }

            const payOrderInfo = response.data;
            if (!payOrderInfo) {
              reject(new Error("支付订单信息为空"));
              return;
            }

            debugLog("步骤2完成：获取到支付订单信息", { payOrderInfo });
            resolve(payOrderInfo);
          })
          .catch((error) => {
            debugLog("获取支付信息失败", { error });
            reject(error);
          });
      });
    },
    [debugLog]
  );

  const submitPayOrderPromise = useCallback(
    (payOrderInfo, channelCode = PAYMENT_PROVIDERS.WECHAT) => {
      return new Promise((resolve, reject) => {
        setPaymentStep("submitting_payment");
        debugLog("步骤3：提交支付订单");

        submitPayOrder({
          id: payOrderInfo.id,
          channelCode: channelCode,
          displayMode: "app",
          returnUrl: payOrderInfo.notifyUrl
        })
          .then((response) => {
            if (response.code !== 0) {
              reject(new Error(response.msg || "提交支付失败"));
              return;
            }

            const payResult = response.data;
            debugLog("步骤3完成：支付提交成功", { payResult });
            resolve(payResult);
          })
          .catch((error) => {
            debugLog("提交支付失败", { error });
            reject(error);
          });
      });
    },
    [debugLog]
  );

  const invokePaymentPromise = useCallback(
    (payResult, channelCode) => {
      return new Promise((resolve, reject) => {
        setPaymentStep("invoking_payment");
        debugLog("步骤4：处理支付结果并调起支付");

        handlePaymentResult(payResult, channelCode)
          .then((result) => {
            debugLog("支付调起成功，等待用户操作");
            setPaymentStep("waiting_result");
            resolve(result);
          })
          .catch((error) => {
            debugLog("调起支付失败", { error });
            reject(error);
          });
      });
    },
    [debugLog]
  );

  // 发起VIP支付的完整流程（Promise链式版本）
  const initiateVipPayment = useCallback(
    (vipType, price, channelCode = PAYMENT_PROVIDERS.WECHAT) => {
      setIsPaying(true);
      setError(null);
      setPaymentStep("starting");

      debugLog("开始VIP支付流程", { vipType, price, channelCode });

      // 获取用户信息
      const localUserInfo = Taro.getStorageSync("userInfo");
      if (!localUserInfo || !localUserInfo.id) {
        const error = new Error("请先登录");
        setError(error.message);
        setIsPaying(false);
        setPaymentStep("failed");
        onFailure(error.message);
        return Promise.reject(error);
      }

      // Promise链式调用
      return createOrderPromise(vipType, price, localUserInfo.id)
        .then((payOrderId) => {
          return getPayOrderPromise(payOrderId).then((payOrderInfo) => ({
            payOrderId,
            payOrderInfo,
          }));
        })
        .then(({ payOrderId, payOrderInfo }) => {
          return submitPayOrderPromise(payOrderInfo, channelCode).then(
            (payResult) => ({ payOrderId, payOrderInfo, payResult })
          );
        })
        .then(({ payOrderId, payOrderInfo, payResult }) => {
          // 保存支付数据供回调使用
          const paymentData = {
            vipType,
            price,
            payOrderId,
            payOrderInfo,
            payResult,
            channelCode,
          };
          setPaymentData(paymentData);

          return invokePaymentPromise(payResult, channelCode).then((invokeResult) => ({
            ...paymentData,
            invokeResult,
          }));
        })
        .catch((error) => {
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          debugLog("支付流程出错", { error: errorMessage, step: paymentStep });
          setError(errorMessage);
          setIsPaying(false);
          setPaymentStep("failed");
          onFailure(errorMessage);
          throw error; // 重新抛出错误供外部处理
        });
    },
    [
      createOrderPromise,
      getPayOrderPromise,
      submitPayOrderPromise,
      invokePaymentPromise,
      onFailure,
      paymentStep,
      debugLog,
    ]
  );

  // 获取支付环境信息
  const paymentEnvironment = getPaymentEnvironment();

  // 重置支付状态
  const resetPaymentState = useCallback(() => {
    setIsPaying(false);
    setError(null);
    setPaymentStep("");
    setPaymentData(null);
    debugLog("支付状态已重置");
  }, [debugLog]);

  return {
    // 状态
    isPaying,
    error,
    paymentStep,
    paymentData,
    paymentEnvironment,

    // 主要方法
    initiateVipPayment,
    resetPaymentState,
  };
}
