/**
 * @description 键盘兼容性工具
 * 解决原生App环境中键盘相关方法缺失的问题
 */

// 键盘方法类型定义
interface KeyboardMethods {
  webShowKeyBoard?: () => void;
  webHideKeyBoard?: () => void;
  showKeyBoard?: () => void;
  hideKeyBoard?: () => void;
}

// 扩展 Window 接口
declare global {
  interface Window extends KeyboardMethods {}
}

/**
 * 键盘兼容性管理器
 */
export class KeyboardCompatibility {
  private static instance: KeyboardCompatibility;
  private isInitialized = false;
  private debugMode = false;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): KeyboardCompatibility {
    if (!KeyboardCompatibility.instance) {
      KeyboardCompatibility.instance = new KeyboardCompatibility();
    }
    return KeyboardCompatibility.instance;
  }

  /**
   * 初始化键盘兼容性
   * @param debugMode 是否启用调试模式
   */
  public init(debugMode = false): void {
    if (this.isInitialized) {
      return;
    }

    this.debugMode = debugMode;
    this.setupKeyboardMethods();
    this.setupErrorHandling();
    this.logKeyboardStatus();
    
    this.isInitialized = true;
    this.log('Keyboard compatibility initialized');
  }

  /**
   * 设置键盘相关方法
   */
  private setupKeyboardMethods(): void {
    const _win = window as any;

    // webShowKeyBoard - 显示键盘（原生App方法）
    if (!_win.webShowKeyBoard) {
      _win.webShowKeyBoard = () => {
        this.log('webShowKeyBoard called (compatibility mode)');
        this.handleShowKeyboard();
      };
    }

    // webHideKeyBoard - 隐藏键盘（原生App方法）
    if (!_win.webHideKeyBoard) {
      _win.webHideKeyBoard = () => {
        this.log('webHideKeyBoard called (compatibility mode)');
        this.handleHideKeyboard();
      };
    }

    // showKeyBoard - 显示键盘（封装方法）
    if (!_win.showKeyBoard) {
      _win.showKeyBoard = () => {
        this.log('showKeyBoard called');
        if (typeof _win.webShowKeyBoard === 'function') {
          _win.webShowKeyBoard();
        } else {
          this.handleShowKeyboard();
        }
      };
    }

    // hideKeyBoard - 隐藏键盘（封装方法）
    if (!_win.hideKeyBoard) {
      _win.hideKeyBoard = () => {
        this.log('hideKeyBoard called');
        if (typeof _win.webHideKeyBoard === 'function') {
          _win.webHideKeyBoard();
        } else {
          this.handleHideKeyboard();
        }
      };
    }
  }

  /**
   * 处理显示键盘
   */
  private handleShowKeyboard(): void {
    this.log('Handling show keyboard');
    
    // 在H5环境中，可以尝试聚焦到当前活动的输入框
    const activeElement = document.activeElement;
    if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
      (activeElement as HTMLInputElement | HTMLTextAreaElement).focus();
      this.log('Focused on active input element');
    } else {
      // 查找页面中的输入框并聚焦
      const inputs = document.querySelectorAll('input, textarea');
      if (inputs.length > 0) {
        const firstInput = inputs[0] as HTMLInputElement | HTMLTextAreaElement;
        firstInput.focus();
        this.log('Focused on first input element');
      }
    }
  }

  /**
   * 处理隐藏键盘
   */
  private handleHideKeyboard(): void {
    this.log('Handling hide keyboard');
    
    // 在H5环境中，移除焦点来隐藏键盘
    const activeElement = document.activeElement;
    if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
      (activeElement as HTMLInputElement | HTMLTextAreaElement).blur();
      this.log('Blurred active input element');
    }
  }

  /**
   * 设置错误处理
   */
  private setupErrorHandling(): void {
    const originalErrorHandler = window.onerror;
    
    window.onerror = (message, source, lineno, colno, error) => {
      if (typeof message === 'string' && this.isKeyboardError(message)) {
        this.handleKeyboardError(message, source, lineno, colno, error);
        return true; // 阻止默认错误处理
      }
      
      // 调用原始错误处理器
      if (originalErrorHandler) {
        return originalErrorHandler(message, source, lineno, colno, error);
      }
      
      return false;
    };
  }

  /**
   * 检查是否是键盘相关错误
   */
  private isKeyboardError(message: string): boolean {
    const keyboardErrorPatterns = [
      'webShowKeyBoard',
      'webHideKeyBoard',
      'showKeyBoard',
      'hideKeyBoard'
    ];
    
    return keyboardErrorPatterns.some(pattern => message.includes(pattern));
  }

  /**
   * 处理键盘相关错误
   */
  private handleKeyboardError(
    message: string, 
    source?: string, 
    lineno?: number, 
    colno?: number, 
    error?: Error
  ): void {
    this.log('Keyboard error caught and handled:', {
      message,
      source,
      lineno,
      colno,
      error: error?.stack
    });

    // 重新初始化键盘方法
    this.setupKeyboardMethods();
  }

  /**
   * 记录键盘状态
   */
  private logKeyboardStatus(): void {
    if (!this.debugMode) return;

    const _win = window as any;
    const status = {
      webShowKeyBoard: typeof _win.webShowKeyBoard,
      webHideKeyBoard: typeof _win.webHideKeyBoard,
      showKeyBoard: typeof _win.showKeyBoard,
      hideKeyBoard: typeof _win.hideKeyBoard,
    };

    console.group('🎹 Keyboard Compatibility Status');
    console.table(status);
    
    // 列出所有键盘相关方法
    const keyboardMethods = Object.keys(window).filter(key => 
      key.toLowerCase().includes('keyboard') || 
      key.toLowerCase().includes('keyb')
    );
    console.log('Available keyboard methods:', keyboardMethods);
    
    console.groupEnd();
  }

  /**
   * 调试日志
   */
  private log(message: string, data?: any): void {
    if (this.debugMode) {
      if (data) {
        console.log(`[KeyboardCompatibility] ${message}`, data);
      } else {
        console.log(`[KeyboardCompatibility] ${message}`);
      }
    }
  }

  /**
   * 检查键盘方法是否可用
   */
  public checkKeyboardMethods(): { [key: string]: boolean } {
    const _win = window as any;
    return {
      webShowKeyBoard: typeof _win.webShowKeyBoard === 'function',
      webHideKeyBoard: typeof _win.webHideKeyBoard === 'function',
      showKeyBoard: typeof _win.showKeyBoard === 'function',
      hideKeyBoard: typeof _win.hideKeyBoard === 'function',
    };
  }

  /**
   * 安全调用显示键盘
   */
  public safeShowKeyboard(): void {
    const _win = window as any;
    try {
      if (typeof _win.showKeyBoard === 'function') {
        _win.showKeyBoard();
      } else if (typeof _win.webShowKeyBoard === 'function') {
        _win.webShowKeyBoard();
      } else {
        this.handleShowKeyboard();
      }
    } catch (error) {
      this.log('Error calling show keyboard:', error);
      this.handleShowKeyboard();
    }
  }

  /**
   * 安全调用隐藏键盘
   */
  public safeHideKeyboard(): void {
    const _win = window as any;
    try {
      if (typeof _win.hideKeyBoard === 'function') {
        _win.hideKeyBoard();
      } else if (typeof _win.webHideKeyBoard === 'function') {
        _win.webHideKeyBoard();
      } else {
        this.handleHideKeyboard();
      }
    } catch (error) {
      this.log('Error calling hide keyboard:', error);
      this.handleHideKeyboard();
    }
  }

  /**
   * 获取初始化状态
   */
  public isReady(): boolean {
    return this.isInitialized;
  }
}

// 导出单例实例
export const keyboardCompatibility = KeyboardCompatibility.getInstance();

// 默认导出
export default keyboardCompatibility;
