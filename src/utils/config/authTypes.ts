
export const AuthTypes = {
    CAMERA: 1, // 相机权限（拍照 录像）
    GALLERY_PHOTO: 2, // 图片权限
    GALLERY_VIDEO: 3, // 视频权限
    GALLERY_AUDIO: 4, // 音频权限
    STORAGE: 5, // 存储权限（保存图片）
    AUDIO: 6, // 麦克风权限（录音）
    NOTICE: 7, // 通知权限
    LOCATION: 8, // 定位权限
    CONTACT: 9, // 通讯录权限
    FLOATWIN: 10, // 悬浮窗权限
    ACCESS: 11, // 无障碍服务权限
    AUTOSTART: 12, // 自启动权限
    LOCALNET: 13, // iOS本地网络权限
  };
  
  export const AuthStatusIos = {
    NOTDETERMINED: 0, // 用户尚未决定是否授予权限  首次
    RESTRICTED: 1, // 用户没有权限 不清楚什么时候是这个情况
    DENIED: 2, // 用户已经拒绝权限  拒绝跟开启之后再手动关闭
    AUTHORIZED: 3, // 用户已经授予了权限  
    LIMIT: 4, // 授予了部分权限   允许一次 变成4  再进来就变成0了
  };


  export const AuthStatusAndroid = {
    DENIED: 0, // 用户没有权限 没有点拒绝且不再询问
    DENIEDANDNOTASK: 1, // 用户拒绝权限并且不再询问
    AUTHORIZED: 2, // 用户已经授予了权限  
  };
  