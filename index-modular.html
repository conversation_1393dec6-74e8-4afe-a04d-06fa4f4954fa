<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>yk-common 独立测试环境 (模块化版本)</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; text-align: center; border-radius: 8px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        button { background: #3498db; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #2980b9; }
        button.success { background: #27ae60; }
        button.warning { background: #f39c12; }
        .output { background: #2c3e50; color: #ecf0f1; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }
        .info { background: #d4edda; border: 1px solid #28a745; padding: 15px; border-radius: 4px; margin: 15px 0; }
        .feature { background: #e3f2fd; border: 1px solid #2196f3; padding: 15px; border-radius: 4px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 yk-common 独立测试环境</h1>
        <p>模块化版本 - 使用ES模块导入</p>
    </div>
    
    <div class="info">
        <h4>📍 独立测试环境</h4>
        <p><strong>当前位置：</strong>/mnt/d/source/gitlab/yk-common-standalone-test/</p>
        <p><strong>独立性：</strong>完全独立于arcoxc项目，与arcoxc项目同级目录</p>
        <p><strong>安全性：</strong>不会修改arcoxc项目的任何文件</p>
    </div>
    
    <div class="feature">
        <h4>🚀 模块化优势</h4>
        <ul>
            <li>使用ES模块导入，代码结构更清晰</li>
            <li>函数复用性更好，易于维护</li>
            <li>新增工具函数：getMerchantTypeCode, validateBusinessRequestNo</li>
            <li>支持现代JavaScript模块系统</li>
        </ul>
    </div>
    
    <div class="section">
        <h3>🚀 快速测试</h3>
        <button onclick="runDemo()">快速演示</button>
        <button onclick="runTests()" class="success">运行测试</button>
        <button onclick="runValidationDemo()" class="warning">验证演示</button>
        <button onclick="clearOutput()">清空输出</button>
    </div>
    
    <div class="section">
        <h3>💻 测试输出</h3>
        <div class="output" id="output">点击上方按钮开始测试...</div>
    </div>
    
    <script type="module">
        // 使用ES模块导入
        import {
            BusinessType,
            MerchantType,
            generateBusinessRequestNo,
            generateOutRequestNo,
            getMerchantTypeCode,
            validateBusinessRequestNo
        } from './yk-common.mjs';
        
        function appendOutput(text) {
            const output = document.getElementById('output');
            output.textContent += text + '\n';
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('output').textContent = '';
        }
        
        function runDemo() {
            clearOutput();
            appendOutput('🎯 yk-common 业务编号生成演示 (模块化版本)');
            appendOutput('📍 位置: /mnt/d/source/gitlab/yk-common-standalone-test/');
            appendOutput('🔒 完全独立于arcoxc项目');
            appendOutput('📦 使用ES模块导入\n');
            
            appendOutput('1. 基础用法:');
            const result1 = generateBusinessRequestNo(BusinessType.PAYMENT, 12345);
            appendOutput('generateBusinessRequestNo(BusinessType.PAYMENT, 12345)');
            appendOutput('结果: ' + result1);
            
            appendOutput('\n2. 完整参数:');
            const result2 = generateBusinessRequestNo(BusinessType.APPLYMENT, 67890, MerchantType.ENTERPRISE, 'BATCH01');
            appendOutput('generateBusinessRequestNo(BusinessType.APPLYMENT, 67890, MerchantType.ENTERPRISE, "BATCH01")');
            appendOutput('结果: ' + result2);
            
            appendOutput('\n3. 商户进件申请:');
            const result3 = generateOutRequestNo(MerchantType.INDIVIDUAL, 11111);
            appendOutput('generateOutRequestNo(MerchantType.INDIVIDUAL, 11111)');
            appendOutput('结果: ' + result3);
            
            appendOutput('\n4. 商户类型简码:');
            Object.entries(MerchantType).forEach(([name, value]) => {
                const code = getMerchantTypeCode(value);
                appendOutput(`${name} (${value}): ${code}`);
            });
            
            appendOutput('\n✨ 演示完成！');
        }
        
        function runTests() {
            clearOutput();
            appendOutput('🚀 开始运行基础测试 (模块化版本)\n');
            
            let passed = 0;
            let failed = 0;
            
            // 测试1: 基础功能
            try {
                const result = generateBusinessRequestNo(BusinessType.PAYMENT, 12345);
                if (result.startsWith('PAYMENT_') && result.includes('_12345_')) {
                    appendOutput('✅ 基础功能测试通过');
                    passed++;
                } else {
                    appendOutput('❌ 基础功能测试失败');
                    failed++;
                }
            } catch (error) {
                appendOutput('❌ 基础功能测试错误: ' + error.message);
                failed++;
            }
            
            // 测试2: 完整参数
            try {
                const result = generateBusinessRequestNo(BusinessType.APPLYMENT, 67890, MerchantType.ENTERPRISE, 'BATCH01');
                if (result.includes('APPLYMENT_') && result.includes('_ENT_') && result.includes('_67890_') && result.includes('_BATCH01_')) {
                    appendOutput('✅ 完整参数测试通过');
                    passed++;
                } else {
                    appendOutput('❌ 完整参数测试失败');
                    failed++;
                }
            } catch (error) {
                appendOutput('❌ 完整参数测试错误: ' + error.message);
                failed++;
            }
            
            // 测试3: 商户进件申请
            try {
                const result = generateOutRequestNo(MerchantType.INDIVIDUAL, 11111);
                if (result.startsWith('APPLYMENT_') && result.includes('_IND_') && result.includes('_11111_')) {
                    appendOutput('✅ 商户进件申请测试通过');
                    passed++;
                } else {
                    appendOutput('❌ 商户进件申请测试失败');
                    failed++;
                }
            } catch (error) {
                appendOutput('❌ 商户进件申请测试错误: ' + error.message);
                failed++;
            }
            
            // 测试4: 工具函数
            try {
                const code = getMerchantTypeCode(MerchantType.ENTERPRISE);
                if (code === 'ENT') {
                    appendOutput('✅ 工具函数测试通过');
                    passed++;
                } else {
                    appendOutput('❌ 工具函数测试失败');
                    failed++;
                }
            } catch (error) {
                appendOutput('❌ 工具函数测试错误: ' + error.message);
                failed++;
            }
            
            const total = passed + failed;
            appendOutput('\n📊 测试结果汇总:');
            appendOutput('总测试数: ' + total);
            appendOutput('通过数: ' + passed);
            appendOutput('失败数: ' + failed);
            appendOutput('通过率: ' + ((passed / total) * 100).toFixed(1) + '%');
            
            if (failed === 0) {
                appendOutput('\n🎉 所有测试通过！');
            } else {
                appendOutput('\n⚠️ 部分测试失败');
            }
        }
        
        function runValidationDemo() {
            clearOutput();
            appendOutput('🔍 编号格式验证演示\n');
            
            // 生成一些测试编号
            const validNumbers = [
                generateBusinessRequestNo(BusinessType.PAYMENT, 12345),
                generateBusinessRequestNo(BusinessType.APPLYMENT, 67890, MerchantType.ENTERPRISE, 'BATCH01'),
                generateOutRequestNo(MerchantType.INDIVIDUAL, 11111)
            ];
            
            const invalidNumbers = [
                'INVALID_FORMAT',
                'PAYMENT_123_456',
                'UNKNOWN_TYPE_20250701143000_12345_123456',
                ''
            ];
            
            appendOutput('有效编号验证:');
            validNumbers.forEach((testNo, index) => {
                const validation = validateBusinessRequestNo(testNo);
                appendOutput(`${index + 1}. ${validation.valid ? '✅' : '❌'} ${testNo}`);
                if (validation.valid) {
                    const parts = validation.parts;
                    appendOutput(`   业务类型: ${parts.businessType}`);
                    appendOutput(`   时间戳: ${parts.timestamp}`);
                    appendOutput(`   商户类型: ${parts.merchantType || '无'}`);
                    appendOutput(`   用户ID: ${parts.userId}`);
                    appendOutput(`   自定义后缀: ${parts.customSuffix || '无'}`);
                    appendOutput(`   随机数: ${parts.randomNum}`);
                }
                appendOutput('');
            });
            
            appendOutput('无效编号验证:');
            invalidNumbers.forEach((testNo, index) => {
                const validation = validateBusinessRequestNo(testNo);
                appendOutput(`${index + 1}. ${validation.valid ? '✅' : '❌'} ${testNo || '(空字符串)'}`);
                if (!validation.valid) {
                    appendOutput(`   错误: ${validation.error}`);
                }
                appendOutput('');
            });
            
            appendOutput('🔍 验证演示完成！');
        }
        
        // 添加到全局对象，方便控制台调用
        window.generateBusinessRequestNo = generateBusinessRequestNo;
        window.generateOutRequestNo = generateOutRequestNo;
        window.getMerchantTypeCode = getMerchantTypeCode;
        window.validateBusinessRequestNo = validateBusinessRequestNo;
        window.BusinessType = BusinessType;
        window.MerchantType = MerchantType;
        window.runDemo = runDemo;
        window.runTests = runTests;
        window.runValidationDemo = runValidationDemo;
        window.clearOutput = clearOutput;
        
        // 页面加载完成后的提示
        console.log('🔧 yk-common 独立测试环境已加载 (模块化版本)');
        console.log('📍 位置: /mnt/d/source/gitlab/yk-common-standalone-test/');
        console.log('🔒 完全独立于arcoxc项目');
        console.log('📦 使用ES模块导入，代码结构更清晰');
        console.log('💡 可以在控制台中调用函数进行测试');
        console.log('🆕 新增工具函数: getMerchantTypeCode, validateBusinessRequestNo');
    </script>
</body>
</html>
